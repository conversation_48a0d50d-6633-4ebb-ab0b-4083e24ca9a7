// utils/nativeRazorpay.ts
import {
  Platform,
  NativeModules,
  PermissionsAndroid,
  Linking,
} from "react-native";
import { RazorpayOptions, RazorpaySuccessResponse } from "@/types/payment";
import { Alert } from "react-native";
import axios from "axios";
import Constants from "expo-constants";
import { getAppVersion, getPlatformInfo } from "./paymentConfig";

// Import the Razorpay SDK with enhanced error handling
let RazorpayCheckout: any = null;
try {
  console.log("Attempting to import react-native-razorpay...");

  // First try to import using the standard method
  try {
    RazorpayCheckout = require("react-native-razorpay").default;
    console.log(
      "react-native-razorpay imported via default export:",
      !!RazorpayCheckout
    );
  } catch (importError) {
    console.error(
      "Error importing react-native-razorpay default:",
      importError
    );
  }

  // Verify that the module is properly loaded
  if (!RazorpayCheckout) {
    console.warn(
      "Razorpay default import is null, trying to access via NativeModules"
    );
    // Try to access via NativeModules as a fallback
    try {
      RazorpayCheckout = NativeModules.RazorpayCheckout;
      console.log(
        "react-native-razorpay accessed via NativeModules:",
        !!RazorpayCheckout
      );
    } catch (nativeModuleError) {
      console.error(
        "Error accessing RazorpayCheckout via NativeModules:",
        nativeModuleError
      );
    }
  }

  if (RazorpayCheckout) {
    console.log("✅ Razorpay SDK successfully initialized");
    // Log available methods for debugging
    if (typeof RazorpayCheckout === "object") {
      console.log("Available Razorpay methods:", Object.keys(RazorpayCheckout));
    }
  } else {
    console.error("❌ Failed to initialize Razorpay SDK");
  }
} catch (error) {
  console.error("Failed to import react-native-razorpay:", error);
}

/**
 * Handles payment using the native Razorpay SDK
 *
 * @param options The Razorpay payment options
 * @param onSuccess Callback for successful payment
 * @param onError Callback for payment errors
 * @returns A boolean indicating if the payment was initiated successfully
 */
export const processRazorpayPayment = async (
  options: RazorpayOptions,
  onSuccess: (response: RazorpaySuccessResponse) => void,
  onError: (error: string) => void
): Promise<boolean> => {
  // Enhanced check for Razorpay SDK availability
  const isAvailable = await isRazorpayAvailable();
  if (!isAvailable) {
    console.error("Razorpay SDK is not available");
    onError("Payment gateway not available. Please try again later.");

    // Offer to open Razorpay support for troubleshooting
    Alert.alert(
      "Payment Gateway Issue",
      "Would you like to visit Razorpay support for troubleshooting?",
      [
        { text: "No", style: "cancel" },
        { text: "Yes", onPress: openRazorpaySupport },
      ]
    );

    return false;
  }

  try {
    // Ensure key is present and valid
    if (
      !options.key ||
      typeof options.key !== "string" ||
      options.key.trim() === ""
    ) {
      console.error("Invalid Razorpay key:", options.key);
      onError("Invalid payment configuration. Please contact support.");
      return false;
    }

    // Validate key format (should start with rzp_test_ or rzp_live_)
    const keyPattern = /^rzp_(test|live)_[a-zA-Z0-9]+$/;
    if (!keyPattern.test(options.key)) {
      console.error("Razorpay key format appears invalid:", options.key);
      onError("Invalid payment gateway configuration. Please contact support.");
      return false;
    }

    // Verify the key with Razorpay API (optional)
    try {
      console.log("Attempting to verify Razorpay key (optional)");
      const isKeyValid = await verifyRazorpayKey(options.key);
      if (!isKeyValid) {
        console.warn("Razorpay key verification failed, but continuing anyway");
        // We'll continue anyway since this is just an additional check
      } else {
        console.log("Razorpay key verification passed");
      }
    } catch (keyVerifyError) {
      console.error("Error during Razorpay key verification:", keyVerifyError);
      // Continue anyway, as this is just an additional check
    }

    console.log("Razorpay key validation passed");

    // Ensure order_id is present
    if (
      !options.order_id ||
      typeof options.order_id !== "string" ||
      options.order_id.trim() === ""
    ) {
      console.error("Invalid or missing order_id:", options.order_id);
      onError("Invalid order information. Please try again.");
      return false;
    }

    // Get platform info for better tracking
    const platformInfo = getPlatformInfo();

    // Convert our options format to the format expected by the native SDK
    const razorpayOptions = {
      ...options,
      // Convert amount to string as required by the native SDK
      amount: options.amount.toString(),
      // Add additional options for better stability
      send_sms_hash: true,
      display_logo: true,
      remember_customer: true,
      readonly: {
        email: false,
        contact: false,
        name: false,
      },
      // Add a description for the payment
      description: options.description || "Purchase",
      // Add image if needed
      // image: 'https://your-app-logo-url.png',
      // Add notes for better tracking
      notes: {
        ...(options.notes || {}),
        platform: platformInfo.platform,
        app_version: platformInfo.appVersion,
        device_version: platformInfo.version,
      },
      // Ensure theme is properly set
      theme: options.theme || { color: "#1E2A47" },
    };

    // Log the options for debugging
    console.log(
      "Razorpay payment options:",
      JSON.stringify(razorpayOptions, null, 2)
    );

    // Add additional logging before opening Razorpay
    console.log("About to open Razorpay with options:", {
      key: razorpayOptions.key,
      order_id: razorpayOptions.order_id,
      amount: razorpayOptions.amount,
      currency: razorpayOptions.currency,
      // Don't log the entire object as it may contain sensitive information
    });

    try {
      // Wrap the open call in a try-catch for immediate error handling
      // Process the payment using the native SDK with enhanced error handling
      RazorpayCheckout.open(razorpayOptions)
        .then((data: RazorpaySuccessResponse) => {
          console.log("Payment success:", data);
          onSuccess(data);
        })
        .catch((error: any) => {
          // Log the complete error object for debugging
          console.error("Razorpay error:", JSON.stringify(error, null, 2));

          // Try to get more information about the error
          if (error.error && typeof error.error === "object") {
            console.error(
              "Detailed error info:",
              JSON.stringify(error.error, null, 2)
            );
          }

          // Handle different error scenarios
          let errorMessage = "Payment failed. Please try again.";

          if (error.code) {
            // Handle specific error codes
            console.log("Error code detected:", error.code);
            switch (error.code) {
              case "BAD_REQUEST_ERROR":
                errorMessage =
                  "Invalid payment details. Please check and try again.";
                break;
              case "NETWORK_ERROR":
                errorMessage =
                  "Network error. Please check your internet connection.";
                break;
              case "INTERNAL_SERVER_ERROR":
                errorMessage = "Server error. Please try again later.";
                break;
              case "GATEWAY_ERROR":
                errorMessage = "Payment gateway error. Please try again.";
                break;
              case "PAYMENT_CANCELED":
                errorMessage = "Payment was canceled.";
                break;
              default:
                // Use the error description if available
                errorMessage = error.description || errorMessage;
                console.log(
                  "Using default error message for code:",
                  error.code
                );
            }
          } else if (error.error && error.error.description) {
            // Some versions of the SDK return error in a nested object
            errorMessage = error.error.description;
            console.log("Using nested error description:", errorMessage);
          } else if (typeof error === "string") {
            // Handle case where error is a string
            errorMessage = error;
            console.log("Error is a string:", errorMessage);
          } else if (error.message) {
            // Handle case where error is an Error object
            errorMessage = error.message;
            console.log("Using error.message:", errorMessage);
          }

          // Check for the specific error message you're seeing
          if (
            errorMessage.includes("Something went wrong") ||
            error.toString().includes("Something went wrong")
          ) {
            console.error(
              "Detected the 'Something went wrong' error - this is often related to API key issues or server configuration"
            );
            errorMessage =
              "The payment service is currently unavailable. Please try again later or contact support.";
          }

          onError(errorMessage);
        });
    } catch (immediateError: any) {
      // This will catch synchronous errors that happen immediately when calling open()
      console.error("Immediate error when opening Razorpay:", immediateError);
      onError(
        "Failed to launch payment gateway: " +
          (immediateError.message || "Unknown error")
      );
    }

    return true;
  } catch (error: any) {
    // Enhanced error logging
    console.error("Error initiating Razorpay payment:", error);
    console.error("Error details:", error.message || error);

    // Provide more specific error message if possible
    let errorMessage = "Failed to initialize payment. Please try again.";
    if (error.message && error.message.includes("not a function")) {
      errorMessage =
        "Payment gateway initialization failed. Please try again later.";
    }

    onError(errorMessage);
    return false;
  }
};

/**
 * Check for device compatibility issues that might affect Razorpay
 */
export const checkDeviceCompatibility = async (): Promise<boolean> => {
  try {
    // Check platform-specific issues
    if (Platform.OS === "android") {
      // Check Android version
      const androidVersion = parseInt(Platform.Version.toString(), 10);
      console.log("Android version:", androidVersion);

      if (androidVersion < 21) {
        // Android 5.0 Lollipop
        Alert.alert(
          "Device Compatibility Issue",
          "Your Android version may not be compatible with the payment gateway. Please try a different payment method or update your device.",
          [{ text: "OK" }]
        );
        return false;
      }

      // Check for necessary permissions on Android
      try {
        // These permissions might be needed for some payment flows
        const hasStoragePermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE
        );

        console.log("Storage permission status:", hasStoragePermission);

        if (!hasStoragePermission) {
          console.log("Requesting storage permission");
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
            {
              title: "Storage Permission",
              message:
                "This app needs access to your storage to process payments",
              buttonNeutral: "Ask Me Later",
              buttonNegative: "Cancel",
              buttonPositive: "OK",
            }
          );

          if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
            console.log("Storage permission denied");
            // We can still try to proceed, but log this as a potential issue
          }
        }
      } catch (permError) {
        console.error("Error checking permissions:", permError);
        // Continue anyway, as this is just a precautionary check
      }
    }

    return true;
  } catch (error) {
    console.error("Error checking device compatibility:", error);
    return true; // Continue anyway, as this is just a precautionary check
  }
};

/**
 * Checks if the Razorpay SDK is available and properly initialized on the device
 * with enhanced error reporting
 */
export const isRazorpayAvailable = async (): Promise<boolean> => {
  try {
    // For Expo, we need to check if we're running in a development build
    // as the native module won't be available in Expo Go
    console.log("Checking Expo environment...");
    console.log("Expo executionEnvironment:", Constants.executionEnvironment);
    console.log("Expo appOwnership:", (Constants as any).appOwnership);

    // Only consider it Expo Go if both conditions are met
    const isExpoGo =
      Constants.executionEnvironment === "storeClient" &&
      (Constants as any).appOwnership === "expo";

    if (isExpoGo) {
      console.warn("Running in Expo Go - native Razorpay SDK not available");
      return false;
    }

    console.log("Not running in Expo Go, continuing with native SDK check");

    // First check device compatibility
    const isDeviceCompatible = await checkDeviceCompatibility();
    if (!isDeviceCompatible) {
      console.warn("Device compatibility issues detected");
      // We still continue to check SDK availability
    }

    // Check if the SDK is imported successfully
    if (!RazorpayCheckout) {
      console.error("Razorpay SDK not imported");

      // Try to access via NativeModules as a last resort
      try {
        console.log(
          "Attempting to access RazorpayCheckout directly from NativeModules..."
        );
        const { RazorpayCheckout: RazorpayNative } = NativeModules;

        if (RazorpayNative) {
          console.log(
            "Found RazorpayNative in NativeModules:",
            !!RazorpayNative
          );
          console.log("Available methods:", Object.keys(RazorpayNative));

          if (typeof RazorpayNative.open === "function") {
            console.log("✅ Found Razorpay.open method via NativeModules");
            RazorpayCheckout = RazorpayNative;
            return true;
          } else {
            console.error("❌ RazorpayNative found but missing open method");
          }
        } else {
          console.error("❌ RazorpayNative not found in NativeModules");
        }
      } catch (nativeError) {
        console.error(
          "Failed to access Razorpay via NativeModules:",
          nativeError
        );
      }

      console.error("❌ Razorpay SDK not available after all checks");
      return false;
    }

    // Check if the open method is available
    console.log("Checking if Razorpay.open method exists...");
    if (typeof RazorpayCheckout.open !== "function") {
      console.error("❌ Razorpay SDK missing open method");

      // Log available methods for debugging
      console.log("Available Razorpay methods:", Object.keys(RazorpayCheckout));
      console.log("RazorpayCheckout type:", typeof RazorpayCheckout);

      return false;
    }

    console.log("✅ Razorpay.open method exists and is a function");

    // Additional check for Android-specific issues
    if (Platform.OS === "android") {
      // On Android, sometimes the module is present but not properly linked
      try {
        // Try to access a property to verify the module is properly linked
        const checkProperty = RazorpayCheckout.hasOwnProperty("open");
        console.log("Razorpay property check:", checkProperty);
      } catch (androidError) {
        console.error("Android-specific Razorpay check failed:", androidError);
        return false;
      }
    }

    console.log("Razorpay SDK is available and properly initialized");
    return true;
  } catch (error) {
    console.error("Error checking Razorpay availability:", error);
    return false;
  }
};

/**
 * Verify if a Razorpay key is valid by making a test API request
 * @param key The Razorpay key to verify
 * @returns Promise<boolean> indicating if the key is valid
 */
export const verifyRazorpayKey = async (key: string): Promise<boolean> => {
  try {
    // Only proceed if the key format is valid
    const keyPattern = /^rzp_(test|live)_[a-zA-Z0-9]+$/;
    if (!keyPattern.test(key)) {
      console.error("Invalid Razorpay key format:", key);
      return false;
    }

    let auth;
    try {
      // Create basic auth credentials from the key using btoa instead of Buffer
      // btoa is available in React Native and works for basic ASCII strings
      auth = btoa(`${key}:`);
    } catch (encodeError) {
      console.error("Error encoding Razorpay key:", encodeError);
      // If btoa fails, we'll skip the verification but still return true
      // to allow the payment process to continue
      return true;
    }

    // Make a simple request to the Razorpay API to check if the key is valid
    const response = await axios.get("https://api.razorpay.com/v1/methods", {
      headers: {
        Authorization: `Basic ${auth}`,
      },
    });

    // If we get a 200 response, the key is valid
    console.log("Razorpay key verification response status:", response.status);
    return response.status === 200;
  } catch (error: any) {
    // Check if the error is due to an invalid key (401 Unauthorized)
    if (error.response && error.response.status === 401) {
      console.error("Razorpay key verification failed: Invalid key");
      return false;
    }

    // For other errors, log but don't fail (could be network issues)
    console.error("Error verifying Razorpay key:", error.message || error);
    // Return true to allow the process to continue
    return true;
  }
};

/**
 * Opens Razorpay support page for troubleshooting
 */
export const openRazorpaySupport = () => {
  Linking.openURL("https://razorpay.com/support/").catch((err) => {
    console.error("Failed to open Razorpay support page:", err);
    Alert.alert(
      "Couldn't Open Support Page",
      "Please visit https://razorpay.com/support/ for help with payment issues.",
      [{ text: "OK" }]
    );
  });
};
