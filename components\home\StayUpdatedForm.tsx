import React, { useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store/store';
import { subscribeNewsletter, clearNewsletterState } from '@/store/slices/contactSlice';
import { COLORS, SPACING } from '@/constants/theme';
import { ThemedText } from '@/components/common/ThemedText';
import ThemedTextInput from '@/components/common/ThemedTextInput';
import ThemedButton from '@/components/common/ThemedButton';

// Validation schema
const newsletterSchema = Yup.object().shape({
  email: Yup.string()
    .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Please enter a valid email address')
    .required('Email is required'),
});

interface NewsletterFormData {
  email: string;
}

export default function StayUpdatedForm() {
  const dispatch = useDispatch<AppDispatch>();
  const { newsletterLoading, newsletterError, newsletterSuccess } = useSelector(
    (state: RootState) => state.contact
  );

  const initialValues: NewsletterFormData = {
    email: '',
  };

  const handleSubmit = (values: NewsletterFormData, { resetForm }: any) => {
    dispatch(subscribeNewsletter(values));
  };

  useEffect(() => {
    if (newsletterSuccess) {
      Alert.alert(
        'Success',
        'Thank you for subscribing! You will receive updates on new car listings and exclusive deals.',
        [
          {
            text: 'OK',
            onPress: () => dispatch(clearNewsletterState()),
          },
        ]
      );
    }
  }, [newsletterSuccess, dispatch]);

  useEffect(() => {
    if (newsletterError) {
      Alert.alert(
        'Error',
        newsletterError,
        [
          {
            text: 'OK',
            onPress: () => dispatch(clearNewsletterState()),
          },
        ]
      );
    }
  }, [newsletterError, dispatch]);

  return (
    <View style={styles.container}>
      <Formik
        initialValues={initialValues}
        validationSchema={newsletterSchema}
        onSubmit={handleSubmit}
      >
        {({ handleChange, handleBlur, handleSubmit, values, errors, touched, resetForm }) => {
          // Reset form when success
          useEffect(() => {
            if (newsletterSuccess) {
              resetForm();
            }
          }, [newsletterSuccess, resetForm]);

          return (
            <View style={styles.form}>
              <View style={styles.inputContainer}>
                <ThemedTextInput
                  value={values.email}
                  onChangeText={handleChange('email')}
                  onBlur={handleBlur('email')}
                  placeholder="Email Address"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  style={styles.emailInput}
                  error={touched.email && errors.email}
                />
                <ThemedButton
                  onPress={handleSubmit}
                  disabled={newsletterLoading}
                  style={styles.subscribeButton}
                  textStyle={styles.subscribeButtonText}
                >
                  {newsletterLoading ? 'Subscribing...' : '📧 Subscribe to Newsletter'}
                </ThemedButton>
              </View>
            </View>
          );
        }}
      </Formik>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: SPACING.lg,
  },
  form: {
    gap: SPACING.sm,
  },
  inputContainer: {
    gap: SPACING.md,
  },
  emailInput: {
    backgroundColor: COLORS.white,
    borderColor: COLORS.border.primary,
  },
  subscribeButton: {
    backgroundColor: COLORS.secondary,
  },
  subscribeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
