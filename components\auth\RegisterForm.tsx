import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { ThemedText } from '@/components/common/ThemedText';
import ThemedInput from '@/components/common/ThemedInput';
import ThemedButton from '@/components/common/ThemedButton';
import { COLORS } from '@/constants/theme';

const registerSchema = Yup.object().shape({
    name: Yup.string()
        .min(2, 'Name must be at least 2 characters')
        .required('Name is required'),
    phone: Yup.string()
        .matches(/^[0-9]{10}$/, 'Phone number must be 10 digits')
        .required('Phone number is required'),
    password: Yup.string()
        .min(6, 'Password must be at least 6 characters')
        .required('Password is required'),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref('password')], 'Passwords must match')
        .required('Confirm password is required'),
});

export default function RegisterForm({ email, onRegisterSuccess, loading }: {
    email: string;
    onRegisterSuccess: (values: { name: string; phone: string; password: string }) => void
    loading: boolean
}) {
    return (
        <Formik
            initialValues={{
                email: email,
                name: '',
                phone: '',
                password: '',
                confirmPassword: ''
            }}
            validationSchema={registerSchema}
            onSubmit={(values) => onRegisterSuccess(values)}
            enableReinitialize // <--- Critical: Update form when email changes
        >
            {({ handleChange, handleSubmit, values, errors, touched }) => (
                <>
                    <ThemedText style={styles.title}>Create Account</ThemedText>

                    {/* Email Input (Read-only) */}
                    <ThemedInput
                        value={values.email}
                        editable={false}
                        placeholder="Email"
                        onChangeText={handleChange('email')}
                    />

                    {/* Name Input */}
                    <ThemedInput
                        value={values.name}
                        onChangeText={handleChange('name')}
                        placeholder="Full Name"
                    />
                    {touched.name && errors.name && (
                        <ThemedText style={styles.errorText}>{errors.name}</ThemedText>
                    )}

                    {/* Phone Input */}
                    <ThemedInput
                        value={values.phone}
                        onChangeText={handleChange('phone')}
                        placeholder="Phone Number"
                        keyboardType="phone-pad"
                    />
                    {touched.phone && errors.phone && (
                        <ThemedText style={styles.errorText}>{errors.phone}</ThemedText>
                    )}

                    {/* Password Input */}
                    <ThemedInput
                        value={values.password}
                        onChangeText={handleChange('password')}
                        placeholder="Password"
                        secureTextEntry
                    />
                    {touched.password && errors.password && (
                        <ThemedText style={styles.errorText}>{errors.password}</ThemedText>
                    )}

                    {/* Confirm Password Input */}
                    <ThemedInput
                        value={values.confirmPassword}
                        onChangeText={handleChange('confirmPassword')}
                        placeholder="Confirm Password"
                        secureTextEntry
                    />
                    {touched.confirmPassword && errors.confirmPassword && (
                        <ThemedText style={styles.errorText}>{errors.confirmPassword}</ThemedText>
                    )}

                    <ThemedButton
                        style={styles.button}
                        onPress={handleSubmit}
                        disabled={loading}
                    >
                        {loading ? 'Registering...' : 'Register'}
                    </ThemedButton>
                </>
            )}
        </Formik>
    );
}

const styles = StyleSheet.create({
    title: {
        fontSize: 24,
        marginBottom: 16,
        textAlign: 'center',
        color: COLORS.text.primary,
    },
    button: {
        width: '100%',
        marginTop: 8,
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginTop: -8,
        marginBottom: 8,
        marginLeft: 16,
        alignSelf: 'flex-start',
    },
});