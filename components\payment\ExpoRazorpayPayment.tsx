// components/payment/ExpoRazorpayPayment.tsx
import React, { useEffect, useState, useRef } from 'react';
import { Modal, View, StyleSheet, ActivityIndicator, Text, TouchableOpacity, BackHandler, Platform, SafeAreaView } from 'react-native';
import { WebView } from 'react-native-webview';
import { RazorpayOptions, RazorpaySuccessResponse } from '@/types/payment';
import { Ionicons } from '@expo/vector-icons';

interface ExpoRazorpayPaymentProps {
  visible: boolean;
  options: RazorpayOptions;
  onClose: () => void;
  onPaymentSuccess: (response: RazorpaySuccessResponse) => void;
  onPaymentError: (error: string) => void;
}

const ExpoRazorpayPayment = ({
  visible,
  options,
  onClose,
  onPaymentSuccess,
  onPaymentError
}: ExpoRazorpayPaymentProps) => {
  const [loading, setLoading] = useState(true);
  const [htmlContent, setHtmlContent] = useState('');
  const [webViewKey, setWebViewKey] = useState(1);
  const [hasError, setHasError] = useState(false);
  const webViewRef = useRef<WebView>(null);

  // Handle back button press
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (visible) {
        onClose();
        return true;
      }
      return false;
    });

    return () => backHandler.remove();
  }, [visible, onClose]);

  // Generate HTML content when component mounts or options change
  useEffect(() => {
    if (visible && options) {
      try {
        console.log('Generating Razorpay HTML content');

        // Validate required options before generating HTML
        if (!options.key || !options.order_id || !options.amount) {
          console.error('Missing required Razorpay options:',
            !options.key ? 'key' : !options.order_id ? 'order_id' : 'amount');
          setHasError(true);
          onPaymentError('Invalid payment configuration. Please try again.');
          return;
        }

        // Log the options for debugging (excluding sensitive data)
        console.log('Razorpay options:', {
          key: options.key.substring(0, 10) + '...',
          order_id: options.order_id,
          amount: options.amount,
          currency: options.currency
        });

        // Generate HTML content with a small delay to ensure clean state
        setTimeout(() => {
          try {
            const content = generateRazorpayHTML(options);
            setHtmlContent(content);
            setWebViewKey(prevKey => prevKey + 1);
            setHasError(false);
            console.log('Razorpay HTML content generated successfully');
          } catch (innerError) {
            console.error('Error in delayed HTML generation:', innerError);
            setHasError(true);
            onPaymentError('Failed to initialize payment gateway. Please try again.');
          }
        }, 100);
      } catch (error) {
        console.error('Error generating Razorpay HTML:', error);
        setHasError(true);
        onPaymentError('Failed to initialize payment gateway. Please try again.');
      }
    } else {
      // Clear HTML content when modal is hidden
      if (!visible) {
        setHtmlContent('');
        setHasError(false);
      }
    }
  }, [visible, options]);

  const handleMessage = (event: any) => {
    try {
      console.log('Received message from WebView:', event.nativeEvent.data);

      const data = event.nativeEvent.data;
      if (!data) {
        console.error('Empty WebView message received');
        return;
      }

      const response = JSON.parse(data);

      // Handle cancellation
      if (response.cancelled || response.cancel) {
        console.log('Payment was cancelled');
        onClose();
        return;
      }

      // Handle JavaScript errors
      if (response.error && response.error_description &&
        response.error_description.includes('JavaScript error')) {
        console.error('JavaScript error in WebView:', response.error_description);

        // Check for specific JavaScript errors
        const errorMsg = response.error_description.toLowerCase();

        if (errorMsg.includes('razorpay') && errorMsg.includes('not defined')) {
          // Razorpay script loading issue
          setHasError(true);
          onPaymentError('Failed to load payment gateway. Please check your internet connection and try again.');
        } else if (errorMsg.includes('network') || errorMsg.includes('connection')) {
          // Network-related errors
          setHasError(true);
          onPaymentError('Network error. Please check your internet connection and try again.');
        } else {
          // Other JavaScript errors
          setHasError(true);
          onPaymentError('There was a problem with the payment gateway. Please try again later.');
        }
        return;
      }

      // Handle other errors
      if (response.error || response.error_code) {
        const errorMessage = response.error_description ||
          response.error?.description ||
          'Payment failed. Please try again.';
        console.error('Payment error:', errorMessage);
        onPaymentError(errorMessage);
        return;
      }

      // Handle success
      if (response.razorpay_payment_id) {
        console.log('Payment success:', response.razorpay_payment_id);
        onPaymentSuccess(response);
        return;
      }

      // Handle unexpected response
      console.warn('Unexpected response:', response);
      onPaymentError('Unexpected response from payment gateway. Please try again.');
    } catch (error) {
      console.error('Error handling WebView message:', error);
      onPaymentError('Error processing payment response. Please try again.');
    }
  };

  const handleReload = () => {
    setHasError(false);
    setLoading(true);
    setWebViewKey(prevKey => prevKey + 1);
  };

  // Log visibility state for debugging
  useEffect(() => {
    console.log(`ExpoRazorpayPayment visibility changed to: ${visible ? 'visible' : 'hidden'}`);
    if (visible) {
      console.log('Payment options available:', !!options);
      console.log('HTML content generated:', !!htmlContent);
    }
  }, [visible, options, htmlContent]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
      onShow={() => console.log('Razorpay modal is now visible')}
    >
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <View style={styles.header}>
            <TouchableOpacity
              onPress={() => {
                console.log('Close button pressed');
                onClose();
              }}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color="#000" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Payment</Text>
          </View>

          {loading && !hasError && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#528FF0" />
              <Text style={styles.loadingText}>Loading payment gateway...</Text>
            </View>
          )}

          {hasError && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>Failed to load payment gateway</Text>
              <TouchableOpacity
                style={styles.reloadButton}
                onPress={handleReload}
              >
                <Text style={styles.reloadButtonText}>Try Again</Text>
              </TouchableOpacity>
            </View>
          )}

          {htmlContent && !hasError ? (
            <WebView
              ref={webViewRef}
              key={webViewKey}
              source={{ html: htmlContent, baseUrl: 'https://checkout.razorpay.com/' }}
              onMessage={handleMessage}
              onLoadStart={() => {
                console.log('WebView started loading');
                setLoading(true);
              }}
              onLoadEnd={() => {
                console.log('WebView finished loading');
                // Add a small delay before hiding the loading indicator
                setTimeout(() => {
                  setLoading(false);
                }, 2000); // Increased delay for better stability
              }}
              style={styles.webView}
              originWhitelist={['*']}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              startInLoadingState={true}
              scalesPageToFit={true}
              mixedContentMode="always"
              allowsInlineMediaPlayback={true}
              allowsFullscreenVideo={true}
              mediaPlaybackRequiresUserAction={false}
              allowFileAccess={true}
              cacheEnabled={false}
              incognito={true} // Use incognito mode to avoid cookie/storage issues
              thirdPartyCookiesEnabled={true}
              sharedCookiesEnabled={true}
              injectedJavaScript={`
                window.onerror = function(message, file, line, col, error) {
                  window.ReactNativeWebView.postMessage(JSON.stringify({
                    error: true,
                    error_description: 'JavaScript error: ' + message
                  }));
                  return true;
                };
                true;
              `}
              onError={(syntheticEvent) => {
                const { nativeEvent } = syntheticEvent;
                console.error('WebView error: ', nativeEvent);
                console.log('WebView error details:', JSON.stringify(nativeEvent));
                setHasError(true);
                setLoading(false);
                onPaymentError('Error loading payment gateway. Please try again.');
              }}
              onHttpError={(syntheticEvent) => {
                const { nativeEvent } = syntheticEvent;
                console.error('WebView HTTP error: ', nativeEvent);
                console.log('WebView HTTP error details:', JSON.stringify(nativeEvent));
                if (nativeEvent.statusCode >= 400) {
                  setHasError(true);
                  setLoading(false);
                  onPaymentError(`Payment gateway error (${nativeEvent.statusCode}). Please try again.`);
                }
              }}
              renderLoading={() => (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#528FF0" />
                  <Text style={styles.loadingText}>Loading payment gateway...</Text>
                </View>
              )}
            />
          ) : null}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

// Generate HTML content for Razorpay checkout
const generateRazorpayHTML = (options: RazorpayOptions) => {
  // Create a clean copy of options without functions or complex objects
  const cleanOptions = {
    key: options.key,
    amount: options.amount,
    currency: options.currency || 'INR',
    name: options.name || '2nd Car',
    description: options.description || 'Purchase',
    order_id: options.order_id,
    prefill: options.prefill || {},
    notes: options.notes || {},
    theme: options.theme || { color: '#1E2A47' }
  };

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
      <meta http-equiv="Content-Security-Policy" content="default-src * 'self' 'unsafe-inline' 'unsafe-eval' data: gap: blob: https://ssl.gstatic.com https://*.razorpay.com https://*.razorpay.in https://api.razorpay.com https://checkout.razorpay.com;">
      <title>Razorpay Payment</title>
      <style>
        body, html {
          margin: 0;
          padding: 0;
          height: 100%;
          width: 100%;
          background-color: #f5f5f5;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        .container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          padding: 20px;
        }
        .message {
          text-align: center;
          margin-bottom: 20px;
        }
        #error-container {
          color: #f44336;
          margin-top: 20px;
          padding: 10px;
          border: 1px solid #f44336;
          border-radius: 4px;
          display: none;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="message">
          <p>Initializing payment gateway...</p>
        </div>
        <div id="error-container"></div>
      </div>

      <script>
        // Error handling function
        function showError(message) {
          const errorContainer = document.getElementById('error-container');
          errorContainer.textContent = message;
          errorContainer.style.display = 'block';

          // Send error to React Native
          if (window.ReactNativeWebView) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              error: true,
              error_description: message
            }));
          }
        }

        // Global error handler
        window.onerror = function(message, source, lineno, colno, error) {
          console.error('JavaScript error:', message, 'at', source, lineno, colno);
          showError('JavaScript error: ' + message);
          return true;
        };

        // Load Razorpay script
        function loadRazorpayScript() {
          return new Promise((resolve, reject) => {
            if (window.Razorpay) {
              resolve();
              return;
            }

            const script = document.createElement('script');
            script.src = 'https://checkout.razorpay.com/v1/checkout.js';
            script.onload = () => {
              console.log('Razorpay script loaded successfully');
              resolve();
            };
            script.onerror = (error) => {
              console.error('Failed to load Razorpay script:', error);
              reject(new Error('Failed to load Razorpay script'));
            };
            document.body.appendChild(script);
          });
        }

        // Function to handle payment success
        function handleSuccess(response) {
          if (window.ReactNativeWebView) {
            window.ReactNativeWebView.postMessage(JSON.stringify(response));
          }
        }

        // Function to handle payment failure
        function handleFailure(response) {
          if (window.ReactNativeWebView) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              error: true,
              error_code: response.error.code,
              error_description: response.error.description,
              error_source: response.error.source,
              error_step: response.error.step,
              error_reason: response.error.reason
            }));
          }
        }

        // Function to handle modal dismiss
        function handleDismiss() {
          if (window.ReactNativeWebView) {
            window.ReactNativeWebView.postMessage(JSON.stringify({ cancelled: true }));
          }
        }

        // Function to initialize Razorpay
        async function initializeRazorpay() {
          try {
            console.log('Starting Razorpay initialization');

            // Update UI to show we're initializing
            document.querySelector('.message p').textContent = 'Connecting to payment gateway...';

            // First ensure the script is loaded
            await loadRazorpayScript();
            console.log('Razorpay script loaded, checking if Razorpay object exists');

            // Update UI to show script is loaded
            document.querySelector('.message p').textContent = 'Preparing payment...';

            // Check if Razorpay is available
            if (!window.Razorpay) {
              console.error('Razorpay object not available after script load');
              throw new Error('Razorpay not available');
            }

            console.log('Razorpay object available, preparing options');

            // Prepare options with explicit type conversion for amount
            const options = ${JSON.stringify(cleanOptions)};

            // Ensure amount is treated as a number
            options.amount = Number(options.amount);

            console.log('Payment options prepared:', JSON.stringify({
              key: options.key.substring(0, 4) + '...',
              order_id: options.order_id,
              amount: options.amount,
              currency: options.currency
            }));

            // Add handlers
            options.handler = handleSuccess;
            options.modal = {
              ondismiss: handleDismiss,
              escape: false,
              backdropclose: false
            };

            // Add additional options for better compatibility
            options.retry = options.retry || { enabled: false };
            options.theme = options.theme || { color: '#1E2A47' };
            options.send_sms_hash = true;

            console.log('Creating Razorpay instance');
            // Create Razorpay instance
            const rzp = new window.Razorpay(options);
            console.log('Razorpay instance created successfully');

            // Set up event listeners
            rzp.on('payment.failed', handleFailure);
            console.log('Payment failure event listener added');

            // Update UI to show we're opening checkout
            document.querySelector('.message p').textContent = 'Opening payment gateway...';

            // Open Razorpay checkout with a small delay
            setTimeout(() => {
              console.log('Opening Razorpay checkout');
              rzp.open();
              console.log('Razorpay checkout opened');
            }, 500);

            // Check if the modal opened successfully
            setTimeout(() => {
              const razorpayContainer = document.querySelector('.razorpay-container');
              if (!razorpayContainer) {
                console.error('Razorpay container not found in DOM after 5 seconds');
                showError('Payment form did not appear. Please try again.');
              } else {
                console.log('Razorpay container found in DOM, checkout displayed successfully');
              }
            }, 5000);
          } catch (error) {
            console.error('Error initializing Razorpay:', error);
            showError(error.message || 'Failed to initialize Razorpay');
          }
        }

        // Track initialization attempts
        let initAttempted = false;

        // Initialize when document is ready
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
          console.log('Document already complete, initializing Razorpay immediately');
          initAttempted = true;
          initializeRazorpay();
        } else {
          document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded event fired, initializing Razorpay with delay');
            initAttempted = true;
            // Give a bit more time for everything to be properly loaded
            setTimeout(initializeRazorpay, 800);
          });
        }

        // Fallback initialization with increased timeout
        setTimeout(() => {
          if (!initAttempted) {
            console.log('Fallback initialization triggered');
            initializeRazorpay();
          } else {
            console.log('Skipping fallback initialization as Razorpay was already initialized');
          }
        }, 2000);
      </script>
    </body>
    </html>
  `;
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff'
  },
  container: {
    flex: 1,
    backgroundColor: '#fff'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 56,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
    position: 'relative',
    zIndex: 10
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000'
  },
  closeButton: {
    position: 'absolute',
    left: 16,
    padding: 8
  },
  webView: {
    flex: 1,
    backgroundColor: '#fff'
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    zIndex: 5
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666'
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20
  },
  errorText: {
    fontSize: 16,
    color: '#f44336',
    marginBottom: 20,
    textAlign: 'center'
  },
  reloadButton: {
    backgroundColor: '#528FF0',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 6
  },
  reloadButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500'
  }
});

export default ExpoRazorpayPayment;
