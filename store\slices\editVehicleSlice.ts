import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";

const BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

// Helper to get auth token
const getAuthToken = async () => {
  try {
    const token = await AsyncStorage.getItem("token");
    return token;
  } catch (error) {
    console.error("Error getting auth token:", error);
    return null;
  }
};

// ---------------------------------------------------
// Types and Interfaces
// ---------------------------------------------------
export interface VehicleEditData {
  id: number;
  title: string;
  brand_id: number | string;
  model_id: number | string;
  variant_id?: string;
  variant: string;
  year: string;
  fuel_type: string;
  kilometers_driven: number;
  price: string;
  transmission: string;
  ownership: string;
  state_id: number | string;
  state_name?: string;
  city_id: number | string;
  city_name?: string;
  brand?: string;
  model?: string;
  location: string;
  // Common fields
  description: string;
  Images: string[]; // Note: Capital 'I' as per API response
  images?: string[]; // Fallback for lowercase
  status: string;
  insurance_type?: string;
  make_offer?: boolean | string;
  is_verified?: boolean;
  updated_at?: string;
  created_at?: string;
  user_id?: number;
}

export interface EditVehicleResponse {
  status: boolean;
  message: string;
  data: VehicleEditData;
}

interface Brand {
  id: number;
  name: string;
  key: string;
  image_url?: string;
  image?: string;
}

interface Model {
  id: number;
  name: string;
}

interface Variant {
  id: number;
  name: string;
}

interface State {
  id: number;
  name: string;
  country_id: string;
  country_code: string;
  country_name: string;
  state_code: string;
  latitude: string;
  longitude: string;
}

interface City {
  id: number;
  name: string;
  state_id: string;
  state_code: string;
  state_name: string;
  country_id: string;
  country_code: string;
  country_name: string;
  latitude: string;
  longitude: string;
}

// ---------------------------------------------------
// API Functions
// ---------------------------------------------------
const api = {
  fetchVehicleDetails: async (vehicleId: string, vehicleType: string) => {
    const token = await getAuthToken();
    if (!token) {
      throw new Error("Authentication required");
    }

    // Determine the correct endpoint based on vehicle type
    let endpoint = "";
    switch (vehicleType) {
      case "car":
        endpoint = `${BASE_URL}/car-sell-display/${vehicleId}`;
        break;
      case "bike":
        endpoint = `${BASE_URL}/bike-sell-display/${vehicleId}`;
        break;
      case "scooter":
        endpoint = `${BASE_URL}/scooter-sell-display/${vehicleId}`;
        break;
      case "commercial":
        endpoint = `${BASE_URL}/commercial-sell-display/${vehicleId}`;
        break;
      default:
        endpoint = `${BASE_URL}/car-sell-display/${vehicleId}`;
    }

    const response = await axios.get(endpoint, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    return response.data;
  },

  updateVehicle: async (vehicleId: string, vehicleType: string, data: any) => {
    const token = await getAuthToken();
    if (!token) {
      throw new Error("Authentication required");
    }

    const formData = new FormData();

    // Append all form fields
    Object.keys(data).forEach((key) => {
      if (key === "images") {
        // Handle images array
        data[key].forEach((image: any, index: number) => {
          if (typeof image === "string") {
            // Existing image URL
            formData.append("existing_images[]", image);
          } else {
            // New image file
            formData.append("images[]", {
              uri: image.uri,
              type: "image/jpeg",
              name: image.name || `image${index + 1}.jpg`,
            } as any);
          }
        });
      } else {
        formData.append(key, data[key]);
      }
    });

    // Determine the correct endpoint based on vehicle type
    let endpoint = "";
    switch (vehicleType) {
      case "car":
        endpoint = `${BASE_URL}/car-sell-edit/${vehicleId}`;
        break;
      case "bike":
        endpoint = `${BASE_URL}/bike-sell-edit/${vehicleId}`;
        break;
      case "scooter":
        endpoint = `${BASE_URL}/scooter-sell-edit/${vehicleId}`;
        break;
      case "commercial":
        endpoint = `${BASE_URL}/commercial-sell-edit/${vehicleId}`;
        break;
      default:
        endpoint = `${BASE_URL}/car-sell-edit/${vehicleId}`;
    }

    const response = await axios.post(endpoint, formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "multipart/form-data",
      },
    });

    return response.data;
  },

  fetchBrands: async (type: string) => {
    const token = await getAuthToken();
    if (!token) {
      throw new Error("Authentication required");
    }

    const response = await axios.get(`${BASE_URL}/brands`, {
      headers: { Authorization: `Bearer ${token}` },
      params: { type, limit: 10000, offset: 0 },
    });

    return response.data;
  },

  fetchModels: async (type: string, brandId: string) => {
    const token = await getAuthToken();
    if (!token) {
      throw new Error("Authentication required");
    }

    const response = await axios.get(`${BASE_URL}/models`, {
      headers: { Authorization: `Bearer ${token}` },
      params: { type, brand_id: brandId, limit: 10000 },
    });
    return response.data;
  },

  fetchVariants: async (modelId: string) => {
    const token = await getAuthToken();
    if (!token) {
      throw new Error("Authentication required");
    }

    const response = await axios.get(`${BASE_URL}/car-variants`, {
      headers: { Authorization: `Bearer ${token}` },
      params: { model_id: modelId, limit: 10000 },
    });
    return response.data;
  },

  fetchStates: async () => {
    const token = await getAuthToken();
    if (!token) {
      throw new Error("Authentication required");
    }

    const response = await axios.get(`${BASE_URL}/states/101`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data;
  },

  fetchCities: async (stateId: string) => {
    const token = await getAuthToken();
    if (!token) {
      throw new Error("Authentication required");
    }

    const response = await axios.get(`${BASE_URL}/cities/${stateId}`, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data;
  },

  deleteVehicle: async (vehicleId: string, vehicleType: string) => {
    const token = await getAuthToken();
    if (!token) {
      throw new Error("Authentication required");
    }

    // Determine the correct endpoint based on vehicle type
    let endpoint = "";
    switch (vehicleType) {
      case "car":
        endpoint = `${BASE_URL}/car-sell-delete/${vehicleId}`;
        break;
      case "bike":
        endpoint = `${BASE_URL}/bike-sell-delete/${vehicleId}`;
        break;
      case "scooter":
        endpoint = `${BASE_URL}/scooter-sell-delete/${vehicleId}`;
        break;
      case "commercial":
        endpoint = `${BASE_URL}/commercial-sell-delete/${vehicleId}`;
        break;
      default:
        endpoint = `${BASE_URL}/car-sell-delete/${vehicleId}`;
    }

    const response = await axios.delete(endpoint, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    return response.data;
  },
};

// ---------------------------------------------------
// Async Thunks
// ---------------------------------------------------
export const fetchVehicleForEdit = createAsyncThunk(
  "editVehicle/fetchVehicleForEdit",
  async (
    { vehicleId, vehicleType }: { vehicleId: string; vehicleType: string },
    { rejectWithValue }
  ) => {
    try {
      return await api.fetchVehicleDetails(vehicleId, vehicleType);
    } catch (error: any) {
      console.error("Fetch vehicle for edit error:", error);
      if (error.response) {
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        return rejectWithValue({ message: "No response from server" });
      } else {
        return rejectWithValue({ message: error.message });
      }
    }
  }
);

export const updateVehicle = createAsyncThunk(
  "editVehicle/updateVehicle",
  async (
    {
      vehicleId,
      vehicleType,
      formData,
    }: { vehicleId: string; vehicleType: string; formData: any },
    { rejectWithValue }
  ) => {
    try {
      return await api.updateVehicle(vehicleId, vehicleType, formData);
    } catch (error: any) {
      console.error("Update vehicle error:", error);
      if (error.response) {
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        return rejectWithValue({ message: "No response from server" });
      } else {
        return rejectWithValue({ message: error.message });
      }
    }
  }
);

export const deleteVehicle = createAsyncThunk(
  "editVehicle/deleteVehicle",
  async (
    { vehicleId, vehicleType }: { vehicleId: string; vehicleType: string },
    { rejectWithValue }
  ) => {
    try {
      return await api.deleteVehicle(vehicleId, vehicleType);
    } catch (error: any) {
      console.error("Delete vehicle error:", error);
      if (error.response) {
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        return rejectWithValue({ message: "No response from server" });
      } else {
        return rejectWithValue({ message: error.message });
      }
    }
  }
);

export const fetchEditBrands = createAsyncThunk(
  "editVehicle/fetchBrands",
  async (type: string, { rejectWithValue }) => {
    try {
      const data = await api.fetchBrands(type);
      const list = data.data ? data.data : data;

      if (!Array.isArray(list)) {
        console.error("❌ Invalid brands data format:", list);
        return rejectWithValue({
          message: "Invalid data format received from server",
        });
      }

      const transformedBrands = list.map((brand: Brand) => ({
        label: brand.name,
        value: brand.id.toString(),
        image: brand.image_url || brand.image,
        key: brand.key,
      }));

      return transformedBrands;
    } catch (error: any) {
      console.error("Fetch brands error:", error);
      return rejectWithValue({
        message:
          error.response?.data?.message ||
          error.message ||
          "Failed to fetch brands",
      });
    }
  }
);

export const fetchEditModels = createAsyncThunk(
  "editVehicle/fetchModels",
  async (
    { type, brandId }: { type: string; brandId: string },
    { rejectWithValue }
  ) => {
    try {
      const data = await api.fetchModels(type, brandId);
      const list = data.data ? data.data : data;

      if (!Array.isArray(list)) {
        console.error("Invalid models data format:", list);
        return rejectWithValue({
          message: "Invalid data format received from server",
        });
      }

      const transformedModels = list.map((model: Model) => ({
        label: model.name,
        value: model.id.toString(),
      }));

      return transformedModels;
    } catch (error: any) {
      console.error("Fetch models error:", error);
      return rejectWithValue({
        message:
          error.response?.data?.message ||
          error.message ||
          "Failed to fetch models",
      });
    }
  }
);

export const fetchEditVariants = createAsyncThunk(
  "editVehicle/fetchVariants",
  async (modelId: string, { rejectWithValue }) => {
    try {
      const data = await api.fetchVariants(modelId);
      const list = data.data ? data.data : data;

      if (!Array.isArray(list)) {
        console.error("Invalid variants data format:", list);
        return rejectWithValue({
          message: "Invalid data format received from server",
        });
      }

      const transformedVariants = list.map((variant: Variant) => ({
        label: variant.name,
        value: variant.id.toString(),
      }));

      return transformedVariants;
    } catch (error: any) {
      console.error("Fetch variants error:", error);
      return rejectWithValue({
        message:
          error.response?.data?.message ||
          error.message ||
          "Failed to fetch variants",
      });
    }
  }
);

export const fetchEditStates = createAsyncThunk(
  "editVehicle/fetchStates",
  async (_, { rejectWithValue }) => {
    try {
      const data = await api.fetchStates();
      const list = data.data ? data.data : data;

      if (!Array.isArray(list)) {
        console.error("❌ Invalid states data format:", list);
        return rejectWithValue({
          message: "Invalid data format received from server",
        });
      }

      const transformedStates = list.map((state: State) => ({
        label: state.name,
        value: state.id.toString(),
      }));

      return transformedStates;
    } catch (error: any) {
      console.error("Fetch states error:", error);
      return rejectWithValue({
        message: error.message || "Failed to fetch states",
      });
    }
  }
);

export const fetchEditCities = createAsyncThunk(
  "editVehicle/fetchCities",
  async (stateId: string, { rejectWithValue }) => {
    try {
      const data = await api.fetchCities(stateId);
      const list = data.data ? data.data : data;
      return list.map((city: City) => ({
        label: city.name,
        value: city.id.toString(),
      }));
    } catch (error: any) {
      console.error("Fetch cities error:", error);
      return rejectWithValue({
        message: error.message || "Failed to fetch cities",
      });
    }
  }
);

// ---------------------------------------------------
// Redux Slice
// ---------------------------------------------------
interface EditVehicleState {
  vehicleData: VehicleEditData | null;
  brands: { label: string; value: string; image?: string; key?: string }[];
  models: { label: string; value: string }[];
  variants: { label: string; value: string }[];
  states: { label: string; value: string }[];
  cities: { label: string; value: string }[];
  loading: boolean;
  updating: boolean;
  deleting: boolean;
  error: string | null;
  // Add specific loading states to prevent duplicate requests
  loadingStates: {
    vehicleData: boolean;
    brands: boolean;
    models: boolean;
    variants: boolean;
    states: boolean;
    cities: boolean;
  };
}

const initialState: EditVehicleState = {
  vehicleData: null,
  brands: [],
  models: [],
  variants: [],
  states: [],
  cities: [],
  loading: false,
  updating: false,
  deleting: false,
  error: null,
  loadingStates: {
    vehicleData: false,
    brands: false,
    models: false,
    variants: false,
    states: false,
    cities: false,
  },
};

const editVehicleSlice = createSlice({
  name: "editVehicle",
  initialState,
  reducers: {
    clearEditVehicleData: (state) => {
      state.vehicleData = null;
      state.error = null;
    },
    clearEditModels: (state) => {
      state.models = [];
    },
    clearEditVariants: (state) => {
      state.variants = [];
    },
    clearEditCities: (state) => {
      state.cities = [];
    },
    resetEditVehicleState: () => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch vehicle for edit
      .addCase(fetchVehicleForEdit.pending, (state) => {
        state.loading = true;
        state.loadingStates.vehicleData = true;
        state.error = null;
      })
      .addCase(fetchVehicleForEdit.fulfilled, (state, action) => {
        state.loading = false;
        state.loadingStates.vehicleData = false;
        state.vehicleData = action.payload.data;
      })
      .addCase(fetchVehicleForEdit.rejected, (state, action) => {
        state.loading = false;
        state.loadingStates.vehicleData = false;
        const payload = action.payload as any;
        state.error =
          payload?.message ||
          action.error?.message ||
          "Failed to fetch vehicle details";
      })
      // Update vehicle
      .addCase(updateVehicle.pending, (state) => {
        state.updating = true;
        state.error = null;
      })
      .addCase(updateVehicle.fulfilled, (state, action) => {
        state.updating = false;
        state.vehicleData = action.payload.data;
      })
      .addCase(updateVehicle.rejected, (state, action) => {
        state.updating = false;
        const payload = action.payload as any;
        state.error =
          payload?.message ||
          action.error?.message ||
          "Failed to update vehicle";
      })
      // Delete vehicle
      .addCase(deleteVehicle.pending, (state) => {
        state.deleting = true;
        state.error = null;
      })
      .addCase(deleteVehicle.fulfilled, (state) => {
        state.deleting = false;
        // Clear vehicle data since it's been deleted
        state.vehicleData = null;
      })
      .addCase(deleteVehicle.rejected, (state, action) => {
        state.deleting = false;
        const payload = action.payload as any;
        state.error =
          payload?.message ||
          action.error?.message ||
          "Failed to delete vehicle";
      })
      // Fetch brands
      .addCase(fetchEditBrands.pending, (state) => {
        state.loadingStates.brands = true;
      })
      .addCase(fetchEditBrands.fulfilled, (state, action) => {
        state.loadingStates.brands = false;
        state.brands = action.payload;
      })
      .addCase(fetchEditBrands.rejected, (state, action) => {
        state.loadingStates.brands = false;
        const payload = action.payload as any;
        if (payload?.message !== "Request already in progress") {
          state.error =
            payload?.message ||
            action.error.message ||
            "Failed to fetch brands";
        }
      })
      // Fetch models
      .addCase(fetchEditModels.pending, (state) => {
        state.loadingStates.models = true;
      })
      .addCase(fetchEditModels.fulfilled, (state, action) => {
        state.loadingStates.models = false;
        state.models = action.payload;
      })
      .addCase(fetchEditModels.rejected, (state, action) => {
        state.loadingStates.models = false;
        const payload = action.payload as any;
        if (payload?.message !== "Request already in progress") {
          state.error =
            payload?.message ||
            action.error.message ||
            "Failed to fetch models";
        }
      })
      // Fetch variants
      .addCase(fetchEditVariants.pending, (state) => {
        state.loadingStates.variants = true;
      })
      .addCase(fetchEditVariants.fulfilled, (state, action) => {
        state.loadingStates.variants = false;
        state.variants = action.payload;
      })
      .addCase(fetchEditVariants.rejected, (state, action) => {
        state.loadingStates.variants = false;
        const payload = action.payload as any;
        if (payload?.message !== "Request already in progress") {
          state.error =
            payload?.message ||
            action.error.message ||
            "Failed to fetch variants";
        }
      })
      // Fetch states
      .addCase(fetchEditStates.pending, (state) => {
        state.loadingStates.states = true;
      })
      .addCase(fetchEditStates.fulfilled, (state, action) => {
        state.loadingStates.states = false;
        state.states = action.payload;
      })
      .addCase(fetchEditStates.rejected, (state, action) => {
        state.loadingStates.states = false;
        const payload = action.payload as any;
        if (payload?.message !== "Request already in progress") {
          state.error =
            payload?.message ||
            action.error.message ||
            "Failed to fetch states";
        }
      })
      // Fetch cities
      .addCase(fetchEditCities.pending, (state) => {
        state.loadingStates.cities = true;
      })
      .addCase(fetchEditCities.fulfilled, (state, action) => {
        state.loadingStates.cities = false;
        state.cities = action.payload;
      })
      .addCase(fetchEditCities.rejected, (state, action) => {
        state.loadingStates.cities = false;
        const payload = action.payload as any;
        if (payload?.message !== "Request already in progress") {
          state.error =
            payload?.message ||
            action.error.message ||
            "Failed to fetch cities";
        }
      });
  },
});

export const {
  clearEditVehicleData,
  clearEditModels,
  clearEditVariants,
  clearEditCities,
  resetEditVehicleState,
} = editVehicleSlice.actions;

export default editVehicleSlice.reducer;
