// types/payment.ts
export interface ApplyCodeRequest {
  code: string;
  package_id: string | number;
}

export interface ApplyCodeResponse {
  status: boolean;
  message: string;
  package_amount?: string;
  discount_amount?: number;
  final_amount?: number;
}

export interface RazorOrderCreateRequest {
  package_id: number;
  coupon_code?: string;
}

export interface RazorOrderCreateSuccessResponse {
  success: boolean;
  order_id: string;
  original_amount: string;
  discount_amount: string;
  final_amount: number;
  razorpay_amount: number;
}

export interface RazorOrderCreateErrorResponse {
  status: boolean;
  message: string;
}

export type RazorOrderCreateResponse =
  | RazorOrderCreateSuccessResponse
  | RazorOrderCreateErrorResponse;

export interface PackageBuyRequest {
  package_id: string | number;
  payment_method: "online";
  code?: string;
  payment_id?: string;
}

export interface PackageBuyResponse {
  status: boolean;
  message: string;
}

export interface RazorpayOptions {
  key: string;
  amount: number;
  currency: string;
  name: string;
  description: string;
  order_id: string;
  image?: string;
  prefill: {
    name: string;
    email: string;
    contact: string;
  };
  theme: {
    color: string;
  };
  retry?: {
    enabled: boolean;
    max_count?: number;
  };
  notes?: Record<string, string>;
}

export interface RazorpaySuccessResponse {
  razorpay_payment_id: string;
  razorpay_order_id: string;
  razorpay_signature: string;
}
