import React from 'react';
import { View, TouchableOpacity, StyleSheet, TextStyle } from 'react-native';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING, FONTS } from '@/constants/theme';

interface SectionHeaderProps {
    title: string;
    onViewAllPress?: () => void;
}

export const SectionHeader = ({ title, onViewAllPress }: SectionHeaderProps) => {
    return (
        <View style={styles.container}>
            {/* ✅ Ensure that styles.title is only applied to ThemedText */}
            <ThemedText style={[styles.text, {
                fontSize: 18,
                ...FONTS.bold,
            } as TextStyle]}>{title}</ThemedText>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: SPACING.md,
        marginBottom: SPACING.sm,
    },
    text: {
        // Ensures all text-related styles are correctly handled
        color: COLORS.text.primary,
    },


});
