import React, { useState } from 'react';
import {
    View,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Image,
    TextInput,
    Platform,
    KeyboardAvoidingView,
    Alert,
    StatusBar,
    TextStyle
} from 'react-native';
import { useRouter } from 'expo-router';
import { Formik } from 'formik';
import * as Yup from 'yup';
import * as ImagePicker from 'expo-image-picker';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, FONTS, SPACING } from '@/constants/theme';
import { ThemedText } from '@/components/common/ThemedText';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { updateProfile } from '@/store/slices/accountSlice';
import ThemedButton from '@/components/common/ThemedButton';
import Constants from 'expo-constants';
import DateTimePicker from '@react-native-community/datetimepicker';

// Validation schema
const profileSchema = Yup.object().shape({
    name: Yup.string()
        .min(2, 'Name must be at least 2 characters')
        .required('Name is required'),
    gender: Yup.string().required('Gender is required'),
    phone: Yup.string()
        .matches(/^[0-9]{10}$/, 'Phone number must be 10 digits')
        .required('Phone number is required'),
    address: Yup.string().required('Address is required'),
    city: Yup.string().required('City is required'),
    state: Yup.string().required('State is required'),
    country: Yup.string().required('Country is required'),
    postal_code: Yup.string()
        .matches(/^[0-9]{6}$/, 'Postal code must be 6 digits')
        .required('Postal code is required'),
    dob: Yup.string().required('Date of birth is required'),
});

export default function EditProfileScreen() {
    const router = useRouter();
    const dispatch = useAppDispatch();
    const { userData: user } = useAppSelector((state) => state.account);

    // Format the initial dob (if available) to YYYY-MM-DD
    const formatDate = (date: Date) => {
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${year}-${month}-${day}`;
    };
    const initialDob = user?.dob ? formatDate(new Date(user.dob)) : '';

    const [profileImage, setProfileImage] = useState(user?.profile_picture || null);
    const [showDatePicker, setShowDatePicker] = useState(false);

    // IMAGE PICKER
    const pickImage = async () => {
        try {
            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: false,
                aspect: [1, 1],
                quality: 0.8,
            });
            if (!result.canceled && result.assets && result.assets.length > 0) {
                setProfileImage(result.assets[0].uri);
                return result.assets[0].uri;
            }
            return null;
        } catch (error) {
            console.error('Error picking image:', error);
            return null;
        }
    };

    // Form submission: Dispatch updateProfile thunk only.
    const handleSubmitForm = async (values: any) => {
        // Merge profile image with the form values
        const updatedValues = {
            ...values,
            profile_picture: profileImage,
        };
        const result = await dispatch(updateProfile(updatedValues));

        if (updateProfile.fulfilled.match(result)) {
            Alert.alert('Success', 'Profile updated successfully');
            router.back();
        } else {
            Alert.alert('Error', result?.error?.message || 'Failed to update profile');
        }
    };

    return (
        <KeyboardAvoidingView
            style={styles.container}
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
        >
            <StatusBar barStyle="light-content" backgroundColor="#1E2A47" />

            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
                    <Ionicons name="arrow-back" size={24} color={COLORS.white} />
                </TouchableOpacity>
                <ThemedText style={styles.headerTitle}>Edit Profile</ThemedText>
                <View style={{ width: 24 }} />
            </View>

            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                <Formik
                    initialValues={{
                        name: user?.name || '',
                        dob: initialDob,
                        gender: user?.gender || '',
                        phone: user?.phone || '',
                        address: user?.address || '',
                        city: user?.city || '',
                        state: user?.state || '',
                        country: user?.country || '',
                        postal_code: user?.postal_code || '',
                    }}
                    validationSchema={profileSchema}
                    onSubmit={handleSubmitForm}
                >
                    {({ handleChange, handleBlur, handleSubmit, setFieldValue, values, errors, touched }) => (
                        <View style={styles.formContainer}>
                            {/* Profile Picture */}
                            <View style={styles.profileImageContainer}>
                                {profileImage ? (
                                    <Image source={{ uri: profileImage }} style={styles.profileImage} />
                                ) : (
                                    <View style={styles.profilePlaceholder}>
                                        <ThemedText style={styles.profilePlaceholderText}>
                                            {user?.name ? user.name.charAt(0).toUpperCase() : '?'}
                                        </ThemedText>
                                    </View>
                                )}
                                <TouchableOpacity
                                    style={styles.uploadButton}
                                    onPress={async () => {
                                        const uri = await pickImage();
                                        if (uri) {
                                            setFieldValue('profile_picture', uri);
                                        }
                                    }}
                                >
                                    <ThemedText style={styles.uploadButtonText}>Upload Image</ThemedText>
                                </TouchableOpacity>
                            </View>

                            {/* Name */}
                            <View style={styles.inputGroup}>
                                <ThemedText style={styles.label}>Name</ThemedText>
                                <TextInput
                                    style={[styles.input, touched.name && errors.name && styles.inputError]}
                                    value={values.name}
                                    onChangeText={handleChange('name')}
                                    onBlur={handleBlur('name')}
                                    placeholder="Enter your name"
                                    placeholderTextColor={COLORS.text.secondary}
                                />
                                {touched.name && errors.name && (
                                    <ThemedText style={styles.errorText}>{errors.name as string}</ThemedText>
                                )}
                            </View>

                            {/* Date of Birth */}
                            <View style={styles.inputGroup}>
                                <ThemedText style={styles.label}>Date of Birth</ThemedText>
                                <TouchableOpacity
                                    style={[styles.input, touched.dob && errors.dob && styles.inputError, { justifyContent: 'center' }]}
                                    onPress={() => setShowDatePicker(true)}
                                >
                                    <ThemedText style={{ color: values.dob ? COLORS.text.primary : COLORS.text.secondary }}>
                                        {values.dob || 'Select your date of birth'}
                                    </ThemedText>
                                </TouchableOpacity>
                                {touched.dob && errors.dob && (
                                    <ThemedText style={styles.errorText}>{errors.dob}</ThemedText>
                                )}
                                {/* Display formatted date for debugging */}
                                {values.dob ? (
                                    <ThemedText style={styles.formattedDateText}>
                                        Formatted DOB: {values.dob}
                                    </ThemedText>
                                ) : null}
                            </View>

                            {showDatePicker && (
                                <DateTimePicker
                                    mode="date"
                                    display="default"
                                    maximumDate={new Date()}
                                    value={values.dob ? new Date(values.dob) : new Date()}
                                    onChange={(event, selectedDate) => {
                                        setShowDatePicker(false);
                                        if (event.type === 'set' && selectedDate) {
                                            const formatted = formatDate(selectedDate).trim();
                                            setFieldValue('dob', formatted);
                                        }
                                    }}
                                />
                            )}

                            {/* Gender */}
                            <View style={styles.inputGroup}>
                                <ThemedText style={styles.label}>Gender</ThemedText>
                                <View style={styles.genderContainer}>
                                    {['male', 'female', 'other'].map((genderOption) => (
                                        <TouchableOpacity
                                            key={genderOption}
                                            style={styles.genderOption}
                                            onPress={() => setFieldValue('gender', genderOption)}
                                        >
                                            <View
                                                style={[
                                                    styles.radioOuter,
                                                    values.gender === genderOption && styles.radioOuterSelected,
                                                ]}
                                            >
                                                {values.gender === genderOption && <View style={styles.radioInner} />}
                                            </View>
                                            <ThemedText style={styles.genderText}>
                                                {genderOption.charAt(0).toUpperCase() + genderOption.slice(1)}
                                            </ThemedText>
                                        </TouchableOpacity>
                                    ))}
                                </View>
                                {touched.gender && errors.gender && (
                                    <ThemedText style={styles.errorText}>{errors.gender as string}</ThemedText>
                                )}
                            </View>

                            {/* Phone */}
                            <View style={styles.inputGroup}>
                                <ThemedText style={styles.label}>Phone</ThemedText>
                                <TextInput
                                    style={[styles.input, touched.phone && errors.phone && styles.inputError]}
                                    value={values.phone}
                                    onChangeText={handleChange('phone')}
                                    onBlur={handleBlur('phone')}
                                    placeholder="Enter your phone number"
                                    placeholderTextColor={COLORS.text.secondary}
                                    keyboardType="phone-pad"
                                />
                                {touched.phone && errors.phone && (
                                    <ThemedText style={styles.errorText}>{errors.phone as string}</ThemedText>
                                )}
                            </View>

                            {/* Address */}
                            <View style={styles.inputGroup}>
                                <ThemedText style={styles.label}>Address</ThemedText>
                                <TextInput
                                    style={[styles.textArea, touched.address && errors.address && styles.inputError]}
                                    value={values.address}
                                    onChangeText={handleChange('address')}
                                    onBlur={handleBlur('address')}
                                    placeholder="Enter your address"
                                    placeholderTextColor={COLORS.text.secondary}
                                    multiline
                                />
                                {touched.address && errors.address && (
                                    <ThemedText style={styles.errorText}>{errors.address as string}</ThemedText>
                                )}
                            </View>

                            {/* City */}
                            <View style={styles.inputGroup}>
                                <ThemedText style={styles.label}>City</ThemedText>
                                <TextInput
                                    style={[styles.input, touched.city && errors.city && styles.inputError]}
                                    value={values.city}
                                    onChangeText={handleChange('city')}
                                    onBlur={handleBlur('city')}
                                    placeholder="Enter your city"
                                    placeholderTextColor={COLORS.text.secondary}
                                />
                                {touched.city && errors.city && (
                                    <ThemedText style={styles.errorText}>{errors.city as string}</ThemedText>
                                )}
                            </View>

                            {/* State */}
                            <View style={styles.inputGroup}>
                                <ThemedText style={styles.label}>State</ThemedText>
                                <TextInput
                                    style={[styles.input, touched.state && errors.state && styles.inputError]}
                                    value={values.state}
                                    onChangeText={handleChange('state')}
                                    onBlur={handleBlur('state')}
                                    placeholder="Enter your state"
                                    placeholderTextColor={COLORS.text.secondary}
                                />
                                {touched.state && errors.state && (
                                    <ThemedText style={styles.errorText}>{errors.state as string}</ThemedText>
                                )}
                            </View>

                            {/* Country */}
                            <View style={styles.inputGroup}>
                                <ThemedText style={styles.label}>Country</ThemedText>
                                <TextInput
                                    style={[styles.input, touched.country && errors.country && styles.inputError]}
                                    value={values.country}
                                    onChangeText={handleChange('country')}
                                    onBlur={handleBlur('country')}
                                    placeholder="Enter your country"
                                    placeholderTextColor={COLORS.text.secondary}
                                />
                                {touched.country && errors.country && (
                                    <ThemedText style={styles.errorText}>{errors.country as string}</ThemedText>
                                )}
                            </View>

                            {/* Postal Code */}
                            <View style={styles.inputGroup}>
                                <ThemedText style={styles.label}>Postal Code</ThemedText>
                                <TextInput
                                    style={[styles.input, touched.postal_code && errors.postal_code && styles.inputError]}
                                    value={values.postal_code}
                                    onChangeText={handleChange('postal_code')}
                                    onBlur={handleBlur('postal_code')}
                                    placeholder="Enter your postal code"
                                    placeholderTextColor={COLORS.text.secondary}
                                    keyboardType="number-pad"
                                />
                                {touched.postal_code && errors.postal_code && (
                                    <ThemedText style={styles.errorText}>{errors.postal_code as string}</ThemedText>
                                )}
                            </View>

                            {/* Buttons */}
                            <View style={styles.buttonContainer}>
                                <ThemedButton onPress={() => router.back()} style={styles.cancelButton}>
                                    <ThemedText style={{ color: COLORS.black }}>Cancel</ThemedText>
                                </ThemedButton>
                                <ThemedButton onPress={handleSubmit} style={styles.saveButton}>
                                    Save Changes
                                </ThemedButton>
                            </View>
                        </View>
                    )}
                </Formik>
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

/* ----------------- STYLES ----------------- */
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.background,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingTop: 16 + Constants.statusBarHeight,
        backgroundColor: '#1E2A47',
        paddingHorizontal: 16,
        paddingVertical: 14,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    backButton: {
        padding: SPACING.xs,
        borderRadius: 20,
    },
    headerTitle: {
        fontSize: 20,
        ...FONTS.medium,
        fontWeight: 'bold',
        color: COLORS.white,
    } as TextStyle,
    scrollView: {
        flex: 1,
        backgroundColor: COLORS.background,
    },
    formContainer: {
        padding: SPACING.md,
    },
    /* Profile Image */
    profileImageContainer: {
        alignItems: 'center',
        marginBottom: SPACING.lg,
    },
    profileImage: {
        width: 120,
        height: 120,
        borderRadius: 60,
        marginBottom: SPACING.sm,
        borderWidth: 3,
        borderColor: COLORS.primary,
    },
    profilePlaceholder: {
        width: 120,
        height: 120,
        borderRadius: 60,
        backgroundColor: COLORS.primary,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: SPACING.sm,
    },
    profilePlaceholderText: {
        fontSize: 40,
        color: '#fff',
        fontWeight: 'bold',
    },
    uploadButton: {
        backgroundColor: COLORS.background,
        paddingVertical: SPACING.xs,
        paddingHorizontal: SPACING.md,
        borderRadius: 20,
        borderWidth: 1,
        borderColor: COLORS.primary,
        elevation: 1,
    },
    uploadButtonText: {
        color: COLORS.primary,
        ...FONTS.medium,
    } as TextStyle,
    /* Inputs */
    inputGroup: {
        marginBottom: SPACING.md,
    },
    label: {
        marginBottom: SPACING.xs,
        ...FONTS.medium,
        color: COLORS.text.primary,
    } as TextStyle,
    input: {
        backgroundColor: COLORS.white,
        borderRadius: 8,
        padding: SPACING.md,
        color: COLORS.text.primary,
        borderWidth: 1,
        borderColor: COLORS.border.primary,
        fontSize: 16,
    },
    textArea: {
        backgroundColor: COLORS.white,
        borderRadius: 8,
        padding: SPACING.md,
        color: COLORS.text.primary,
        borderWidth: 1,
        borderColor: COLORS.border.primary,
        height: 100,
        textAlignVertical: 'top',
        fontSize: 16,
    },
    inputError: {
        borderColor: COLORS.error,
        borderWidth: 1,
    },
    errorText: {
        color: COLORS.error,
        marginTop: SPACING.xs,
        fontSize: 12,
    },
    formattedDateText: {
        marginTop: SPACING.xs,
        fontSize: 14,
        color: COLORS.text.secondary,
    },
    /* Gender Radio Buttons */
    genderContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: SPACING.xs,
    },
    genderOption: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: SPACING.xs,
    },
    radioOuter: {
        width: 22,
        height: 22,
        borderRadius: 11,
        borderWidth: 2,
        borderColor: COLORS.text.secondary,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: SPACING.xs,
    },
    radioOuterSelected: {
        borderColor: COLORS.primary,
    },
    radioInner: {
        width: 12,
        height: 12,
        borderRadius: 6,
        backgroundColor: COLORS.primary,
    },
    genderText: {
        fontSize: 16,
        color: COLORS.text.primary,
    },
    /* Buttons */
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: SPACING.lg,
        marginBottom: SPACING.xl,
    },
    cancelButton: {
        flex: 1,
        padding: SPACING.md,
        borderRadius: 8,
        backgroundColor: COLORS.background,
        marginRight: SPACING.sm,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: COLORS.border.primary,
        justifyContent: 'center',
    },
    saveButton: {
        flex: 1,
        marginLeft: SPACING.sm,
    },
});
