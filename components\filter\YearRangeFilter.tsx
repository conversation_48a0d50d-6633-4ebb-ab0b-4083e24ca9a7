import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, SPACING, FONTS, BORDERS, SHADOWS } from '@/constants/theme';
import { Slider } from '@miblanchard/react-native-slider';

interface YearRangeFilterProps {
  minYear?: number;
  maxYear?: number;
  onChange: (minYear: number, maxYear: number) => void;
}

const YearRangeFilter: React.FC<YearRangeFilterProps> = ({
  minYear: initialMinYear,
  maxYear: initialMaxYear,
  onChange
}) => {
  // Default year range
  const currentYear = new Date().getFullYear();
  const MIN_YEAR = 1990;
  const MAX_YEAR = currentYear;

  const [minYear, setMinYear] = useState(initialMinYear || MIN_YEAR);
  const [maxYear, setMaxYear] = useState(initialMaxYear || MAX_YEAR);

  // Update local state when props change
  useEffect(() => {
    if (initialMinYear !== undefined) setMinYear(initialMinYear);
    if (initialMaxYear !== undefined) setMaxYear(initialMaxYear);
  }, [initialMinYear, initialMaxYear]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Year Range</Text>

      <View style={styles.rangeLabels}>
        <Text style={styles.rangeLabel}>{minYear}</Text>
        <Text style={styles.rangeLabel}>{maxYear}</Text>
      </View>

      <View style={styles.slider}>
        <Slider
          minimumValue={MIN_YEAR}
          maximumValue={MAX_YEAR}
          step={1}
          value={minYear}
          onValueChange={(value) => setMinYear(Array.isArray(value) ? value[0] : value)}
          onSlidingComplete={(value) => onChange(Array.isArray(value) ? value[0] : value, maxYear)}
          minimumTrackTintColor={COLORS.primary}
          maximumTrackTintColor={COLORS.gray[300]}
          thumbTintColor={COLORS.primary}
          trackClickable={true}
        />
      </View>

      <View style={styles.slider}>
        <Slider
          minimumValue={MIN_YEAR}
          maximumValue={MAX_YEAR}
          step={1}
          value={maxYear}
          onValueChange={(value) => setMaxYear(Array.isArray(value) ? value[0] : value)}
          onSlidingComplete={(value) => onChange(minYear, Array.isArray(value) ? value[0] : value)}
          minimumTrackTintColor={COLORS.primary}
          maximumTrackTintColor={COLORS.gray[300]}
          thumbTintColor={COLORS.primary}
          trackClickable={true}
        />
      </View>

      <View style={styles.rangeLabels}>
        <Text style={styles.rangeHint}>Min</Text>
        <Text style={styles.rangeHint}>Max</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDERS.radius.md,
    ...SHADOWS.xs,
  },
  title: {
    fontWeight: '600',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    marginBottom: SPACING.md,
    letterSpacing: 0.3,
  },
  rangeLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs,
  },
  rangeLabel: {
    fontWeight: '500',
    fontSize: FONTS.size.caption,
    color: COLORS.primary,
  },
  rangeHint: {
    fontWeight: '400',
    fontSize: FONTS.size.small,
    color: COLORS.text.secondary,
  },
  slider: {
    width: '100%',
    height: 40,
    marginVertical: SPACING.xs,
  },
});

export default YearRangeFilter;
