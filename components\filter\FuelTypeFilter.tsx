import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { COLORS, SPACING, FONTS, BORDERS, SHADOWS } from '@/constants/theme';
import { Ionicons } from '@expo/vector-icons';

interface FuelTypeFilterProps {
  selectedFuelTypes: string[];
  onChange: (fuelTypes: string[]) => void;
}

const FuelTypeFilter: React.FC<FuelTypeFilterProps> = ({ selectedFuelTypes, onChange }) => {
  const fuelTypes = [
    { id: 'Petrol', label: 'Petrol' },
    { id: 'Diesel', label: 'Diesel' },
    { id: 'CNG', label: 'CNG' },
    { id: 'Electric', label: 'Electric' },
    { id: 'Hybrid', label: 'Hybrid' },
  ];

  const handleFuelTypeSelect = (fuelType: string) => {
    if (selectedFuelTypes.includes(fuelType)) {
      onChange(selectedFuelTypes.filter(ft => ft !== fuelType));
    } else {
      onChange([...selectedFuelTypes, fuelType]);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Fuel Type</Text>
      <View style={styles.checkboxContainer}>
        {fuelTypes.map((fuelType) => (
          <TouchableOpacity
            key={fuelType.id}
            style={styles.checkboxRow}
            onPress={() => handleFuelTypeSelect(fuelType.id)}
          >
            <View style={styles.checkboxWrapper}>
              <View
                style={[
                  styles.checkbox,
                  selectedFuelTypes.includes(fuelType.id) && styles.checkboxSelected,
                ]}
              >
                {selectedFuelTypes.includes(fuelType.id) && (
                  <Ionicons name="checkmark" size={16} color={COLORS.white} />
                )}
              </View>
            </View>
            <Text style={styles.checkboxLabel}>{fuelType.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDERS.radius.md,
    ...SHADOWS.xs,
  },
  title: {
    fontWeight: '600',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    marginBottom: SPACING.md,
    letterSpacing: 0.3,
  },
  checkboxContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: SPACING.md,
    paddingRight: SPACING.sm,
  },
  checkboxWrapper: {
    marginRight: SPACING.sm,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: BORDERS.radius.sm,
    borderWidth: 2,
    borderColor: COLORS.border.primary,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.surface.secondary,
  },
  checkboxSelected: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
    ...SHADOWS.xs,
  },
  checkboxLabel: {
    fontWeight: '500',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
  },
});

export default FuelTypeFilter;
