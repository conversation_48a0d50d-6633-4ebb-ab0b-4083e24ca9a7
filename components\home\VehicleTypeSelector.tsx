import React, { useRef, useState, useEffect } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Animated,
    LayoutChangeEvent,
} from 'react-native';
import { VehicleType } from '@/store/slices/vehicleSlice';
import CarIcon from '@/assets/icon/car-icon-svg.svg';
import BikeIcon from '@/assets/icon/bike-icon-svg.svg';
import TruckIcon from '@/assets/icon/truck-icon-svg.svg';
import { SPACING } from '@/constants/theme';

// Example colors, update as needed
const DARK_BLUE = '#1B2541';
const ORANGE = '#FF5722';

interface VehicleTypeSelectorProps {
    selectedType: VehicleType;
    onTypeSelect: (type: VehicleType) => void;
}

export const VehicleTypeSelector: React.FC<VehicleTypeSelectorProps> = ({
    selectedType,
    onTypeSelect,
}) => {
    const vehicleTypes = [
        { type: 'car' as VehicleType, icon: CarIcon },
        { type: 'two_wheels' as VehicleType, icon: BikeIcon },
        { type: 'commercial' as VehicleType, icon: TruckIcon },
    ];

    // Store container width to calculate highlight width
    const [containerWidth, setContainerWidth] = useState(0);

    // This animated value represents the index (0, 1, 2) for car, bike, commercial
    const highlightAnim = useRef(
        new Animated.Value(vehicleTypes.findIndex(v => v.type === selectedType))
    ).current;

    // Whenever selectedType changes, animate the highlight to the new index
    useEffect(() => {
        const newIndex = vehicleTypes.findIndex(v => v.type === selectedType);

        // Use spring for a more natural, smoother animation
        Animated.spring(highlightAnim, {
            toValue: newIndex,
            // Adjust these parameters for your preferred “feel”
            speed: 12,           // How fast the spring accelerates
            bounciness: 6,       // Lower bounciness for less overshoot
            useNativeDriver: true, // Ensures smoother animation off the JS thread
        }).start();
    }, [selectedType]);

    // Each segment is containerWidth / vehicleTypes.length
    const highlightWidth = containerWidth / vehicleTypes.length;

    // Translate highlight from 0 to highlightWidth * (index)
    const translateX = highlightAnim.interpolate({
        inputRange: [0, vehicleTypes.length - 1],
        outputRange: [0, highlightWidth * (vehicleTypes.length - 1)],
    });

    return (
        <View
            style={styles.container}
            onLayout={(e: LayoutChangeEvent) => {
                setContainerWidth(e.nativeEvent.layout.width);
            }}
        >
            {/* Sliding highlight (absolute behind icons) */}
            <Animated.View
                style={[
                    styles.highlight,
                    {
                        width: highlightWidth,
                        transform: [{ translateX }],
                    },
                ]}
            />

            {vehicleTypes.map(({ type, icon: Icon }) => (
                <TouchableOpacity
                    key={type}
                    style={styles.typeButton}
                    onPress={() => onTypeSelect(type)}
                    activeOpacity={0.8}
                >
                    <Icon width={28} height={28} fill="#FFFFFF" />
                </TouchableOpacity>
            ))}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        position: 'relative',
        flexDirection: 'row',
        backgroundColor: DARK_BLUE,
        height: 56,
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: 8,
        overflow: 'hidden', // Ensures highlight won't bleed outside
        // Optional horizontal padding if desired
        paddingHorizontal: 16,
        marginHorizontal: SPACING.md,
    },
    highlight: {
        position: 'absolute',
        left: 0,
        top: 0,
        bottom: 0,
        backgroundColor: ORANGE,
    },
    typeButton: {
        flex: 1,
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
    },
});
