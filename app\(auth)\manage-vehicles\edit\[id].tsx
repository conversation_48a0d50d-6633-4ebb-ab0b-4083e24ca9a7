import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  TextInput,
  StyleSheet,
  Alert,
  ActivityIndicator,
  StatusBar,
  Animated,
} from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import * as ImagePicker from 'expo-image-picker';
import { Dropdown } from 'react-native-element-dropdown';
import { useDispatch, useSelector } from 'react-redux';
import { AntDesign, Ionicons } from '@expo/vector-icons';
import { useRouter, useLocalSearchParams } from 'expo-router';
import Constants from 'expo-constants';
import { EditVehicleFormSkeleton } from '@/components/edit/EditVehicleFormSkeleton';

import { AppDispatch, RootState } from '@/store/store';
import {
  fetchVehicleForEdit,
  updateVehicle,
  fetchEditBrands,
  fetchEditModels,
  fetchEditVariants,
  fetchEditStates,
  fetchEditCities,
  clearEditModels,
  clearEditVariants,
  clearEditCities,
  resetEditVehicleState,
} from '@/store/slices/editVehicleSlice';
import { COLORS, FONTS, SPACING } from '@/constants/theme';
import {
  FUEL_TYPES,
  TRANSMISSION_TYPES,
  OWNER_OPTIONS,
  INSURANCE_OPTIONS,
} from '@/constants/commercialVehicle';

interface DropdownItem {
  label: string;
  value: string;
  image?: string;
}

const getValidationSchema = (vehicleType: string) => {
  const baseSchema = {
    title: Yup.string().required('Title is required'),
    brand: Yup.string().required('Brand is required'),
    brandId: Yup.string().required('Brand ID is required'),
    model: Yup.string().required('Model is required'),
    modelId: Yup.string().required('Model ID is required'),
    variant: Yup.string().optional(),
    year: Yup.number()
      .required('Year is required')
      .min(1900, 'Year cannot be before 1900')
      .max(new Date().getFullYear(), 'Year cannot exceed current year')
      .typeError('Year must be a number'),
    fuelType: Yup.string().required('Fuel type is required'),
    // Transmission is conditionally required based on vehicle type
    transmission: Yup.string().when([], {
      is: () => ['car', 'commercial'].includes(vehicleType),
      then: (schema) => schema.required('Transmission is required'),
      otherwise: (schema) => schema.optional(),
    }),
    kmDriven: Yup.number()
      .required('KM Driven is required')
      .min(0, 'KM Driven must be >= 0')
      .typeError('KM Driven must be a number'),
    owners: Yup.string().required('Number of owners is required'),
    insuranceType: Yup.string().required('Insurance type is required'),
    state: Yup.string().required('State is required'),
    stateId: Yup.string().required('State ID is required'),
    city: Yup.string().required('City is required'),
    cityId: Yup.string().required('City ID is required'),
    description: Yup.string().required('Description is required'),
    photos: Yup.array().min(1, 'At least one photo is required'),
    price: Yup.number()
      .required('Price is required')
      .min(0, 'Price must be >= 0')
      .typeError('Price must be a number'),
  };

  // Add vehicle-type specific validation
  if (vehicleType === 'commercial') {
    return Yup.object({
      ...baseSchema,
    });
  }

  if (vehicleType === 'bike') {
    return Yup.object({
      ...baseSchema,
    });
  }

  // For cars - variant is required
  if (vehicleType === 'car') {
    return Yup.object({
      ...baseSchema,
      variant: Yup.string().required('Variant is required for cars'),
      variantId: Yup.string().required('Variant ID is required'),
    });
  }

  // Default for scooter
  return Yup.object({
    ...baseSchema,
    variantId: Yup.string().optional(),
  });
};

export default function EditVehicleScreen() {
  const router = useRouter();
  const { id, type } = useLocalSearchParams<{ id: string; type: string }>();
  const dispatch = useDispatch<AppDispatch>();

  const {
    vehicleData,
    brands,
    models,
    variants,
    states,
    cities,
    loading,
    updating,
    error,
    loadingStates,
  } = useSelector((state: RootState) => state.editVehicle);

  const [initialValues, setInitialValues] = useState({
    title: '',
    brand: '',
    brandId: '',
    model: '',
    modelId: '',
    variant: '',
    variantId: '',
    year: '',
    fuelType: '',
    transmission: '',
    kmDriven: '',
    owners: '',
    insuranceType: '',
    state: '',
    stateId: '',
    city: '',
    cityId: '',
    // Common fields
    description: '',
    photos: [] as string[],
    price: '',
  });

  // Animation for smooth form appearance
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (id && type) {
      dispatch(fetchVehicleForEdit({ vehicleId: id, vehicleType: type }));
      dispatch(fetchEditBrands(type));
      dispatch(fetchEditStates());
    }

    return () => {
      dispatch(resetEditVehicleState());
    };
  }, [dispatch, id, type]);

  // Separate useEffect for setting initial values when vehicleData changes
  useEffect(() => {
    if (vehicleData) {
      // Find brand and model names from the fetched data or use API response values
      const brandItem = brands.find(b => b.value === vehicleData.brand_id?.toString());
      const modelItem = models.find(m => m.value === vehicleData.model_id?.toString());
      const variantItem = variants.find(v => v.value === vehicleData.variant_id?.toString());
      const stateItem = states.find(s => s.value === vehicleData.state_id?.toString());
      const cityItem = cities.find(c => c.value === vehicleData.city_id?.toString());

      const newInitialValues = {
        title: vehicleData.title || '',
        brand: brandItem?.label || vehicleData.brand || '',
        brandId: vehicleData.brand_id?.toString() || '',
        model: modelItem?.label || vehicleData.model || '',
        modelId: vehicleData.model_id?.toString() || '',
        variant: variantItem?.label || vehicleData.variant || '',
        variantId: vehicleData.variant_id?.toString() || '',
        year: vehicleData.year || '',
        fuelType: vehicleData.fuel_type || '',
        transmission: vehicleData.transmission || '',
        kmDriven: vehicleData.kilometers_driven?.toString() || '',
        owners: vehicleData.ownership || '',
        insuranceType: vehicleData.insurance_type || '',
        state: stateItem?.label || vehicleData.state_name || '',
        stateId: vehicleData.state_id?.toString() || '',
        city: cityItem?.label || vehicleData.city_name || '',
        cityId: vehicleData.city_id?.toString() || '',
        // Common fields
        description: vehicleData.description || '',
        photos: vehicleData.Images || vehicleData.images || [],
        price: vehicleData.price || '',
      };

      setInitialValues(newInitialValues);
    }
  }, [vehicleData?.id, vehicleData?.brand_id, vehicleData?.model_id, vehicleData?.variant_id, vehicleData?.state_id, vehicleData?.city_id, brands.length, models.length, variants.length, states.length, cities.length]);

  // Refs to track if we've already fetched data to prevent duplicates
  const hasInitializedModels = useRef(false);
  const hasInitializedVariants = useRef(false);
  const hasInitializedCities = useRef(false);

  // Separate useEffect for fetching dependent data (models, variants, and cities) - simplified to prevent infinite loops
  useEffect(() => {
    if (vehicleData && type) {
      // Only fetch models if we have brand_id and haven't initialized yet
      if (vehicleData.brand_id && !hasInitializedModels.current) {
        hasInitializedModels.current = true;
        dispatch(fetchEditModels({ type: type, brandId: vehicleData.brand_id.toString() }));
      }

      // Only fetch variants if we have model_id and haven't initialized yet (for cars only)
      if (type === 'car' && vehicleData.model_id && !hasInitializedVariants.current) {
        hasInitializedVariants.current = true;
        dispatch(fetchEditVariants(vehicleData.model_id.toString()));
      }

      // Only fetch cities if we have state_id and haven't initialized yet
      if (vehicleData.state_id && !hasInitializedCities.current) {
        hasInitializedCities.current = true;
        dispatch(fetchEditCities(vehicleData.state_id.toString()));
      }
    }
  }, [vehicleData?.brand_id, vehicleData?.model_id, vehicleData?.state_id, type, dispatch]);

  // Reset refs when vehicle data changes
  useEffect(() => {
    if (vehicleData?.id) {
      hasInitializedModels.current = false;
      hasInitializedVariants.current = false;
      hasInitializedCities.current = false;
    }
  }, [vehicleData?.id]);

  // Debounced retry function to prevent rapid clicks
  const lastRetryTime = useRef(0);
  const handleRetry = useCallback(() => {
    const now = Date.now();
    if (now - lastRetryTime.current < 2000) { // 2 second cooldown
      return;
    }
    lastRetryTime.current = now;

    if (id && type) {
      dispatch(fetchVehicleForEdit({ vehicleId: id, vehicleType: type }));
    }
  }, [id, type, dispatch]);



  const pickImage = async (
    currentPhotos: string[],
    setFieldValue: (field: string, value: any, shouldValidate?: boolean) => void
  ) => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (permissionResult.status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        aspect: [4, 3],
        quality: 0.8,
        allowsMultipleSelection: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const newPhotos = [...currentPhotos];
        result.assets.forEach((asset) => {
          newPhotos.push(asset.uri);
        });
        setFieldValue('photos', newPhotos, true);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const renderDropdownItem = (item: DropdownItem) => {
    return (
      <View style={styles.dropdownItem}>
        {item.image && <Image source={{ uri: item.image }} style={styles.brandImage} />}
        <Text style={styles.dropdownItemText}>{item.label}</Text>
      </View>
    );
  };

  // Check form rendering condition
  const shouldRenderForm = vehicleData && brands.length > 0 && states.length > 0;

  // Trigger fade-in animation when form is ready
  useEffect(() => {
    if (shouldRenderForm) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      fadeAnim.setValue(0);
    }
  }, [shouldRenderForm, fadeAnim]);

  // Enhanced loading state with skeleton
  if (loading && !vehicleData) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={COLORS.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Vehicle</Text>
          <View style={{ width: 24 }} />
        </View>
        <EditVehicleFormSkeleton vehicleType={type} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <StatusBar barStyle="light-content" />
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={styles.backButton}
          >
            <Ionicons name="arrow-back" size={24} color={COLORS.white} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Vehicle</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.centerContainer}>
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle-outline" size={64} color={COLORS.error} />
            <Text style={styles.errorTitle}>Oops! Something went wrong</Text>
            <Text style={styles.errorText}>
              {typeof error === 'string' ? error : 'Failed to load vehicle details'}
            </Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={handleRetry}
            >
              <Ionicons name="refresh" size={20} color={COLORS.white} style={{ marginRight: 8 }} />
              <Text style={styles.retryButtonText}>Try Again</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color={COLORS.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Vehicle</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.scrollContainer}>
        <Text style={styles.title}>
          Edit {type ? type.charAt(0).toUpperCase() + type.slice(1) : 'Vehicle'} Details
        </Text>

        {/* Only render form when we have vehicle data and basic dropdown data */}
        {shouldRenderForm ? (
          <Animated.View style={{ opacity: fadeAnim }}>
            <Formik
              initialValues={initialValues}
              validationSchema={getValidationSchema(type || 'car')}
              enableReinitialize={true}
              validateOnChange={true}
              validateOnBlur={true}
              onSubmit={async (values, { setSubmitting }) => {
                try {
                  const formattedData: any = {
                    title: values.title,
                    brand: values.brand,
                    brand_id: values.brandId,
                    model: values.model,
                    model_id: values.modelId,
                    year: Number(values.year),
                    fuel_type: values.fuelType,
                    kilometers_driven: Number(values.kmDriven),
                    price: Number(values.price),
                    ownership: values.owners,
                    insurance_type: values.insuranceType,
                    country: 'India',
                    state: values.state,
                    state_id: values.stateId,
                    city: values.city,
                    city_id: values.cityId,
                    location: `${values.city}, ${values.state}, India`,
                    description: values.description,
                    images: values.photos.map((photo, index) => {
                      if (typeof photo === 'string' && photo.startsWith('http')) {
                        return photo; // Existing image URL
                      }
                      return {
                        uri: photo,
                        type: 'image/jpeg',
                        name: `photo${index + 1}.jpg`,
                      };
                    }),
                  };

                  // Only include transmission for car and commercial vehicle types
                  if (['car', 'commercial'].includes(type || 'car')) {
                    formattedData.transmission = values.transmission;
                  }

                  // Only include variant and variant_id for cars
                  if (type === 'car') {
                    formattedData.variant = values.variant;
                    formattedData.variant_id = values.variantId || null;
                  }

                  // Add vehicle-type specific fields
                  // No longer including bike-specific fields: engine_capacity, color, bike_condition, registration_number

                  if (id && type) {
                    await dispatch(updateVehicle({
                      vehicleId: id,
                      vehicleType: type,
                      formData: formattedData
                    })).unwrap();

                    Alert.alert('Success', 'Vehicle updated successfully!', [
                      { text: 'OK', onPress: () => router.back() }
                    ]);
                  }
                } catch (error: any) {
                  console.error('Update error:', error);
                  Alert.alert('Error', error?.message || 'Failed to update vehicle. Please try again.');
                } finally {
                  setSubmitting(false);
                }
              }}
            >
              {({
                handleChange,
                handleBlur,
                handleSubmit,
                values,
                errors,
                touched,
                setFieldValue,
                setFieldTouched,
                validateForm,
                isSubmitting,
              }) => {
                return (
                  <View style={styles.form}>
                    {/* Brand Selection */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        Select Type<Text style={styles.required}> *</Text>
                      </Text>
                      <Dropdown
                        style={[styles.dropdown, touched.brand && errors.brand && styles.dropdownError]}
                        placeholderStyle={styles.placeholderStyle}
                        selectedTextStyle={styles.selectedTextStyle}
                        data={brands}
                        search
                        searchPlaceholder="Search..."
                        labelField="label"
                        valueField="value"
                        placeholder={brands.length > 0 ? "Select type" : "Loading brands..."}
                        value={values.brandId}
                        renderItem={renderDropdownItem}
                        disable={loadingStates.brands}
                        onChange={(item) => {
                          setFieldValue('brand', item.label, false);
                          setFieldValue('brandId', item.value, false);
                          setFieldTouched('brand', true, false);
                          setFieldTouched('brandId', true, false);
                          // Reset model and variant when brand changes
                          setFieldValue('model', '', true);
                          setFieldValue('modelId', '', true);
                          setFieldValue('variant', '', true);
                          setFieldValue('variantId', '', true);
                          dispatch(clearEditModels());
                          dispatch(clearEditVariants());

                          // Fetch models for the selected brand
                          if (!loadingStates.models) {
                            dispatch(fetchEditModels({ type: type || 'car', brandId: item.value }));
                          }
                          setTimeout(() => validateForm(), 0);
                        }}
                        renderLeftIcon={() => (
                          <AntDesign style={styles.icon} color="black" name="Safety" size={20} />
                        )}
                      />
                      {touched.brand && errors.brand && <Text style={styles.error}>Type is required</Text>}
                    </View>

                    {/* Model Selection */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        Select Brand<Text style={styles.required}> *</Text>
                      </Text>
                      <Dropdown
                        style={[styles.dropdown, touched.model && errors.model && styles.dropdownError]}
                        placeholderStyle={styles.placeholderStyle}
                        selectedTextStyle={styles.selectedTextStyle}
                        data={models}
                        search
                        searchPlaceholder="Search..."
                        labelField="label"
                        valueField="value"
                        placeholder={models.length > 0 ? "Select brand" : (loadingStates.models ? "Loading models..." : "Select type first")}
                        value={values.modelId}
                        renderItem={renderDropdownItem}
                        disable={loadingStates.models || !values.brandId}
                        onChange={(item) => {
                          setFieldValue('model', item.label, false);
                          setFieldValue('modelId', item.value, false);
                          setFieldTouched('model', true, false);
                          setFieldTouched('modelId', true, false);
                          // Reset variant when model changes
                          setFieldValue('variant', '', true);
                          setFieldValue('variantId', '', true);
                          if (type === 'car') {
                            dispatch(clearEditVariants());
                            dispatch(fetchEditVariants(item.value));
                          }
                          setTimeout(() => validateForm(), 0);
                        }}
                        renderLeftIcon={() => (
                          <AntDesign style={styles.icon} color="black" name="Safety" size={20} />
                        )}
                      />
                      {touched.model && errors.model && <Text style={styles.error}>Brand is required</Text>}
                    </View>

                    {/* Variant (only for Car) */}
                    {type === 'car' && (
                      <View style={styles.fieldContainer}>
                        <Text style={styles.label}>
                          Select Variant<Text style={styles.required}> *</Text>
                        </Text>
                        <Dropdown
                          style={[
                            styles.dropdown,
                            touched.variant && errors.variant && styles.dropdownError,
                          ]}
                          placeholderStyle={styles.placeholderStyle}
                          selectedTextStyle={styles.selectedTextStyle}
                          data={variants}
                          search
                          searchPlaceholder="Search..."
                          labelField="label"
                          valueField="value"
                          placeholder={loadingStates.variants ? "Loading variants..." : (values.modelId ? "Select variant" : "Select brand first")}
                          value={values.variantId}
                          renderItem={renderDropdownItem}
                          disable={loadingStates.variants || !values.modelId}
                          onChange={(item) => {
                            setFieldValue("variant", item.label, false);
                            setFieldValue("variantId", item.value, false);
                            setFieldTouched("variant", true, false);
                            setFieldTouched("variantId", true, false);
                            // Validate after state updates
                            setTimeout(() => validateForm(), 0);
                          }}
                          renderLeftIcon={() => (
                            <AntDesign style={styles.icon} color="black" name="Safety" size={20} />
                          )}
                          // This prop will render when the data array is empty
                          flatListProps={{
                            ListEmptyComponent: (
                              <View style={{
                                padding: 10,
                                alignItems: 'center',
                              }}>
                                <Text style={{
                                  color: 'gray',
                                  fontSize: 16,
                                }}>No data found</Text>
                              </View>
                            ),
                          }}
                        />
                        {touched.variant && errors.variant && <Text style={styles.error}>{errors.variant}</Text>}
                      </View>
                    )}

                    {/* Year */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        Select Year<Text style={styles.required}> *</Text>
                      </Text>
                      <TextInput
                        style={[styles.input, touched.year && errors.year && styles.inputError]}
                        onChangeText={(text) => {
                          setFieldValue('year', text, true);
                        }}
                        onBlur={handleBlur('year')}
                        value={values.year}
                        keyboardType="numeric"
                        placeholder="e.g. 2022"
                      />
                      {touched.year && errors.year && <Text style={styles.error}>{errors.year}</Text>}
                    </View>

                    {/* Fuel Type */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        Select Fuel<Text style={styles.required}> *</Text>
                      </Text>
                      <View style={styles.chipContainer}>
                        {FUEL_TYPES.map((fuel) => {
                          const selected = values.fuelType === fuel;
                          return (
                            <TouchableOpacity
                              key={fuel}
                              style={[styles.chip, selected && styles.chipSelected]}
                              onPress={() => {
                                setFieldValue('fuelType', fuel, false);
                                setFieldTouched('fuelType', true, false);
                                setTimeout(() => validateForm(), 0);
                              }}
                            >
                              <Text style={[styles.chipText, selected && styles.chipTextSelected]}>
                                {fuel}
                              </Text>
                            </TouchableOpacity>
                          );
                        })}
                      </View>
                      {touched.fuelType && errors.fuelType && <Text style={styles.error}>{errors.fuelType}</Text>}
                    </View>

                    {/* Transmission (only for Car and Commercial) */}
                    {['car', 'commercial'].includes(type || 'car') && (
                      <View style={styles.fieldContainer}>
                        <Text style={styles.label}>
                          Select Transmission<Text style={styles.required}> *</Text>
                        </Text>
                        <View style={styles.chipContainer}>
                          {TRANSMISSION_TYPES.map((trans) => {
                            const selected = values.transmission === trans;
                            return (
                              <TouchableOpacity
                                key={trans}
                                style={[styles.chip, selected && styles.chipSelected]}
                                onPress={() => {
                                  setFieldValue('transmission', trans, false);
                                  setFieldTouched('transmission', true, false);
                                  setTimeout(() => validateForm(), 0);
                                }}
                              >
                                <Text style={[styles.chipText, selected && styles.chipTextSelected]}>
                                  {trans}
                                </Text>
                              </TouchableOpacity>
                            );
                          })}
                        </View>
                        {touched.transmission && errors.transmission && (
                          <Text style={styles.error}>{errors.transmission}</Text>
                        )}
                      </View>
                    )}

                    {/* KM Driven */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        KM Driven<Text style={styles.required}> *</Text>
                      </Text>
                      <TextInput
                        style={[styles.input, touched.kmDriven && errors.kmDriven && styles.inputError]}
                        onChangeText={(text) => {
                          setFieldValue('kmDriven', text, true);
                        }}
                        onBlur={handleBlur('kmDriven')}
                        value={values.kmDriven}
                        keyboardType="numeric"
                        placeholder="Enter KM"
                      />
                      {touched.kmDriven && errors.kmDriven && <Text style={styles.error}>{errors.kmDriven}</Text>}
                    </View>

                    {/* Number of Owners */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        No. of Owners<Text style={styles.required}> *</Text>
                      </Text>
                      <View style={styles.chipContainer}>
                        {OWNER_OPTIONS.map((owner) => {
                          const selected = values.owners === owner;
                          return (
                            <TouchableOpacity
                              key={owner}
                              style={[styles.chip, selected && styles.chipSelected]}
                              onPress={() => {
                                setFieldValue('owners', owner, false);
                                setFieldTouched('owners', true, false);
                                setTimeout(() => validateForm(), 0);
                              }}
                            >
                              <Text style={[styles.chipText, selected && styles.chipTextSelected]}>
                                {owner}
                              </Text>
                            </TouchableOpacity>
                          );
                        })}
                      </View>
                      {touched.owners && errors.owners && <Text style={styles.error}>{errors.owners}</Text>}
                    </View>

                    {/* Insurance Type */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        Insurance Type<Text style={styles.required}> *</Text>
                      </Text>
                      <View style={styles.chipContainer}>
                        {INSURANCE_OPTIONS.map((insurance) => {
                          const selected = values.insuranceType === insurance.key;
                          return (
                            <TouchableOpacity
                              key={insurance.key}
                              style={[styles.chip, selected && styles.chipSelected]}
                              onPress={() => {
                                setFieldValue('insuranceType', insurance.key, false);
                                setFieldTouched('insuranceType', true, false);
                                setTimeout(() => validateForm(), 0);
                              }}
                            >
                              <Text style={[styles.chipText, selected && styles.chipTextSelected]}>
                                {insurance.name}
                              </Text>
                            </TouchableOpacity>
                          );
                        })}
                      </View>
                      {touched.insuranceType && errors.insuranceType && <Text style={styles.error}>{errors.insuranceType}</Text>}
                    </View>

                    {/* State Selection */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        Select State<Text style={styles.required}> *</Text>
                      </Text>
                      <Dropdown
                        style={[styles.dropdown, touched.state && errors.state && styles.dropdownError]}
                        placeholderStyle={styles.placeholderStyle}
                        selectedTextStyle={styles.selectedTextStyle}
                        data={states}
                        search
                        searchPlaceholder="Search..."
                        labelField="label"
                        valueField="value"
                        placeholder={loadingStates.states ? "Loading states..." : "Select state"}
                        value={values.stateId}
                        renderItem={renderDropdownItem}
                        disable={loadingStates.states}
                        onChange={(item) => {
                          setFieldValue('state', item.label, false);
                          setFieldValue('stateId', item.value, false);
                          setFieldTouched('state', true, false);
                          setFieldTouched('stateId', true, false);
                          // Reset city when state changes
                          setFieldValue('city', '', true);
                          setFieldValue('cityId', '', true);
                          dispatch(clearEditCities());

                          // Fetch cities for the selected state
                          if (!loadingStates.cities) {
                            dispatch(fetchEditCities(item.value));
                          }
                          setTimeout(() => validateForm(), 0);
                        }}
                        renderLeftIcon={() => (
                          <AntDesign style={styles.icon} color="black" name="enviromento" size={20} />
                        )}
                      />
                      {touched.state && errors.state && <Text style={styles.error}>{errors.state}</Text>}
                    </View>

                    {/* City Selection */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        Select City<Text style={styles.required}> *</Text>
                      </Text>
                      <Dropdown
                        style={[styles.dropdown, touched.city && errors.city && styles.dropdownError]}
                        placeholderStyle={styles.placeholderStyle}
                        selectedTextStyle={styles.selectedTextStyle}
                        data={cities}
                        search
                        searchPlaceholder="Search..."
                        labelField="label"
                        valueField="value"
                        placeholder={loadingStates.cities ? "Loading cities..." : (values.stateId ? "Select city" : "Select state first")}
                        value={values.cityId}
                        renderItem={renderDropdownItem}
                        disable={loadingStates.cities || !values.stateId}
                        onChange={(item) => {
                          setFieldValue('city', item.label, false);
                          setFieldValue('cityId', item.value, false);
                          setFieldTouched('city', true, false);
                          setFieldTouched('cityId', true, false);
                          setTimeout(() => validateForm(), 0);
                        }}
                        renderLeftIcon={() => (
                          <AntDesign style={styles.icon} color="black" name="enviromento" size={20} />
                        )}
                      />
                      {touched.city && errors.city && <Text style={styles.error}>{errors.city}</Text>}
                    </View>

                    {/* Title */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        Title<Text style={styles.required}> *</Text>
                      </Text>
                      <TextInput
                        style={[styles.input, touched.title && errors.title && styles.inputError]}
                        onChangeText={handleChange('title')}
                        onBlur={handleBlur('title')}
                        value={values.title}
                        placeholder="Enter a title for your listing"
                      />
                      {touched.title && errors.title && <Text style={styles.error}>{errors.title}</Text>}
                    </View>

                    {/* Description */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        Add Description<Text style={styles.required}> *</Text>
                      </Text>
                      <TextInput
                        style={[
                          styles.input,
                          styles.textArea,
                          touched.description && errors.description && styles.inputError,
                        ]}
                        onChangeText={handleChange('description')}
                        onBlur={handleBlur('description')}
                        value={values.description}
                        multiline
                        numberOfLines={4}
                        placeholder="Mention the key features of your vehicle (e.g. brand, model, age, type)"
                      />
                      {touched.description && errors.description && (
                        <Text style={styles.error}>{errors.description}</Text>
                      )}
                    </View>

                    {/* Photos */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        Vehicle Photos<Text style={styles.required}> *</Text>
                      </Text>
                      <View style={styles.photoGrid}>
                        {values.photos.map((uri, index) => (
                          <View key={index} style={styles.photoContainer}>
                            <Image source={{ uri }} style={styles.selectedPhoto} />
                            {/* Cover Image Label */}
                            {index === 0 && (
                              <View style={styles.coverLabel}>
                                <Text style={styles.coverLabelText}>Cover</Text>
                              </View>
                            )}
                            {/* Remove Icon */}
                            <TouchableOpacity
                              style={styles.removeIcon}
                              onPress={() => {
                                const newPhotos = values.photos.filter((_, i) => i !== index);
                                setFieldValue('photos', newPhotos, true);
                              }}
                            >
                              <AntDesign name="closecircle" size={20} color="red" />
                            </TouchableOpacity>
                          </View>
                        ))}
                        <TouchableOpacity
                          style={styles.photoButton}
                          onPress={() => pickImage(values.photos, setFieldValue)}
                        >
                          <AntDesign name="plus" size={24} color={COLORS.primary} />
                        </TouchableOpacity>
                      </View>
                      {touched.photos && errors.photos && (
                        <Text style={styles.error}>{errors.photos}</Text>
                      )}
                    </View>

                    {/* Price */}
                    <View style={styles.fieldContainer}>
                      <Text style={styles.label}>
                        Set Price<Text style={styles.required}> *</Text>
                      </Text>
                      <View style={styles.priceContainer}>
                        <Text style={styles.currencySymbol}>₹</Text>
                        <TextInput
                          style={[styles.priceInput, touched.price && errors.price && styles.inputError]}
                          onChangeText={(text) => {
                            setFieldValue('price', text, true);
                          }}
                          onBlur={handleBlur('price')}
                          value={values.price}
                          keyboardType="numeric"
                          placeholder="Enter Price"
                        />
                      </View>
                      {touched.price && errors.price && <Text style={styles.error}>{errors.price}</Text>}
                    </View>

                    {/* Submit Button */}
                    <TouchableOpacity
                      style={[styles.submitBtn, (updating || isSubmitting) && styles.submitBtnDisabled]}
                      onPress={() => {
                        validateForm().then(errors => {
                          // If there are validation errors, mark all fields as touched to show errors
                          if (Object.keys(errors).length > 0) {
                            Object.keys(values).forEach(fieldName => {
                              setFieldTouched(fieldName, true, false);
                            });
                            setTimeout(() => validateForm(), 0);
                          }
                          handleSubmit();
                        });
                      }}
                      disabled={updating || isSubmitting}
                    >
                      <Text style={styles.submitBtnText}>
                        {updating || isSubmitting ? 'Updating...' : 'Update Vehicle'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                );
              }}
            </Formik>
          </Animated.View>
        ) : (
          <View style={styles.container}>
            <View style={styles.progressContainer}>
              <View style={styles.progressHeader}>
                <ActivityIndicator size="small" color={COLORS.primary} />
                <Text style={styles.progressText}>
                  {!vehicleData ? 'Loading vehicle data...' :
                    brands.length === 0 ? 'Loading brands...' :
                      states.length === 0 ? 'Loading states...' :
                        'Preparing form...'}
                </Text>
              </View>
              <View style={styles.progressBar}>
                <View style={[
                  styles.progressFill,
                  {
                    width: vehicleData ?
                      (brands.length > 0 ?
                        (states.length > 0 ? '100%' : '75%')
                        : '50%')
                      : '25%'
                  }
                ]} />
              </View>
            </View>
            <EditVehicleFormSkeleton vehicleType={type} />
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#eaeaea',
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#eaeaea',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 16 + Constants.statusBarHeight,
    backgroundColor: '#1E2A47',
    paddingHorizontal: 16,
    paddingVertical: 14,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  backButton: {
    padding: SPACING.xs,
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 20,
    ...FONTS.medium,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: 16,
    color: COLORS.text.secondary,
  },
  errorContainer: {
    alignItems: 'center',
    padding: SPACING.xl,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.text.primary,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  errorText: {
    color: COLORS.text.secondary,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: SPACING.lg,
    lineHeight: 22,
  },
  retryButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  retryButtonText: {
    color: COLORS.white,
    fontWeight: 'bold',
    fontSize: 16,
  },
  progressContainer: {
    backgroundColor: COLORS.white,
    margin: SPACING.md,
    padding: SPACING.md,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  progressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  progressText: {
    marginLeft: SPACING.sm,
    fontSize: 14,
    color: COLORS.text.secondary,
    fontWeight: '500',
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.primary,
    borderRadius: 2,
  },
  scrollContainer: {
    flex: 1,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 20,
    color: '#333',
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  form: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  fieldContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 15,
    fontWeight: '600',
    color: '#555',
    marginBottom: 8,
  },
  required: {
    color: 'red',
  },
  dropdown: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: '#fff',
    height: 50,
    justifyContent: 'center',
  },
  dropdownError: {
    borderColor: 'red',
  },
  placeholderStyle: {
    color: '#999',
    fontSize: 14,
  },
  selectedTextStyle: {
    fontSize: 14,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    backgroundColor: '#fff',
  },
  inputError: {
    borderColor: 'red',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: COLORS.white,
    borderWidth: 0.2,
  },
  chipSelected: {
    backgroundColor: '#F9E8DB',
    borderColor: '#FC6316',
  },
  chipText: {
    color: COLORS.black,
    fontSize: 14,
  },
  chipTextSelected: {
    color: COLORS.black,
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 8,
    alignItems: 'center',
  },
  photoButton: {
    width: 100,
    height: 100,
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: '#ddd',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedPhoto: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  photoContainer: {
    position: 'relative',
    width: 100,
    height: 100,
  },
  coverLabel: {
    position: 'absolute',
    bottom: 4,
    left: 4,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  coverLabelText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  removeIcon: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'white',
    borderRadius: 10,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currencySymbol: {
    fontSize: 18,
    marginRight: 6,
  },
  priceInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    backgroundColor: '#fff',
  },
  submitBtn: {
    backgroundColor: COLORS.primary,
    paddingVertical: 14,
    borderRadius: 8,
    marginTop: 16,
    alignItems: 'center',
  },
  submitBtnText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  submitBtnDisabled: {
    backgroundColor: '#ccc',
  },
  error: {
    color: 'red',
    marginTop: 4,
    fontSize: 12,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
  },
  brandImage: {
    width: 30,
    height: 30,
    marginRight: 10,
    resizeMode: 'contain',
  },
  dropdownItemText: {
    fontSize: 14,
    color: COLORS.primary,
  },
  icon: {
    marginRight: 5,
  },
});
