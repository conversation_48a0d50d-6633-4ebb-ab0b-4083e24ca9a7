import React, { useRef, useCallback, memo } from 'react';
import { View, StyleSheet, TextInput, Keyboard, Platform } from 'react-native';
import { COLORS } from '@/constants/theme';

interface OTPInputProps {
    otp: string;
    setOtp: (otp: string) => void;
    length?: number;
    disabled?: boolean;
    autoFocus?: boolean;
}

const OTPInput: React.FC<OTPInputProps> = ({
    otp,
    setOtp,
    length = 6,
    disabled = false,
    autoFocus = true,
}) => {
    const inputRefs = useRef<Array<TextInput | null>>([]);

    // Validate and clean input value
    const sanitizeInput = useCallback((value: string): string => {
        return value.replace(/[^0-9]/g, '').slice(0, length);
    }, [length]);

    // Handle paste functionality
    const handlePaste = useCallback((pastedText: string) => {
        const sanitizedText = sanitizeInput(pastedText);
        if (sanitizedText) {
            setOtp(sanitizedText.padEnd(length, ''));
            Keyboard.dismiss();
        }
    }, [length, setOtp, sanitizeInput]);

    const handleOtpChange = useCallback((value: string, index: number) => {
        // Handle paste or multiple character input
        if (value.length > 1) {
            handlePaste(value);
            return;
        }

        // Handle single character input
        if (value.match(/^[0-9]?$/)) {
            const newOtp = otp.split('');
            newOtp[index] = value;
            const newOtpString = newOtp.join('');
            setOtp(newOtpString);

            if (value && index < length - 1) {
                inputRefs.current[index + 1]?.focus();
            } else if (index === length - 1 && newOtpString.length === length) {
                Keyboard.dismiss();
            }
        }
    }, [otp, length, setOtp, handlePaste]);

    const handleKeyPress = useCallback(({ nativeEvent: { key } }: { nativeEvent: { key: string } }, index: number) => {
        if (key === 'Backspace') {
            const newOtp = otp.split('');

            if (!newOtp[index] && index > 0) {
                // Move to previous input if current is empty
                newOtp[index - 1] = '';
                setOtp(newOtp.join(''));
                inputRefs.current[index - 1]?.focus();
            } else {
                // Clear current input
                newOtp[index] = '';
                setOtp(newOtp.join(''));
            }
        }
    }, [otp, setOtp]);

    const handleFocus = useCallback((index: number) => {
        // On Android, move cursor to the end of input
        if (Platform.OS === 'android') {
            inputRefs.current[index]?.setNativeProps({
                selection: { start: 1, end: 1 }
            });
        }
    }, []);

    return (
        <View style={styles.otpContainer}>
            {Array(length).fill(0).map((_, index) => (
                <TextInput
                    key={index}
                    ref={el => inputRefs.current[index] = el}
                    style={[
                        styles.otpInput,
                        otp[index] ? styles.otpInputFilled : null,
                        disabled ? styles.otpInputDisabled : null,
                    ]}
                    value={otp[index] || ''}
                    onChangeText={value => handleOtpChange(value, index)}
                    onKeyPress={e => handleKeyPress(e, index)}
                    onFocus={() => handleFocus(index)}
                    keyboardType="number-pad"
                    maxLength={1}
                    selectTextOnFocus
                    selectionColor={COLORS.primary}
                    editable={!disabled}
                    autoFocus={autoFocus && index === 0}
                    accessible={true}
                    accessibilityLabel={`OTP digit ${index + 1} of ${length}`}
                    accessibilityHint="Enter a single digit"
                    textContentType={Platform.OS === 'ios' ? 'oneTimeCode' : undefined}
                />
            ))}
        </View>
    );
};

const styles = StyleSheet.create({
    otpContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        marginVertical: 24,
        paddingHorizontal: 16,
        gap: 8,
    },
    otpInput: {
        flex: 1,
        aspectRatio: 1,
        borderRadius: 12,
        backgroundColor: COLORS.white,
        textAlign: 'center',
        fontSize: 20,
        borderWidth: 1,
        borderColor: COLORS.text.primary,
        padding: 0,
        minHeight: 48,
    },
    otpInputFilled: {
        borderColor: COLORS.primary,
        backgroundColor: COLORS.white,
    },
    otpInputDisabled: {
        opacity: 0.5,
        backgroundColor: COLORS.gray[200],
    },
});

export default memo(OTPInput);