import React, { useState, useEffect } from 'react';
import {
    Alert,
    Dimensions,
    Image,
    ImageStyle,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    View,
    TextInput,
    ActivityIndicator,
    TouchableOpacity,
    ViewStyle
} from 'react-native';
import EmailForm from '@/components/auth/EmailForm';
import ForgotPasswordForm from '@/components/auth/ForgotPasswordForm';
import LoginForm from '@/components/auth/LoginForm';
import RegisterForm from '@/components/auth/RegisterForm';
import ResetPasswordForm from '@/components/auth/ResetPasswordForm';
import VerifyForgotPasswordOtpForm from '@/components/auth/VerifyForgotPasswordOtpForm';
import VerifyOtpForm from '@/components/auth/VerifyOtpForm';
import LoadingIndicator from '@/components/common/LoadingIndicator';
import ThemedButton from '@/components/common/ThemedButton';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING } from '@/constants/theme';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
    checkEmail,
    forgotPassword,
    login,
    register,
    resetPassword,
    verifyForgotPasswordOtp,
    verifyOtp,
    registerWithGoogle,
    registerWithPhone
} from '@/store/slices/authSlice';
import { useRouter } from 'expo-router';
import * as Google from 'expo-auth-session/providers/google';
import * as WebBrowser from 'expo-web-browser';
import { makeRedirectUri, exchangeCodeAsync } from 'expo-auth-session';
import { GoogleAuthProvider, signInWithCredential } from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { auth } from '../../config/firebase'; // adjust the path as needed
import { Ionicons } from '@expo/vector-icons'; // Add this import
import * as Facebook from 'expo-auth-session/providers/facebook';
import { FacebookAuthProvider } from 'firebase/auth';

// Complete any in-progress auth session (important for web)
WebBrowser.maybeCompleteAuthSession();

// Replace the existing BackButton component with this enhanced version
const BackButton = ({ onPress }: { onPress: () => void }) => (
    <TouchableOpacity
        onPress={onPress}
        style={{
            position: 'absolute',
            left: SPACING.lg,
            top: Platform.OS === 'ios' ? 50 : 50,
            zIndex: 1,
            padding: SPACING.xs,
            backgroundColor: COLORS.background,
            borderRadius: 20,
            elevation: 2,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.2,
            shadowRadius: 2,
        }}
    >
        <Ionicons name="arrow-back" size={24} color={COLORS.text.primary} />
    </TouchableOpacity>
);

// First, create a helper function to get the client ID
const getClientId = () => {
    const clientId = Platform.select({
        ios: process.env.EXPO_PUBLIC_IOS_CLIENT_ID,
        android: process.env.EXPO_PUBLIC_ANDROID_CLIENT_ID,
        default: process.env.EXPO_PUBLIC_WEB_CLIENT_ID
    });

    if (!clientId) {
        throw new Error('Client ID not configured for this platform');
    }

    return clientId;
};

export default function RegisterScreen() {
    const router = useRouter();
    const dispatch = useAppDispatch();
    const { loading } = useAppSelector((state) => state.auth);

    // Existing states for email flow
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [otp, setOtp] = useState('');
    const [step, setStep] = useState<
        'email' | 'login' | 'register' | 'verify' | 'forgotPassword' | 'verifyForgotPassword' | 'resetPassword'
    >('email');
    const [loader, setLoader] = useState(false);

    // States for Google sign‑in and phone verification
    const [phoneRequired, setPhoneRequired] = useState(false);
    const [pendingUid, setPendingUid] = useState<string | null>(null);
    const [phone, setPhone] = useState('');

    // Add these new states after your existing states
    const [googleLoading, setGoogleLoading] = useState(false);
    const [facebookLoading, setFacebookLoading] = useState(false);

    // Configure the Google auth request.
    const [request, response, promptAsync] = Google.useAuthRequest({
        webClientId: process.env.EXPO_PUBLIC_WEB_CLIENT_ID,
        androidClientId: process.env.EXPO_PUBLIC_ANDROID_CLIENT_ID,
        iosClientId: process.env.EXPO_PUBLIC_IOS_CLIENT_ID,
        responseType: Platform.select({ web: 'token', default: 'code' }),
        redirectUri: makeRedirectUri({
            scheme: 'com.factcoding.secondcar',
            path: ''
        }),
        scopes: ['profile', 'email']
    });

    // Add Facebook configuration after Google configuration
    const [fbRequest, fbResponse, promptFacebookAsync] = Facebook.useAuthRequest({
        clientId: process.env.EXPO_PUBLIC_FACEBOOK_APP_ID || '',
        androidClientId: process.env.EXPO_PUBLIC_FACEBOOK_ANDROID_CLIENT_ID,
        iosClientId: process.env.EXPO_PUBLIC_FACEBOOK_IOS_CLIENT_ID,
    });

    // Listen for changes in the Google auth response.
    useEffect(() => {
        if (response?.type === 'success') {
            handleAuthResponse(response);
        } else if (response?.type === 'error') {
            Alert.alert('Authentication Error', response.error?.message || 'Failed to authenticate with Google');
        }
    }, [response]);

    // ----- Email/Password Flow Functions -----

    const handleEmailSubmit = async (email: string) => {
        setLoader(true);
        try {
            setEmail(email);
            const result = await dispatch(checkEmail(email)).unwrap();
            if (result.status) {
                setStep('login');
            } else {
                setStep('register');
            }
        } catch (error: any) {
            if (error.message === 'User not found') {
                setStep('register');
            } else {
                Alert.alert('Error', error.message || 'Failed to check email');
            }
        } finally {
            setLoader(false);
        }
    };

    const handleLogin = async () => {
        if (!password) {
            Alert.alert('Error', 'Please enter your password');
            return;
        }
        try {
            setLoader(true);
            const result = await dispatch(login({ email, password })).unwrap();
            if (result.status) {
                router.replace('/(auth)/(tabs)');
            } else {
                Alert.alert('Error', result.message);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Login failed');
        } finally {
            setLoader(false);
        }
    };

    const handleRegister = async (values: { name: string; phone: string; password: string }) => {
        try {
            setLoader(true);
            const result = await dispatch(
                register({
                    email,
                    name: values.name,
                    phone: values.phone,
                    password: values.password
                })
            ).unwrap();
            if (result.status) {
                setStep('verify');
            } else {
                Alert.alert('Error', result.message);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Registration failed');
        } finally {
            setLoader(false);
        }
    };

    const handleVerifyOtp = async () => {
        if (!otp) {
            Alert.alert('Error', 'Please enter the OTP');
            return;
        }
        try {
            setLoader(true);
            const result = await dispatch(verifyOtp({ email, otp })).unwrap();
            if (result.status) {
                router.replace('/(auth)/(tabs)');
            } else {
                Alert.alert('Error', result.message);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'OTP verification failed');
        } finally {
            setLoader(false);
        }
    };

    const handleForgotPassword = async () => {
        try {
            setLoader(true);
            const result = await dispatch(forgotPassword(email)).unwrap();
            if (result.status) {
                setStep('verifyForgotPassword');
            } else {
                Alert.alert('Error', result.message);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Failed to send OTP');
        } finally {
            setLoader(false);
        }
    };

    const handleVerifyForgotPasswordOtp = async () => {
        try {
            setLoader(true);
            const result = await dispatch(verifyForgotPasswordOtp({ email, otp })).unwrap();
            if (result.status) {
                setStep('resetPassword');
            } else {
                Alert.alert('Error', result.message);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'OTP verification failed');
        } finally {
            setLoader(false);
        }
    };

    const handleResetPassword = async (values: { password: string; confirmPassword: string }) => {
        if (values.password !== values.confirmPassword) {
            Alert.alert('Error', 'Passwords do not match');
            return;
        }
        try {
            setLoader(true);
            const result = await dispatch(
                resetPassword({
                    email,
                    newPassword: values.password,
                    otp
                })
            ).unwrap();
            if (result.status) {
                router.replace('/(auth)/(tabs)');
            } else {
                Alert.alert('Error', result.message);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Password reset failed');
        } finally {
            setLoader(false);
        }
    };

    // ----- Google Sign-In & Phone Verification Functions -----

    const registerUserFlow = async (uid: string) => {
        try {
            const result = await dispatch(registerWithGoogle(uid)).unwrap();
            if (result.user?.status === '0' || !result.authorisation?.token) {
                setPendingUid(uid);
                // Navigate to the Phone Input Screen and pass pendingUid as a parameter
                router.push(`/auth/phonenumber?pendingUid=${uid}`);
                return null;
            }
            if (result.status && result.authorisation?.token) {
                await AsyncStorage.setItem('token', result.authorisation.token);
                await AsyncStorage.setItem('user', JSON.stringify(result.user));
                return result;
            }
            return null;
        } catch (error: any) {
            if (error.requiresPhone) {
                setPendingUid(uid);
                router.push(`/auth/phonenumber?pendingUid=${uid}`);
                return null;
            }
            throw error;
        }
    };


    const handleAuthResponse = async (response: any) => {
        try {
            setLoader(true);
            let idToken = null;
            let accessToken = null;

            // If tokens are returned directly (web)
            if (response.params.id_token) {
                idToken = response.params.id_token;
                accessToken = response.params.access_token;
            }
            // Otherwise, exchange the authorization code for tokens.
            else if (response.params.code) {
                try {
                    const clientId = getClientId();
                    const tokenResult = await exchangeCodeAsync(
                        {
                            code: response.params.code,
                            clientId,
                            redirectUri: makeRedirectUri({
                                scheme: 'com.factcoding.secondcar',
                                path: ''
                            }),
                            extraParams: {
                                code_verifier: request?.codeVerifier || ''
                            }
                        },
                        {
                            tokenEndpoint: 'https://oauth2.googleapis.com/token'
                        }
                    );
                    idToken = tokenResult.idToken;
                    accessToken = tokenResult.accessToken;
                } catch (exchangeError) {
                    const manualExchangeResponse = await fetch('https://oauth2.googleapis.com/token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        }
                    });
                    const tokenData = await manualExchangeResponse.json();
                    if (tokenData.id_token) {
                        idToken = tokenData.id_token;
                        accessToken = tokenData.access_token;
                    } else {
                        throw new Error('Failed to obtain tokens from authorization code');
                    }
                }
            }

            if (!idToken) {
                throw new Error('No ID token received from Google authentication');
            }

            // Sign in to Firebase using the received tokens.
            const credential = GoogleAuthProvider.credential(idToken, accessToken);
            const userCredential = await signInWithCredential(auth, credential);
            const user = userCredential.user;

            // Register with backend using Redux action
            const registerResult = await registerUserFlow(user.uid);
            if (registerResult?.status && registerResult.authorisation?.token) {
                await router.replace('/(auth)/(tabs)');
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Failed to complete Google sign-in');
        } finally {
            setLoader(false);
        }
    };

    // Update the handleGoogleSignIn function
    const handleGoogleSignIn = async () => {
        try {
            setGoogleLoading(true); // Use googleLoading instead of loader
            if (!request) {
                Alert.alert('Error', 'Authentication not ready. Please try again.');
                return;
            }
            const result = await promptAsync();
            if (result.type === 'success') {
                await handleAuthResponse(result);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Failed to open Google sign-in');
        } finally {
            setGoogleLoading(false);
        }
    };

    const handlePhoneSubmit = async () => {
        if (!pendingUid || !phone.trim()) {
            Alert.alert('Input Error', 'Please enter a valid phone number.');
            return;
        }
        try {
            setLoader(true);
            const result = await dispatch(
                registerWithPhone({
                    uid: pendingUid,
                    phone: phone.trim()
                })
            ).unwrap();
            if (result.status && result.authorisation?.token) {
                await AsyncStorage.setItem('token', result.authorisation.token);
                await AsyncStorage.setItem('user', JSON.stringify(result.user));
                setPhoneRequired(false);
                setPendingUid(null);
                await router.replace('/(auth)/(tabs)');
            } else {
                throw new Error(result.message || 'Phone registration failed');
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Failed to register phone');
        } finally {
            setLoader(false);
        }
    };

    // Update the handleFacebookSignIn function
    const handleFacebookSignIn = async () => {
        // try {
        //     setFacebookLoading(true); // Use facebookLoading instead of loader
        //     if (!fbRequest) {
        //         Alert.alert('Error', 'Facebook authentication not ready. Please try again.');
        //         return;
        //     }
        //     const result = await promptFacebookAsync();
        //     if (result.type === 'success') {
        //         const { access_token } = result.params;
        //         const credential = FacebookAuthProvider.credential(access_token);
        //         const userCredential = await signInWithCredential(auth, credential);
        //         const user = userCredential.user;

        //         const registerResult = await registerUserFlow(user.uid);
        //         if (registerResult?.status && registerResult.authorisation?.token) {
        //             await router.replace('/(auth)/(tabs)');
        //         }
        //     }
        // } catch (error: any) {
        //     Alert.alert('Error', error.message || 'Failed to complete Facebook sign-in');
        // } finally {
        //     setFacebookLoading(false);
        // }
    };

    // Update the renderSocialButtons function
    const renderSocialButtons = () => {
        if (phoneRequired) {
            // ... existing phone required view ...
        } else {
            return (
                <View style={styles.socialButtonsRow}>
                    <ThemedButton
                        style={[styles.socialButton, { backgroundColor: '#fff' }]}
                        onPress={handleGoogleSignIn}
                        disabled={googleLoading || facebookLoading}
                    >
                        <View style={styles.socialButtonContent}>
                            <Image
                                source={require('@/assets/images/googleImage.png')}
                                style={styles.socialIcon}
                            />
                            {googleLoading ? (
                                <View style={styles.loadingContainer}>
                                    <ActivityIndicator color="#000" style={styles.activityIndicator} />
                                    <ThemedText style={[styles.socialButtonText, { color: '#000' }]}>
                                        Signing in...
                                    </ThemedText>
                                </View>
                            ) : (
                                <ThemedText style={[styles.socialButtonText, { color: '#000' }]}>
                                    Continue with Google
                                </ThemedText>
                            )}
                        </View>
                    </ThemedButton>

                    <ThemedButton
                        style={[styles.socialButton, { backgroundColor: '#1877F2' }]}
                        onPress={handleFacebookSignIn}
                        disabled={googleLoading || facebookLoading}
                    >
                        <View style={styles.socialButtonContent}>
                            <Ionicons name="logo-facebook" size={24} color="#fff" />
                            {facebookLoading ? (
                                <View style={styles.loadingContainer}>
                                    <ActivityIndicator color="#fff" style={styles.activityIndicator} />
                                    <ThemedText style={[styles.socialButtonText, { color: '#fff' }]}>
                                        Signing in...
                                    </ThemedText>
                                </View>
                            ) : (
                                <ThemedText style={[styles.socialButtonText, { color: '#fff' }]}>
                                    Continue with Facebook
                                </ThemedText>
                            )}
                        </View>
                    </ThemedButton>
                </View>
            );
        }
    };

    return (
        <KeyboardAvoidingView
            style={styles.container}
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContent}
                keyboardShouldPersistTaps="handled"
                showsVerticalScrollIndicator={false}
            >
                <View style={styles.contentContainer}>
                    {/* Show back button for all steps except 'email' */}
                    {step !== 'email' && (
                        <BackButton onPress={() => {
                            // Handle different back navigation flows
                            switch (step) {
                                case 'login':
                                case 'register':
                                    setStep('email');
                                    break;
                                case 'verify':
                                    setStep('email');
                                    break;
                                case 'forgotPassword':
                                    setStep('login');
                                    break;
                                case 'verifyForgotPassword':
                                    setStep('forgotPassword');
                                    break;
                                case 'resetPassword':
                                    setStep('verifyForgotPassword');
                                    break;
                                default:
                                    setStep('email');
                            }
                        }} />
                    )}

                    <Image
                        source={require('@/assets/images/splash-icon.png')}
                        style={styles.logo}
                        resizeMode="contain"
                    />

                    {step === 'email' && (
                        <>
                            <EmailForm
                                onEmailSubmit={handleEmailSubmit}
                                onRegister={() => setStep('register')}
                                loading={loader}
                            />

                            <View style={styles.dividerContainer}>
                                <View style={styles.divider} />
                                <ThemedText style={styles.dividerText}>OR</ThemedText>
                                <View style={styles.divider} />
                            </View>

                            {renderSocialButtons()}
                        </>
                    )}

                    {step === 'login' && (
                        <LoginForm
                            email={email}
                            password={password}
                            setPassword={setPassword}
                            onLoginSuccess={handleLogin}
                            onForgotPassword={() => setStep('forgotPassword')}
                            loading={loader}
                        />
                    )}

                    {step === 'register' && (
                        <RegisterForm
                            email={email}
                            onRegisterSuccess={handleRegister}
                            loading={loader}
                        />
                    )}

                    {step === 'verify' && (
                        <VerifyOtpForm
                            email={email}
                            otp={otp}
                            setOtp={setOtp}
                            onVerifySuccess={handleVerifyOtp}
                            loading={loader}
                        />
                    )}

                    {step === 'forgotPassword' && (
                        <ForgotPasswordForm
                            email={email}
                            onSendOtpSuccess={handleForgotPassword}
                            loading={loader}
                        />
                    )}

                    {step === 'verifyForgotPassword' && (
                        <VerifyForgotPasswordOtpForm
                            email={email}
                            otp={otp}
                            setOtp={setOtp}
                            onVerifySuccess={handleVerifyForgotPasswordOtp}
                            loading={loader}
                        />
                    )}

                    {step === 'resetPassword' && (
                        <ResetPasswordForm
                            email={email}
                            otp={otp}
                            onResetSuccess={handleResetPassword}
                            loading={loader}
                        />
                    )}
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.background
    },
    scrollContent: {
        flexGrow: 1,
        justifyContent: 'center'
    },
    contentContainer: {
        flex: 1,
        padding: SPACING.xl,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: COLORS.background,
        position: 'relative', // Add this for absolute positioning of back button
    },
    logo: {
        width: width * 0.4,
        height: width * 0.4,
        marginBottom: SPACING.xl,
        backgroundColor: 'transparent'
    } as ImageStyle,
    dividerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: SPACING.lg,
        width: '100%'
    },
    divider: {
        flex: 1,
        height: 1
    },
    dividerText: {
        marginHorizontal: SPACING.md,
        color: COLORS.text.secondary,
        fontSize: 14
    },
    // Social/Google sign-in styles (moved from SocialButtons)
    socialButtonsRow: {
        flexDirection: 'column',
        width: '100%',
        gap: 10
    },
    socialButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        paddingVertical: 12,
        borderRadius: 8,
        marginVertical: 6,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
    } as ViewStyle,
    socialButtonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
        justifyContent: 'center',
        width: '100%',
    },
    socialIcon: {
        width: 24,
        height: 24
    },
    socialButtonText: {
        fontSize: 16,
        fontWeight: '600'
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    activityIndicator: {
        marginRight: 8
    },
    phoneContainer: {
        padding: 16,
        width: '100%',
        backgroundColor: '#fff',
        borderRadius: 8,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84
    },
    label: {
        fontSize: 16,
        marginBottom: 16,
        fontWeight: '600',
        textAlign: 'center'
    },
    phoneInput: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        marginBottom: 16,
        fontSize: 16,
        width: '100%',
        backgroundColor: '#f5f5f5'
    },
    backButton: {
        position: 'absolute',
        left: SPACING.md,
        top: Platform.OS === 'ios' ? 50 : 40,
        zIndex: 1,
        padding: SPACING.sm,
        backgroundColor: COLORS.primary,
        borderRadius: 25,
        flexDirection: 'row',
        alignItems: 'center',
        elevation: 4,
        shadowColor: COLORS.primary,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        borderWidth: 1,
        borderColor: 'rgba(255,255,255,0.2)',
    },
    backButtonText: {
        color: COLORS.white,
        fontSize: 14,
        fontWeight: '600',
        marginLeft: 2,
    }
});
