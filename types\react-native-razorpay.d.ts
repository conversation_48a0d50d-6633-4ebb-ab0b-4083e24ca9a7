// Type definitions for react-native-razorpay
declare module "react-native-razorpay" {
  interface RazorpayOptions {
    key: string;
    amount: string | number;
    currency?: string;
    name?: string;
    description?: string;
    image?: string;
    order_id?: string;
    prefill?: {
      name?: string;
      email?: string;
      contact?: string;
      method?: string;
    };
    theme?: {
      color?: string;
      backdrop_color?: string;
      hide_topbar?: boolean;
    };
    modal?: {
      ondismiss?: () => void;
      animation?: boolean;
      backdropclose?: boolean;
      escape?: boolean;
      handleback?: boolean;
      confirm_close?: boolean;
    };
    notes?: Record<string, string>;
    display?: {
      logo?: string;
      name?: string;
      description?: string;
      color?: string;
      backdrop?: string;
      offer_id?: string;
    };
    retry?: {
      enabled?: boolean;
      max_count?: number;
    };
    send_sms_hash?: boolean;
    remember_customer?: boolean;
    readonly?: {
      contact?: boolean;
      email?: boolean;
      name?: boolean;
    };
    hidden?: {
      contact?: boolean;
      email?: boolean;
    };
    callback_url?: string;
    redirect?: boolean;
    customer_id?: string;
    timeout?: number;
    method?: {
      netbanking?: boolean;
      card?: boolean;
      upi?: boolean;
      wallet?: boolean;
      emi?: boolean;
      paylater?: boolean;
    };
    config?: {
      display?: {
        language?: string;
        blocks?: {
          banks?: {
            name: string;
            instruments: Array<{
              method: string;
              banks?: string[];
            }>;
          };
        };
        sequence?: string[];
        preferences?: {
          show_default_blocks?: boolean;
        };
      };
    };
  }

  interface RazorpaySuccessResponse {
    razorpay_payment_id: string;
    razorpay_order_id: string;
    razorpay_signature: string;
  }

  interface RazorpayErrorResponse {
    code: string;
    description: string;
    source: string;
    step: string;
    reason: string;
    metadata: Record<string, any>;
  }

  const RazorpayCheckout: {
    open: (options: RazorpayOptions) => Promise<RazorpaySuccessResponse>;
  };

  export default RazorpayCheckout;
}
