import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { ThemedText } from '@/components/common/ThemedText';
import ThemedInput from '@/components/common/ThemedInput';
import ThemedButton from '@/components/common/ThemedButton';
import { COLORS } from '@/constants/theme';

const resetPasswordSchema = Yup.object().shape({
    password: Yup.string()
        .min(6, 'Password must be at least 6 characters')
        .required('Password is required'),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref('password')], 'Passwords must match')
        .required('Confirm password is required'),
});

interface ResetPasswordFormProps {
    email: string;
    otp: string;
    onResetSuccess: (values: { password: string; confirmPassword: string }) => Promise<void>;
    loading: boolean;
}

const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({ email, otp, onResetSuccess, loading }) => {
    return (
        <>
            <ThemedText style={styles.title}>Reset Password</ThemedText>
            <ThemedText style={styles.subtitle}>Enter your new password</ThemedText>

            <Formik
                initialValues={{ password: '', confirmPassword: '' }}
                validationSchema={resetPasswordSchema}
                onSubmit={onResetSuccess}
            >
                {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
                    <>
                        <ThemedInput
                            value={values.password}
                            onChangeText={handleChange('password')}
                            onBlur={handleBlur('password')}
                            placeholder="New Password"
                            secureTextEntry
                            error={touched.password && errors.password}
                        />

                        <ThemedInput
                            value={values.confirmPassword}
                            onChangeText={handleChange('confirmPassword')}
                            onBlur={handleBlur('confirmPassword')}
                            placeholder="Confirm New Password"
                            secureTextEntry
                            error={touched.confirmPassword && errors.confirmPassword}
                        />

                        <ThemedButton
                            style={styles.button}
                            onPress={handleSubmit}
                            disabled={loading}
                        >
                            {loading ? 'Resetting...' : 'Reset Password'}
                        </ThemedButton>
                    </>
                )}
            </Formik>
        </>
    );
};

const styles = StyleSheet.create({
    title: {
        fontSize: 24,
        marginBottom: 8,
        textAlign: 'center',
        color: COLORS.primary,
    },
    subtitle: {
        fontSize: 16,
        marginBottom: 24,
        textAlign: 'center',
        color: COLORS.text.primary,
    },
    button: {
        width: '100%',
        marginTop: 8,
    },
});

export default ResetPasswordForm;