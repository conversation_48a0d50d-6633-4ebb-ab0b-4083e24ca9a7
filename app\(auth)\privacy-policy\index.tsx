import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, FONTS, SPACING } from '@/constants/theme';
import { Ionicons } from '@expo/vector-icons';
import Constants from 'expo-constants';
import { router } from 'expo-router';
import React from 'react';
import { ScrollView, StyleSheet, Text, TextStyle, TouchableOpacity, View } from 'react-native';

export default function PrivacyPolicyScreen() {
    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
                    <Ionicons name="arrow-back" size={24} color={COLORS.white} />
                </TouchableOpacity>
                <ThemedText style={styles.headerTitle}>Privacy Policy</ThemedText>
                <View style={{ width: 24 }} />
            </View>

            {/* Content */}
            <ScrollView
                contentContainerStyle={styles.content}
                showsVerticalScrollIndicator={false}
            >
                <Text style={styles.heading}>Privacy Policy</Text>
                <Text style={styles.lastUpdated}>Effective Date: April 25, 2025</Text>

                <Text style={styles.paragraph}>
                    Welcome to <Text style={styles.bold}>2ndCar.in</Text> — India's trusted online platform for connecting vehicle buyers and sellers.
                    <Text style={styles.bold}> 2ndCar.in</Text> is owned and operated by <Text style={styles.bold}>2nd Car</Text>.
                </Text>

                <Text style={styles.paragraph}>
                    We are committed to protecting your privacy. This Privacy Policy explains how we collect, use, and protect your personal information.
                </Text>

                <Text style={styles.sectionHeading}>Information We Collect</Text>
                <Text style={styles.paragraph}>
                    • Information you provide when you list a vehicle (name, contact number, email, vehicle details).
                </Text>
                <Text style={styles.paragraph}>
                    • Information when you contact us for any support or inquiry.
                </Text>

                <Text style={styles.sectionHeading}>How We Use Information</Text>
                <Text style={styles.paragraph}>
                    • To display seller's information to potential buyers.
                </Text>
                <Text style={styles.paragraph}>
                    • To provide customer support when required.
                </Text>
                <Text style={styles.paragraph}>
                    • To communicate with you regarding your account or services.
                </Text>

                <Text style={styles.sectionHeading}>Sharing of Information</Text>
                <Text style={styles.paragraph}>
                    • Your contact information will be publicly displayed with your vehicle listing.
                </Text>
                <Text style={styles.paragraph}>
                    • We do not sell, trade, or rent users' personal information to others.
                </Text>

                <Text style={styles.sectionHeading}>Cookies</Text>
                <Text style={styles.paragraph}>
                    Our website may use cookies to enhance user experience.
                </Text>

                <Text style={styles.sectionHeading}>Your Consent</Text>
                <Text style={styles.paragraph}>
                    By using our site, you consent to our privacy policy.
                </Text>

                <Text style={styles.sectionHeading}>Changes to Privacy Policy</Text>
                <Text style={styles.paragraph}>
                    We may update our Privacy Policy from time to time. Changes will be updated here.
                </Text>

                <Text style={styles.sectionHeading}>Contact Us</Text>
                <Text style={styles.paragraph}>
                    For any questions regarding this policy, please contact us at <Text style={styles.bold}><EMAIL></Text>.
                </Text>
            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FFFFFF',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingTop: 16 + Constants.statusBarHeight,
        backgroundColor: '#1E2A47',
        paddingHorizontal: 16,
        paddingVertical: 14,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    backButton: {
        padding: SPACING.xs,
        borderRadius: 20,
    },
    headerTitle: {
        fontSize: 20,
        ...FONTS.medium,
        fontWeight: 'bold',
        color: COLORS.white,
    } as TextStyle,
    appBar: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 15,
        paddingHorizontal: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0',
    },
    appBarTitle: {
        fontSize: 18,
        fontWeight: '600',
    },
    content: {
        paddingHorizontal: 20,
        paddingTop: 20,
        paddingBottom: 40,
    },
    heading: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 10,
    },
    lastUpdated: {
        fontSize: 14,
        color: '#666',
        marginBottom: 15,
    },
    sectionHeading: {
        fontSize: 18,
        fontWeight: 'bold',
        marginTop: 20,
        marginBottom: 10,
    },
    paragraph: {
        fontSize: 16,
        marginBottom: 10,
        lineHeight: 24,
    },
    bold: {
        fontWeight: 'bold',
    },
});