import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { COLORS, SPACING } from '@/constants/theme';
import ThemedButton from '@/components/common/ThemedButton';
import ThemedTextInput from '@/components/common/ThemedTextInput';
import LoadingIndicator from '@/components/common/LoadingIndicator';
import { ThemedText } from '@/components/common/ThemedText';

interface PhoneNumberFormProps {
    onSubmit: (phone: string) => void;
    loading?: boolean;
}

const phoneRegExp = /^[0-9]{10}$/;

const validationSchema = Yup.object().shape({
    phone: Yup.string()
        .matches(phoneRegExp, 'Please enter a valid 10-digit phone number')
        .required('Phone number is required'),
});

export default function PhoneNumberForm({ onSubmit, loading }: PhoneNumberFormProps) {
    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <LoadingIndicator size={40} color1={COLORS.primary} color2={COLORS.secondary} />
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <ThemedText style={styles.title}>Enter Your Phone Number</ThemedText>
            <ThemedText style={styles.subtitle}>
                Please enter your phone number to complete the registration
            </ThemedText>

            <Formik
                initialValues={{ phone: '' }}
                validationSchema={validationSchema}
                onSubmit={(values) => onSubmit(values.phone)}
            >
                {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
                    <View style={styles.form}>
                        <ThemedTextInput
                            placeholder="Phone Number"
                            value={values.phone}
                            onChangeText={handleChange('phone')}
                            onBlur={handleBlur('phone')}
                            keyboardType="phone-pad"
                            error={touched.phone && errors.phone}
                            maxLength={10}
                        />

                        <ThemedButton
                            onPress={handleSubmit}
                            style={styles.button}
                        >
                            Submit
                        </ThemedButton>
                    </View>
                )}
            </Formik>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        width: '100%',
        alignItems: 'center',
    },
    loadingContainer: {
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        marginVertical: SPACING.xl,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: SPACING.sm,
        color: COLORS.text.primary,
    },
    subtitle: {
        fontSize: 16,
        marginBottom: SPACING.xl,
        color: COLORS.text.secondary,
        textAlign: 'center',
    },
    form: {
        width: '100%',
        gap: SPACING.lg,
    },
    button: {
        marginTop: SPACING.md,
    },
}); 