// app/(auth)/_layout.tsx
import { useAppSelector } from '@/store/hooks';
import { Redirect, Stack } from 'expo-router';

export default function AuthLayout() {
    const { isAuthenticated } = useAppSelector((state) => state.auth);

    // Redirect to login if not authenticated
    if (!isAuthenticated) {
        return <Redirect href="/auth/register" />;
    }

    return <Stack screenOptions={{ headerShown: false }} />;
}