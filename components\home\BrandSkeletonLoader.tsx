import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { ShimmerPlaceholder } from '@/components/common/ShimmerPlaceholder';
import { COLORS, SPACING } from '@/constants/theme';

export const BrandSkeletonLoader = () => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.brandsScroll}
    >
      {[...Array(6)].map((_, index) => (
        <View key={index} style={styles.brandItem}>
          <ShimmerPlaceholder style={styles.brandLogo} />
        </View>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  brandsScroll: {
    paddingLeft: SPACING.md,
    paddingVertical: SPACING.xs,
  },
  brandItem: {
    width: 80,
    height: 80,
    backgroundColor: COLORS.white,
    borderRadius: 45,
    marginRight: SPACING.md,
    marginVertical: SPACING.xs,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    padding: SPACING.xs,
  },
  brandLogo: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
  },
});
