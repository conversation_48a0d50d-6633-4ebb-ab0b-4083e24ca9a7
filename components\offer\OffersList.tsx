import React from 'react';
import {
  View,
  FlatList,
  Image,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING, BORDERS, FONTS, SHADOWS } from '@/constants/theme';
import { Offer } from '@/store/slices/offerSlice';

interface OffersListProps {
  offers?: Offer[];
  onPlaceOffer: () => void;
}

export default function OffersList({ offers = [], onPlaceOffer }: OffersListProps) {
  const formatTimeAgo = (dateString: string): string => {
    const now = new Date();
    const offerDate = new Date(dateString);
    const diffInMs = now.getTime() - offerDate.getTime();
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInDays > 0) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    } else if (diffInHours > 0) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  const renderOfferItem = ({ item, index }: { item: Offer; index: number }) => (
    <View style={styles.offerItem}>
      <View style={styles.offerNumberContainer}>
        <ThemedText style={styles.offerNumber}>{index + 1}</ThemedText>
      </View>

      <View style={styles.offerContent}>
        <View style={styles.userInfo}>
          <View style={styles.profilePicture}>
            {item.profile_picture ? (
              <Image
                source={{ uri: item.profile_picture }}
                style={styles.profilePictureImage}
              />
            ) : (
              <View style={styles.defaultAvatar}>
                <Ionicons name="person" size={16} color={COLORS.text.secondary} />
              </View>
            )}
          </View>
          <View style={styles.userDetails}>
            <ThemedText style={styles.userName}>{item.name}</ThemedText>
            <ThemedText style={styles.offerTime}>
              {formatTimeAgo(item.offered_at)}
            </ThemedText>
          </View>
        </View>

        <View style={styles.offerAmountContainer}>
          <ThemedText style={styles.offerAmount}>
            ₹ {item.make_offer.toLocaleString('en-IN')}
          </ThemedText>
        </View>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons
        name="pricetag-outline"
        size={48}
        color={COLORS.text.secondary}
      />
      <ThemedText style={styles.emptyStateText}>No offers yet</ThemedText>
      <ThemedText style={styles.emptyStateSubtext}>
        Be the first to make an offer on this vehicle
      </ThemedText>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <ThemedText style={styles.title}>Latest Offers</ThemedText>
        <TouchableOpacity style={styles.placeOfferButton} onPress={onPlaceOffer}>
          <Ionicons name="flash" size={16} color={COLORS.white} />
          <ThemedText style={styles.placeOfferButtonText}>Place Offer</ThemedText>
        </TouchableOpacity>
      </View>

      {offers.length > 0 ? (
        <FlatList
          data={offers}
          renderItem={renderOfferItem}
          keyExtractor={(item, index) => `${item.user_id}-${index}`}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          scrollEnabled={false} // Disable scroll since it's inside a ScrollView
        />
      ) : (
        renderEmptyState()
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    borderRadius: BORDERS.radius.md,
    padding: SPACING.md,
    marginVertical: SPACING.md,
    ...SHADOWS.sm,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: FONTS.size.h3,
    fontWeight: FONTS.weight.bold,
    color: COLORS.text.primary,
  },
  placeOfferButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.secondary,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDERS.radius.sm,
    gap: SPACING.xs,
  },
  placeOfferButtonText: {
    color: COLORS.white,
    fontSize: FONTS.size.caption,
    fontWeight: FONTS.weight.bold,
  },
  listContainer: {
    gap: SPACING.sm,
  },
  offerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface.secondary,
    borderRadius: BORDERS.radius.sm,
    padding: SPACING.sm,
    borderWidth: BORDERS.width.hairline,
    borderColor: COLORS.border.secondary,
  },
  offerNumberContainer: {
    width: 32,
    height: 32,
    backgroundColor: COLORS.primary,
    borderRadius: BORDERS.radius.full,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm,
  },
  offerNumber: {
    color: COLORS.white,
    fontSize: FONTS.size.caption,
    fontWeight: FONTS.weight.bold,
  },
  offerContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profilePicture: {
    width: 32,
    height: 32,
    borderRadius: BORDERS.radius.full,
    marginRight: SPACING.sm,
    backgroundColor: COLORS.gray[200],
    overflow: 'hidden',
  },
  profilePictureImage: {
    width: '100%',
    height: '100%',
  },
  defaultAvatar: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.gray[200],
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: FONTS.size.caption,
    fontWeight: FONTS.weight.medium,
    color: COLORS.text.primary,
  },
  offerTime: {
    fontSize: FONTS.size.small,
    color: COLORS.text.secondary,
    marginTop: 2,
  },
  offerAmountContainer: {
    alignItems: 'flex-end',
  },
  offerAmount: {
    fontSize: FONTS.size.caption,
    fontWeight: FONTS.weight.bold,
    color: COLORS.primary,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  emptyStateText: {
    fontSize: FONTS.size.body,
    fontWeight: FONTS.weight.medium,
    color: COLORS.text.primary,
    marginTop: SPACING.sm,
  },
  emptyStateSubtext: {
    fontSize: FONTS.size.caption,
    color: COLORS.text.secondary,
    textAlign: 'center',
    marginTop: SPACING.xs,
  },
});
