// utils/paymentConfig.ts
import { Platform } from "react-native";
import Constants from "expo-constants";

// Define environment types
type Environment = "development" | "staging" | "production";

// Get the current environment
export const getEnvironment = (): Environment => {
  // You can customize this logic based on how you determine environments
  if (__DEV__) {
    return "development";
  }

  // Check for staging environment (you can customize this check)
  const releaseChannel = Constants.releaseChannel;
  if (releaseChannel && releaseChannel.includes("staging")) {
    return "staging";
  }

  return "production";
};

// Razorpay configuration
interface RazorpayConfig {
  keyId: string;
  keySecret?: string; // Only used on server-side
  name: string;
  description: string;
  currency: string;
  theme: {
    color: string;
  };
}

// Configuration for different environments
const RAZORPAY_CONFIG: Record<Environment, RazorpayConfig> = {
  development: {
    keyId: "rzp_test_1ijqDexs5oo2U1", // Test key
    keySecret: "2cbQFDyvy3riqnf5esfMnedN", // Only used server-side
    name: "2nd Car",
    description: "Purchase",
    currency: "INR",
    theme: {
      color: "#1E2A47",
    },
  },
  staging: {
    keyId: "rzp_test_1ijqDexs5oo2U1", // Test key
    keySecret: "2cbQFDyvy3riqnf5esfMnedN", // Only used server-side
    name: "2nd Car",
    description: "Purchase",
    currency: "INR",
    theme: {
      color: "#1E2A47",
    },
  },
  production: {
    keyId: "rzp_test_1ijqDexs5oo2U1", // Replace with your live key
    keySecret: "2cbQFDyvy3riqnf5esfMnedN", // Only used server-side
    name: "2nd Car",
    description: "Purchase",
    currency: "INR",
    theme: {
      color: "#1E2A47",
    },
  },
};

// Get Razorpay configuration for current environment
export const getRazorpayConfig = (): RazorpayConfig => {
  const environment = getEnvironment();
  console.log("environment", environment);
  return RAZORPAY_CONFIG[environment];
};

// Get Razorpay key for current environment
export const getRazorpayKey = (): string => {
  return getRazorpayConfig().keyId;
};

// Get app version
export const getAppVersion = (): string => {
  return Constants.expoConfig?.version || "1.0.0";
};

// Get platform-specific information
export const getPlatformInfo = () => {
  return {
    platform: Platform.OS,
    version: Platform.Version,
    appVersion: getAppVersion(),
  };
};

// Export default configuration
export default {
  getRazorpayKey,
  getRazorpayConfig,
  getEnvironment,
  getPlatformInfo,
};
