import React, { useState } from 'react';
import {
    TextInput,
    StyleSheet,
    TextStyle,
    View,
    TouchableOpacity,
} from 'react-native';
import { COLORS, FONTS } from '@/constants/theme';
import { Ionicons } from '@expo/vector-icons';

interface ThemedInputProps {
    style?: TextStyle;
    value: string;
    onChangeText: (text: string) => void;
    placeholder?: string;
    secureTextEntry?: boolean;
    keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
    autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
    editable?: boolean;
    onBlur?: (e: any) => void;
    error?: string | false | undefined;
    multiline?: boolean;
    numberOfLines?: number;
    maxLength?: number;
}

export default function ThemedInput({
    style,
    value,
    onChangeText,
    placeholder,
    secureTextEntry,
    keyboardType = 'default',
    autoCapitalize = 'none',
    editable = true,
    onBlur,
    error,
    multiline = false,
    numberOfLines = 1,
    maxLength,
}: ThemedInputProps) {
    // Local state to toggle password visibility if secureTextEntry is true
    const [hidePassword, setHidePassword] = useState(secureTextEntry);

    return (
        <View style={[styles.inputContainer]}>
            <TextInput
                style={[styles.input, error && styles.inputError, style, multiline && styles.multilineInput]}
                value={value}
                onChangeText={onChangeText}
                placeholder={placeholder}
                placeholderTextColor={COLORS.text.secondary}
                // Use the local hidePassword state if secureTextEntry is true
                secureTextEntry={secureTextEntry ? hidePassword : false}
                keyboardType={keyboardType}
                autoCapitalize={autoCapitalize}
                editable={editable}
                onBlur={onBlur}
                multiline={multiline}
                numberOfLines={numberOfLines}
                maxLength={maxLength}
                textAlignVertical={multiline ? 'top' : 'center'}
            />
            {secureTextEntry && (
                <TouchableOpacity
                    style={styles.iconContainer}
                    onPress={() => setHidePassword(!hidePassword)}
                    activeOpacity={0.7}>
                    <Ionicons
                        name={hidePassword ? 'eye-off' : 'eye'}
                        size={24}
                        color={COLORS.text.primary}
                    />
                </TouchableOpacity>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    inputContainer: {
        width: '100%',
        position: 'relative',
        marginBottom: 16,
    },
    input: {
        width: '100%',
        backgroundColor: COLORS.white,
        padding: 16,
        borderRadius: 12,
        ...FONTS.regular,
        fontSize: 16,
        color: COLORS.text.primary,
    } as TextStyle,
    inputError: {
        borderColor: COLORS.error,
        borderWidth: 1,
    },
    iconContainer: {
        position: 'absolute',
        right: 16,
        top: '50%',
        transform: [{ translateY: -12 }], // centers the icon vertically (assuming icon height is ~24)
    },
    multilineInput: {
        minHeight: 100,
        paddingTop: 16,
        textAlignVertical: 'top',
    },
});
