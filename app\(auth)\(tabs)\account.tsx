import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, FONTS, SPACING } from '@/constants/theme';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { logout } from '@/store/slices/authSlice';
import { useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { StyleSheet, TouchableOpacity, View, Image, Alert, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { clearUserData, fetchUserData } from '@/store/slices/accountSlice';

export default function AccountScreen() {
    const dispatch = useAppDispatch();
    const router = useRouter();
    const { userData, purchase, loading, error } = useAppSelector((state) => state.account);
    const { user } = useAppSelector((state) => state.auth);

    // Load user data on component mount and when user changes
    useEffect(() => {
        const loadData = async () => {
            try {
                await dispatch(fetchUserData()).unwrap();
            } catch (err) {
                console.error("Error fetching user data:", err);
            }
        };

        // Always load data on initial mount
        loadData();

        // Set up a refresh interval
        // const refreshInterval = setInterval(() => {
        //     loadData();
        // }, 30000); // Refresh every 30 seconds

        // Clean up interval on unmount
        // return () => clearInterval(refreshInterval);
    }, [dispatch, user]); // Depend on user and dispatch only

    const handleLogout = async () => {
        dispatch(clearUserData());
        await dispatch(logout());
        router.replace('/auth/register');
    };

    const handleEditProfile = () => {
        router.push('/(auth)/edit-profile');
    };

    const handleBuyPackage = () => {
        router.push('/(auth)/buy-package');
    };

    const handleManageVehicles = () => {
        router.push('/(auth)/manage-vehicles');
    };

    const handleTermsAndConditions = () => {
        router.push('/(auth)/terms-and-conditions');
    };

    const handlePrivacyPolicy = () => {
        router.push('/(auth)/privacy-policy');
    };

    // Add this function to handle status colors
    const getStatusConfig = (status: "pending" | "completed" | "failed") => {
        switch (status) {
            case "completed":
                return {
                    backgroundColor: '#E0F2E9',
                    textColor: '#2E7D32',
                    text: 'Active'
                };
            case "pending":
                return {
                    backgroundColor: '#FFF3E0',
                    textColor: '#E65100',
                    text: 'Processing'
                };
            case "failed":
                return {
                    backgroundColor: '#FFEBEE',
                    textColor: '#C62828',
                    text: 'Payment Failed'
                };
            default:
                return {
                    backgroundColor: '#E0E0E0',
                    textColor: '#757575',
                    text: status
                };
        }
    };

    // Show loading state when loading data initially
    if (loading && !userData) {
        return (
            <View style={styles.loadingContainer}>
                <ThemedText style={{ color: COLORS.text.primary }}>Loading...</ThemedText>
            </View>
        );
    }

    // Helper function to parse expiration date
    const formatDate = (dateString: string) => {
        if (!dateString) return "N/A";

        try {
            const dateObj = new Date(dateString);
            return dateObj.toLocaleDateString('en-US', {
                day: 'numeric',
                month: 'short',
                year: 'numeric',
            });
        } catch (e) {
            console.error("Date parsing error:", e);
            return dateString; // Return original string if parsing fails
        }
    };

    // Example check for "active" vs. "expired" based on current date
    const isPackageActive = (expireAt: any) => {
        if (!expireAt) return false;

        try {
            const now = new Date();
            return new Date(expireAt) > now;
        } catch (e) {
            console.error("Date comparison error:", e);
            return false;
        }
    };

    // Parse the description (which might contain arrays or HTML entities)
    const parseDescription = (descArray: any) => {
        if (!descArray || !Array.isArray(descArray)) {
            return ["No description available"];
        }

        try {
            // Flatten in case each item is an array
            const flattened = descArray.flatMap((item) => item);
            // Replace &nbsp; with a space (or decode HTML as needed)
            return flattened.map((line) =>
                typeof line === 'string' ? line.replace(/&nbsp;/g, ' ') : String(line)
            );
        } catch (e) {
            console.error("Description parsing error:", e);
            return ["Error parsing description"];
        }
    };

    return (
        <View style={styles.container}>
            <ScrollView
                contentContainerStyle={styles.scrollContent}
                showsVerticalScrollIndicator={false}
            >
                {/* Profile Card */}
                <View style={styles.profileCard}>
                    {/* Header Area with Background Color */}
                    <View style={styles.profileHeader}>
                        <View style={styles.headerTextContainer}>
                            <ThemedText style={styles.headerTitle}>My Profile</ThemedText>
                        </View>
                        {/* Optionally place an edit icon in the header */}
                        <TouchableOpacity onPress={handleEditProfile} style={styles.headerEditButton}>
                            <Ionicons name="pencil" size={20} color="#fff" />
                        </TouchableOpacity>
                    </View>

                    {/* Profile Content Overlaid */}
                    <View style={styles.profileContent}>
                        <Image
                            source={
                                userData?.profile_picture
                                    ? { uri: userData.profile_picture }
                                    : require('@/assets/images/car2Image.png')
                            }
                            style={styles.profileImage}
                            defaultSource={require('@/assets/images/car2Image.png')}
                        />
                        <View style={styles.profileDetails}>
                            <ThemedText style={styles.profileName}>
                                {userData?.name || "Loading name..."}
                            </ThemedText>
                            <ThemedText style={styles.profileEmail}>
                                {userData?.email || user?.email || "Loading email..."}
                            </ThemedText>
                        </View>
                    </View>
                </View>



                {/* Manage Vehicles */}
                <TouchableOpacity style={styles.menuItem} onPress={handleManageVehicles}>
                    <Ionicons name="car-sport-outline" size={20} color="#000" />
                    <ThemedText style={styles.menuItemText}>Manage Vehicles</ThemedText>
                    <Ionicons name="chevron-forward" size={20} color="#000" />
                </TouchableOpacity>

                {/* Packages */}
                <TouchableOpacity style={styles.menuItem} onPress={handleBuyPackage}>
                    <Ionicons name="cube-outline" size={20} color="#000" />
                    <ThemedText style={styles.menuItemText}>Packages</ThemedText>
                    <Ionicons name="chevron-forward" size={20} color="#000" />
                </TouchableOpacity>


                {/* Terms & Conditions */}
                <TouchableOpacity style={styles.menuItem} onPress={handleTermsAndConditions}>
                    <ThemedText style={styles.menuItemText}>Terms & Conditions</ThemedText>
                    <Ionicons name="chevron-forward" size={20} color="#000" />
                </TouchableOpacity>

                {/* Privacy Policy */}
                <TouchableOpacity style={styles.menuItem} onPress={handlePrivacyPolicy}>
                    <ThemedText style={styles.menuItemText}>Privacy Policy</ThemedText>
                    <Ionicons name="chevron-forward" size={20} color="#000" />
                </TouchableOpacity>

                {/* Delete Account */}
                <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => router.push('/(auth)/delete-account')}
                >
                    <Ionicons name="trash-outline" size={20} color={COLORS.error} />
                    <ThemedText style={[styles.menuItemText, styles.deleteAccountText]}>Delete Account</ThemedText>
                    <Ionicons name="chevron-forward" size={20} color={COLORS.error} />
                </TouchableOpacity>

                {/* Package Section */}
                {purchase && purchase.length > 0 ? (
                    <View style={styles.packageCard}>
                        {/* Header Row: "Current Package", status, icon */}
                        <View style={styles.packageHeader}>
                            <Ionicons name="cube-outline" size={24} color="#000" />
                            <ThemedText style={styles.packageHeaderTitle}>Current Package</ThemedText>
                            <View style={[
                                styles.activeStatusContainer,
                                { backgroundColor: getStatusConfig(purchase[0]?.payment_status).backgroundColor }
                            ]}>
                                <ThemedText style={[
                                    styles.packageStatusText,
                                    { color: getStatusConfig(purchase[0]?.payment_status).textColor }
                                ]}>
                                    {getStatusConfig(purchase[0]?.payment_status).text}
                                </ThemedText>
                            </View>
                        </View>

                        {/* Package Name */}
                        <View style={styles.packageInfoRow}>
                            <Ionicons name="pricetags-outline" size={16} color="#888" style={{ marginRight: 6 }} />
                            <ThemedText style={styles.packageInfoLabel}>Package Name:</ThemedText>
                            <ThemedText style={styles.packageInfoValue}>
                                {purchase[0]?.package_name || "N/A"}
                            </ThemedText>
                        </View>

                        {/* Expiration Date */}
                        <View style={styles.packageInfoRow}>
                            <Ionicons name="calendar-outline" size={16} color="#888" style={{ marginRight: 6 }} />
                            <ThemedText style={styles.packageInfoLabel}>Expires on:</ThemedText>
                            <ThemedText style={styles.packageInfoValue}>
                                {formatDate(purchase[0]?.expire_at)}
                            </ThemedText>
                        </View>

                        {/* Description */}
                        <View style={styles.packageInfoRow}>
                            <Ionicons name="document-text-outline" size={16} color="#888" style={{ marginRight: 6 }} />
                            <ThemedText style={styles.packageInfoLabel}>Description:</ThemedText>
                        </View>
                        {purchase[0]?.description && Array.isArray(purchase[0].description) &&
                            parseDescription(purchase[0].description).map((descLine, index) => (
                                <ThemedText key={index} style={styles.packageDescriptionItem}>
                                    • {descLine}
                                </ThemedText>
                            ))}

                        {/* Buy/Renew Button */}
                        {/* <TouchableOpacity style={styles.buyButton} onPress={handleBuyPackage}>
                        <ThemedText style={styles.buyButtonText}>
                            {isPackageActive(purchase[0]?.expire_at) ? 'Renew Package' : 'Buy Package'}
                        </ThemedText>
                    </TouchableOpacity> */}
                    </View>
                ) : (
                    <View style={styles.packageCard}>
                        <View style={styles.noPackageContainer}>
                            <View style={styles.noPackageIconContainer}>
                                <Ionicons name="cube-outline" size={40} color={COLORS.text.secondary} />
                            </View>
                            <ThemedText style={styles.noPackageTitle}>No Active Package</ThemedText>
                            <TouchableOpacity
                                style={styles.buyPackageButton}
                                onPress={handleBuyPackage}
                            >
                                <Ionicons name="cart-outline" size={20} color="#fff" style={{ marginRight: 8 }} />
                                <ThemedText style={styles.buyPackageButtonText}>Buy Package</ThemedText>
                            </TouchableOpacity>
                        </View>
                    </View>
                )}
            </ScrollView>

            {/* Logout button fixed at bottom */}
            <View style={styles.logoutContainer}>
                <TouchableOpacity
                    style={styles.logoutButton}
                    onPress={handleLogout}
                    accessibilityLabel="Logout"
                    accessibilityRole="button"
                >
                    <Ionicons name="log-out-outline" size={20} color="white" />
                    <ThemedText style={styles.logoutText}>Logout</ThemedText>
                </TouchableOpacity>
            </View>
        </View>
    );
}

/* ---------- STYLES ---------- */

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: COLORS.white,
        color: COLORS.black
    },


    /* Profile Card */
    profileCard: {
        backgroundColor: '#fff',
        borderRadius: 12,
        marginBottom: SPACING.md,
        overflow: 'hidden', // ensure children are clipped
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 6,
        elevation: 4,
    },
    profileHeader: {
        backgroundColor: '#151E3F',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: SPACING.md,
    },
    headerTextContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    headerTitle: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
    headerEditButton: {
        padding: 8,
    },
    profileContent: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: SPACING.md,
        backgroundColor: '#f0f0f0', // lighter background for the content area
    },
    profileImage: {
        width: 70,
        height: 70,
        borderRadius: 35,
        borderWidth: 2,
        borderColor: '#fff',
        marginRight: SPACING.md,
    },
    profileDetails: {
        flex: 1,
    },
    profileName: {
        fontSize: 17,
        fontWeight: 'bold',
        color: '#000',
    },
    profileEmail: {
        fontSize: 14,
        color: '#666',
        marginTop: 4,
    },

    /* Package Card */
    // [rest of your styles remain the same]
    packageCard: {
        backgroundColor: '#fff',
        borderRadius: 12,
        padding: SPACING.md,
        marginBottom: SPACING.md,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
    },
    packageHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 10,
    },
    packageHeaderTitle: {
        marginLeft: 8,
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
    },
    packageStatus: {
        marginLeft: 'auto',
    },
    activeStatusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 12,
        paddingHorizontal: SPACING.sm,
        paddingVertical: 4,
        marginLeft: SPACING.md,
    },
    packageStatusText: {
        fontWeight: 'bold',
        fontSize: 12,
    },
    packageInfoRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 6,
    },
    packageInfoLabel: {
        fontWeight: '600',
        color: '#333',
        marginRight: 4,
    },
    packageInfoValue: {
        flexShrink: 1,
        color: '#555',
        fontWeight: '400',
    },
    packageDescriptionItem: {
        fontSize: 14,
        color: '#555',
        marginLeft: 26, // offset to align with label + icon
        marginBottom: 2,
    },
    buyButton: {
        backgroundColor: '#151E3F',
        paddingVertical: SPACING.sm,
        paddingHorizontal: SPACING.md,
        borderRadius: 4,
        alignItems: 'center',
        marginTop: SPACING.md,
        alignSelf: 'flex-start',
    },
    buyButtonText: {
        color: 'white',
        fontSize: 14,
        fontWeight: '500',
    },

    /* Menu & Logout */
    menuItem: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'white',
        padding: SPACING.md,
        borderRadius: 8,
        marginBottom: SPACING.sm,
        justifyContent: 'space-between',
    },
    menuItemText: {
        flex: 1,
        marginLeft: 8,
        fontSize: 16,
        color: '#333',
    },
    logoutButton: {
        backgroundColor: '#E53935',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        padding: SPACING.md,
        borderRadius: 8,
    },
    logoutText: {
        color: 'white',
        fontSize: 16,
        marginLeft: SPACING.sm,
        fontWeight: '500',
    },
    noPackageContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        padding: SPACING.xl,
    },
    noPackageIconContainer: {
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: COLORS.background,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: SPACING.md,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
    },
    noPackageTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.text.primary,
        marginBottom: SPACING.lg,
    },
    noPackageDescription: {
        fontSize: 14,
        color: COLORS.text.secondary,
        textAlign: 'center',
        marginBottom: SPACING.lg,
        paddingHorizontal: SPACING.md,
    },
    buyPackageButton: {
        backgroundColor: COLORS.primary,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: SPACING.sm,
        paddingHorizontal: SPACING.xl,
        borderRadius: 25,
        elevation: 2,
        shadowColor: COLORS.primary,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
    },
    buyPackageButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },

    /* Add these new styles at the end of the StyleSheet */
    deleteAccountMenuItem: {
        marginTop: SPACING.md,
    },
    deleteAccountText: {
        color: COLORS.error,
    },
    scrollContent: {
        padding: SPACING.md,
        paddingBottom: 80, // Add padding to prevent content from being hidden behind logout button
    },
    logoutContainer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: '#f5f5f5',
        paddingHorizontal: SPACING.md,
        paddingBottom: SPACING.md,
        paddingTop: SPACING.sm,
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
    },
});