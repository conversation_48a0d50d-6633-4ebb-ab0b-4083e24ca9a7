import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios, { AxiosError, AxiosResponse } from "axios";

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

// Define interfaces for filter parameters
export interface FilterParams {
  vehicle_type?: string[];
  brand?: string[];
  fuel_type?: string[];
  transmission?: string[];
  min_price?: number;
  max_price?: number;
  min_year?: number;
  max_year?: number;
  min_km?: number;
  max_km?: number;
  owners?: string[];
  state?: number[];
  city?: number[];
}

// Define interface for vehicle data in response
export interface FilteredVehicle {
  id: number;
  title: string;
  brand: string;
  model: string;
  year: string;
  fuel_type: string;
  kilometers_driven: number;
  price: string;
  transmission: string | null;
  ownership: string;
  location: string;
  description: string;
  images: string[];
  primary_image: string;
  status: string;
  type: string;
  variant: string;
}

// Define interface for pagination metadata
interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

// Define interface for filter state
interface FilterState {
  filters: FilterParams;
  vehicles: FilteredVehicle[];
  loading: boolean;
  error: string | null;
  // pagination: PaginationMeta;
  isFilterApplied: boolean;
}

// Default filter values
const defaultFilters: FilterParams = {
  vehicle_type: ["car"],
  brand: [],
  fuel_type: [],
  transmission: [],
  min_price: 0,
  max_price: 5000000,
  min_year: 1995,
  max_year: new Date().getFullYear(),
  min_km: 0,
  max_km: 500000,
  owners: [],
  state: [],
  city: [],
};

// Initial state
const initialState: FilterState = {
  filters: { ...defaultFilters },
  vehicles: [],
  loading: false,
  error: null,
  isFilterApplied: false,
};

/**
 * Interface for API response structure
 */
interface ApiResponse {
  status?: boolean;
  data?: FilteredVehicle[];
  vehicles?: FilteredVehicle[];
  total?: number;
  pagination?: {
    total?: number;
  };
  message?: string;
}

/**
 * Create async thunk for filtering vehicles
 * Uses Axios for API requests with proper error handling
 */
export const filterVehicles = createAsyncThunk<
  { data: FilteredVehicle[]; pagination: PaginationMeta },
  FilterParams,
  { rejectValue: string }
>("filter/filterVehicles", async (filterParams, { rejectWithValue }) => {
  try {
    // Define the API endpoint URL
    const url = `${API_BASE_URL}/vehicles/filter`;

    // Preprocess filter parameters to handle transmission field based on vehicle type
    const processedFilterParams = { ...filterParams };

    // Get the selected vehicle type (first element of the array)
    const selectedVehicleType = filterParams.vehicle_type?.[0] || "car";

    // For bike and scooter vehicle types, exclude transmission values from API payload
    // but preserve them in the Redux state for UI consistency when switching vehicle types
    if (selectedVehicleType === "bike" || selectedVehicleType === "scooter") {
      processedFilterParams.transmission = [];
    }

    // Configure Axios request with headers and timeout
    const config = {
      headers: {
        "Content-Type": "application/json",
      },
      timeout: 10000, // 10 seconds timeout
    };

    // Make the POST request using Axios with processed parameters
    const response: AxiosResponse<ApiResponse> = await axios.post(
      url,
      processedFilterParams,
      config
    );

    // Extract data from response
    const data = response.data;

    // Validate response structure
    if (!data.status && !data.data) {
      throw new Error(data.message || "Failed to fetch vehicles");
    }

    // Handle different API response formats
    const vehicles = data.data || data.vehicles || [];
    const total = data.total || data.pagination?.total || vehicles.length;
    const limit = 10; // Default limit

    // Return formatted data
    return {
      data: vehicles,
      pagination: {
        currentPage: 1, // Default to first page
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: limit,
      },
    };
  } catch (error) {
    // Handle Axios errors with proper typing
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<ApiResponse>;

      // Log detailed error information
      console.error("Axios error:", {
        message: axiosError.message,
        status: axiosError.response?.status,
        statusText: axiosError.response?.statusText,
        data: axiosError.response?.data,
      });

      // Extract error message from response if available
      const errorMessage =
        axiosError.response?.data?.message ||
        axiosError.message ||
        `Error ${axiosError.response?.status}: ${axiosError.response?.statusText}`;

      return rejectWithValue(errorMessage);
    } else {
      // Handle non-Axios errors
      return rejectWithValue(
        error instanceof Error ? error.message : "Failed to filter vehicles"
      );
    }
  }
});

// Create the filter slice
const filterSlice = createSlice({
  name: "filter",
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetFilters: (state) => {
      // Reset to default filter values
      state.filters = { ...defaultFilters };
      state.isFilterApplied = false;
    },
    clearFilteredVehicles: (state) => {
      state.vehicles = [];
      // state.pagination = initialState.pagination;
      state.loading = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(filterVehicles.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(filterVehicles.fulfilled, (state, action) => {
        state.loading = false;
        state.vehicles = action.payload.data;
        // state.pagination = action.payload.pagination;
        state.isFilterApplied = true;
      })
      .addCase(filterVehicles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to filter vehicles";
      });
  },
});

export const { setFilters, resetFilters, clearFilteredVehicles } =
  filterSlice.actions;

// Export default filters for use in other components
export { defaultFilters };
export default filterSlice.reducer;
