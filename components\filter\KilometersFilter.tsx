import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, SPACING, FONTS, BORDERS, SHADOWS } from '@/constants/theme';
import { Slider } from '@miblanchard/react-native-slider';

interface KilometersFilterProps {
  minKm?: number;
  maxKm?: number;
  onChange: (minKm: number, maxKm: number) => void;
}

const KilometersFilter: React.FC<KilometersFilterProps> = ({
  minKm: initialMinKm,
  maxKm: initialMaxKm,
  onChange
}) => {
  // Default kilometers range
  const MIN_KM = 0;
  const MAX_KM = 200000;

  const [minKm, setMinKm] = useState(initialMinKm || MIN_KM);
  const [maxKm, setMaxKm] = useState(initialMaxKm || MAX_KM);

  // Update local state when props change
  useEffect(() => {
    if (initialMinKm !== undefined) setMinKm(initialMinKm);
    if (initialMaxKm !== undefined) setMaxKm(initialMaxKm);
  }, [initialMinKm, initialMaxKm]);

  // Format kilometers for display
  const formatKm = (km: number) => {
    if (km >= 1000) {
      return `${(km / 1000).toFixed(0)}k km`;
    } else {
      return `${km} km`;
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Kilometers Driven</Text>

      <View style={styles.rangeLabels}>
        <Text style={styles.rangeLabel}>{formatKm(minKm)}</Text>
        <Text style={styles.rangeLabel}>{formatKm(maxKm)}</Text>
      </View>

      <View style={styles.slider}>
        <Slider
          minimumValue={MIN_KM}
          maximumValue={MAX_KM}
          step={1000}
          value={minKm}
          onValueChange={(value) => setMinKm(Array.isArray(value) ? value[0] : value)}
          onSlidingComplete={(value) => onChange(Array.isArray(value) ? value[0] : value, maxKm)}
          minimumTrackTintColor={COLORS.primary}
          maximumTrackTintColor={COLORS.gray[300]}
          thumbTintColor={COLORS.primary}
          trackClickable={true}
        />
      </View>

      <View style={styles.slider}>
        <Slider
          minimumValue={MIN_KM}
          maximumValue={MAX_KM}
          step={1000}
          value={maxKm}
          onValueChange={(value) => setMaxKm(Array.isArray(value) ? value[0] : value)}
          onSlidingComplete={(value) => onChange(minKm, Array.isArray(value) ? value[0] : value)}
          minimumTrackTintColor={COLORS.primary}
          maximumTrackTintColor={COLORS.gray[300]}
          thumbTintColor={COLORS.primary}
          trackClickable={true}
        />
      </View>

      <View style={styles.rangeLabels}>
        <Text style={styles.rangeHint}>Min</Text>
        <Text style={styles.rangeHint}>Max</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDERS.radius.md,
    ...SHADOWS.xs,
  },
  title: {
    fontWeight: '600',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    marginBottom: SPACING.md,
    letterSpacing: 0.3,
  },
  rangeLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs,
  },
  rangeLabel: {
    fontWeight: '500',
    fontSize: FONTS.size.caption,
    color: COLORS.primary,
  },
  rangeHint: {
    fontWeight: '400',
    fontSize: FONTS.size.small,
    color: COLORS.text.secondary,
  },
  slider: {
    width: '100%',
    height: 40,
    marginVertical: SPACING.xs,
  },
});

export default KilometersFilter;
