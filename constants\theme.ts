export const COLORS = {
    // Primary brand colors
    primary: '#1B264F',
    secondary: '#FF5722',

    // Neutral colors
    background: '#F5F5F5',
    white: '#FFFFFF',
    black: '#000000',
    gray: {
        50: '#FAFAFA',
        100: '#F5F5F5',
        200: '#EEEEEE',
        300: '#E0E0E0',
        400: '#BDBDBD',
        500: '#9E9E9E',
        600: '#757575',
        700: '#616161',
        800: '#424242',
        900: '#212121',
    },

    // Semantic colors
    success: '#4CAF50',
    error: '#F44336',
    warning: '#FFC107',
    info: '#2196F3',

    // Text colors
    text: {
        primary: '#1B264F',
        secondary: '#666666',
        disabled: '#9E9E9E',
        inverse: '#FFFFFF',
        success: '#2E7D32',
        error: '#D32F2F',
        warning: '#FFA000',
    },

    // Surface colors
    surface: {
        primary: '#FFFFFF',
        secondary: '#F5F5F5',
        elevated: '#FFFFFF',
        disabled: '#EEEEEE',
    },

    // Border colors
    border: {
        primary: '#E0E0E0',
        secondary: '#EEEEEE',
        focused: '#1B264F',
        error: '#D32F2F',
    },

    // Icon colors
    icon: {
        primary: '#1B264F',
        secondary: '#666666',
        disabled: '#9E9E9E',
        inverse: '#FFFFFF',
    }
};

export const SPACING = {
    // Base unit: 4px
    none: 0,
    xxs: 4,
    xs: 8,
    sm: 12,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
    xxxl: 64,

    // Screen margins
    screenPadding: 24,

    // Component spacing
    buttonPadding: 16,
    inputPadding: 12,
};

export const FONTS = {
    regular: {
        fontFamily: 'System',
        fontWeight: '400',
    },
    medium: {
        fontFamily: 'System',
        fontWeight: '500',
    },
    bold: {
        fontFamily: 'System',
        fontWeight: '700',
    },
    family: {
        primary: 'System',
        secondary: 'Helvetica Neue',
    },

    weight: {
        light: '300',
        regular: '400',
        medium: '500',
        semiBold: '600',
        bold: '700',
    },

    size: {
        display: 32,
        h1: 24,
        h2: 20,
        h3: 18,
        body: 16,
        caption: 14,
        small: 12,
    },

    lineHeight: {
        display: 40,
        h1: 32,
        h2: 28,
        h3: 24,
        body: 24,
        caption: 20,
        small: 16,
    },

    letterSpacing: {
        tight: -0.5,
        normal: 0,
        wide: 0.5,
    }
};

export const BORDERS = {
    radius: {
        none: 0,
        xs: 4,
        sm: 8,
        md: 12,
        lg: 16,
        xl: 24,
        full: 1000,
    },
    width: {
        none: 0,
        hairline: 0.5,
        thin: 1,
        thick: 2,
    }
};

export const SHADOWS = {
    xs: {
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 1,
    },
    sm: {
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
    },
    md: {
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 6,
        elevation: 4,
    },
};

export const ICONS = {
    size: {
        xs: 16,
        sm: 24,
        md: 32,
        lg: 40,
        xl: 48,
    }
};

export const OPACITY = {
    disabled: 0.5,
    pressed: 0.8,
    hover: 0.9,
    overlay: 0.6,
};

export const Z_INDEX = {
    dropdown: 100,
    modal: 200,
    toast: 300,
    tooltip: 400,
};

// Component-specific theme constants
export const COMPONENTS = {
    button: {
        minHeight: 48,
        borderRadius: BORDERS.radius.md,
    },
    input: {
        minHeight: 56,
        borderRadius: BORDERS.radius.sm,
    },
    card: {
        borderRadius: BORDERS.radius.md,
        padding: SPACING.md,
    }
};

// Animation constants
export const ANIMATION = {
    duration: {
        quick: 150,
        normal: 300,
        slow: 500,
    },
    easing: {
        standard: 'cubic-bezier(0.4, 0, 0.2, 1)',
        decelerate: 'cubic-bezier(0.0, 0, 0.2, 1)',
        accelerate: 'cubic-bezier(0.4, 0, 1, 1)',
    }
};