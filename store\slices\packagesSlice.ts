import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import axios from "axios";
import { Package } from "@/types/package";
import {
  ApplyCodeRequest,
  ApplyCodeResponse,
  PackageBuyRequest,
  PackageBuyResponse,
  RazorOrderCreateRequest,
  RazorOrderCreateResponse,
  RazorOrderCreateSuccessResponse,
} from "@/types/payment";

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

interface PackagesState {
  packages: Package[];
  loading: boolean;
  error: string | null;
  appliedDiscounts: { [key: number]: ApplyCodeResponse };
  applyingCode: boolean;
  razorpayOrder: RazorOrderCreateSuccessResponse | null;
  creatingOrder: boolean;
}

// Use the imported types for consistency
type ApplyCodePayload = ApplyCodeRequest;

// Use the imported types for the purchase package payload
type PurchasePackagePayload = PackageBuyRequest;

const initialState: PackagesState = {
  packages: [],
  loading: false,
  error: null,
  appliedDiscounts: {},
  applyingCode: false,
  razorpayOrder: null,
  creatingOrder: false,
};

export const fetchPackages = createAsyncThunk(
  "packages/fetchPackages",
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/packages`);
      if (response.data.status) {
        return response.data.data;
      }
      return rejectWithValue(response.data.message);
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch packages"
      );
    }
  }
);

export const applyReferralCode = createAsyncThunk(
  "packages/applyReferralCode",
  async (payload: ApplyCodePayload, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: { token: string } };
      console.log("apply code payload:::", payload);
      const response = await axios.post(`${API_BASE_URL}/apply/code`, payload, {
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
      });

      if (response.data.status) {
        console.log("apply code response:::", response.data);
        return {
          packageId: payload.package_id,
          ...response.data,
        };
      }
      return rejectWithValue(response.data.message);
    } catch (error: any) {
      const message = error.response?.data?.message || "Failed to apply code";
      return rejectWithValue(message);
    }
  }
);

export const createRazorpayOrder = createAsyncThunk(
  "packages/createRazorpayOrder",
  async (payload: RazorOrderCreateRequest, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: { token: string } };
      const response = await axios.post(
        `${API_BASE_URL}/razor-order-create`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${state.auth.token}`,
          },
        }
      );

      if (response.data.success) {
        return response.data as RazorOrderCreateSuccessResponse;
      }
      return rejectWithValue(response.data.message || "Failed to create order");
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to create Razorpay order"
      );
    }
  }
);

export const purchasePackage = createAsyncThunk(
  "packages/purchasePackage",
  async (payload: PurchasePackagePayload, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: { token: string } };
      console.log("buy package payload:::", payload);
      const response = await axios.post(
        `${API_BASE_URL}/package/buy`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${state.auth.token}`,
          },
        }
      );

      if (response.data.status) {
        console.log("buy package response:::", response.data);
        return response.data;
      }
      return rejectWithValue(response.data.message);
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to purchase package"
      );
    }
  }
);

const packagesSlice = createSlice({
  name: "packages",
  initialState,
  reducers: {
    clearDiscount: (state, action: PayloadAction<number>) => {
      delete state.appliedDiscounts[action.payload];
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPackages.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPackages.fulfilled, (state, action) => {
        state.loading = false;
        state.packages = action.payload;
      })
      .addCase(fetchPackages.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(applyReferralCode.pending, (state) => {
        state.applyingCode = true;
        state.error = null;
      })
      .addCase(applyReferralCode.fulfilled, (state, action) => {
        state.applyingCode = false;
        state.appliedDiscounts[action.payload.packageId] = action.payload;
      })
      .addCase(applyReferralCode.rejected, (state, action) => {
        state.applyingCode = false;
        state.error = action.payload as string;
      })
      .addCase(createRazorpayOrder.pending, (state) => {
        state.creatingOrder = true;
        state.error = null;
      })
      .addCase(createRazorpayOrder.fulfilled, (state, action) => {
        state.creatingOrder = false;
        state.razorpayOrder = action.payload;
      })
      .addCase(createRazorpayOrder.rejected, (state, action) => {
        state.creatingOrder = false;
        state.error = action.payload as string;
      })
      .addCase(purchasePackage.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(purchasePackage.fulfilled, (state) => {
        state.loading = false;
        // Clear the Razorpay order after successful purchase
        state.razorpayOrder = null;
      })
      .addCase(purchasePackage.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearDiscount } = packagesSlice.actions;

export default packagesSlice.reducer;
