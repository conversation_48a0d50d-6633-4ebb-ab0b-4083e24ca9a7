// sellVehicleSlice.ts
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";

const BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

// Helper to get auth token
const getAuthToken = async () => {
  try {
    const token = await AsyncStorage.getItem("token");
    return token;
  } catch (error) {
    return null;
  }
};

// ---------------------------------------------------
// API Functions
// ---------------------------------------------------
const api = {
  fetchBrands: async (type: string) => {
    const response = await axios.get(`${BASE_URL}/brands`, {
      params: { type, limit: 10000, offset: 1 },
    });
    return response.data;
  },
  fetchModels: async (type: string, brandId: string) => {
    const response = await axios.get(`${BASE_URL}/models`, {
      params: { type, brand_id: brandId, limit: 10000 },
    });
    return response.data;
  },
  fetchVariants: async (modelId: string) => {
    const response = await axios.get(`${BASE_URL}/car-variants`, {
      params: { model_id: modelId, limit: 10000 },
    });
    return response.data;
  },
  fetchStates: async () => {
    const response = await axios.get(`${BASE_URL}/states/101`);
    return response.data;
  },
  fetchCities: async (stateId: string) => {
    const response = await axios.get(`${BASE_URL}/cities/${stateId}`);
    return response.data;
  },
  sellVehicle: async (type: string, data: any) => {
    const token = await getAuthToken();
    if (!token) {
      throw new Error("Authentication required");
    }

    const endpoint = `${BASE_URL}/${type}-sell`;
    const formData = new FormData();

    Object.keys(data).forEach((key) => {
      if (key === "images") {
        data[key].forEach((photo: any) => {
          // Cast file object as any to bypass type errors
          formData.append("images[]", {
            uri: photo.uri,
            type: "image/jpeg",
            name: photo.name,
          } as any);
        });
      } else {
        formData.append(key, data[key]);
      }
    });

    const response = await axios.post(endpoint, formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "multipart/form-data",
      },
    });

    return response.data;
  },
};

// A helper for authenticated GET requests
const authenticatedGet = async (url: string, params?: any) => {
  const token = await getAuthToken();
  if (!token) {
    throw new Error("Authentication required");
  }
  return axios.get(url, {
    headers: { Authorization: `Bearer ${token}` },
    params,
  });
};

// Use authenticatedGet in API methods
api.fetchBrands = async (type: string) => {
  const response = await authenticatedGet(`${BASE_URL}/brands`, {
    type,
    limit: 10000,
    offset: 1,
  });
  return response.data;
};

api.fetchModels = async (type: string, brandId: string) => {
  const response = await authenticatedGet(`${BASE_URL}/models`, {
    type,
    brand_id: brandId,
    limit: 10000,
  });
  return response.data;
};

api.fetchVariants = async (modelId: string) => {
  const response = await authenticatedGet(`${BASE_URL}/car-variants`, {
    model_id: modelId,
    limit: 10000,
  });
  return response.data;
};

// ---------------------------------------------------
// Async Thunks
// ---------------------------------------------------
interface Brand {
  id: number;
  name: string;
  key: string;
  image_url: string;
}

interface Model {
  id: number;
  name: string;
}

interface Variant {
  id: number;
  name: string;
}

interface State {
  id: number;
  name: string;
  country_id: string;
  country_code: string;
  country_name: string;
  state_code: string;
  latitude: string;
  longitude: string;
}

interface City {
  id: number;
  name: string;
  state_id: string;
  state_code: string;
  state_name: string;
  country_id: string;
  country_code: string;
  country_name: string;
  latitude: string;
  longitude: string;
}

export const fetchBrands = createAsyncThunk(
  "vehicle/fetchBrands",
  async (type: string) => {
    const data = await api.fetchBrands(type);
    const list = data.data ? data.data : data;
    // Ensure list is an array before mapping
    if (!Array.isArray(list)) {
      return [];
    }
    return list.map((brand: Brand) => ({
      label: brand.name,
      value: brand.id.toString(),
      image: brand.image_url,
      key: brand.key,
    }));
  }
);

export const fetchModels = createAsyncThunk(
  "vehicle/fetchModels",
  async ({ type, brandId }: { type: string; brandId: string }) => {
    const data = await api.fetchModels(type, brandId);
    const list = data.data ? data.data : data;
    // Ensure list is an array before mapping
    if (!Array.isArray(list)) {
      return [];
    }
    return list.map((model: Model) => ({
      label: model.name,
      value: model.id.toString(),
    }));
  }
);

export const fetchVariants = createAsyncThunk(
  "vehicle/fetchVariants",
  async (modelId: string) => {
    const data = await api.fetchVariants(modelId);
    const list = data.data ? data.data : data;
    // Ensure list is an array before mapping
    if (!Array.isArray(list)) {
      return [];
    }
    return list.map((variant: Variant) => ({
      label: variant.name,
      value: variant.id.toString(),
    }));
  }
);

export const fetchStates = createAsyncThunk("vehicle/fetchStates", async () => {
  const data = await api.fetchStates();
  const list = data.data ? data.data : data;
  // Ensure list is an array before mapping
  if (!Array.isArray(list)) {
    return [];
  }
  return list.map((state: State) => ({
    label: state.name,
    value: state.id.toString(),
  }));
});

export const fetchCities = createAsyncThunk(
  "vehicle/fetchCities",
  async (stateId: string) => {
    const data = await api.fetchCities(stateId);
    const list = data.data ? data.data : data;
    // Ensure list is an array before mapping
    if (!Array.isArray(list)) {
      return [];
    }
    return list.map((city: City) => ({
      label: city.name,
      value: city.id.toString(),
    }));
  }
);

export const sellVehicle = createAsyncThunk(
  "vehicle/sellVehicle",
  async (
    { type, formData }: { type: string; formData: any },
    { rejectWithValue }
  ) => {
    try {
      return await api.sellVehicle(type, formData);
    } catch (error: any) {
      if (error.response) {
        return rejectWithValue(error.response.data);
      } else if (error.request) {
        return rejectWithValue({ message: "No response from server" });
      } else {
        return rejectWithValue({ message: error.message });
      }
    }
  }
);

// ---------------------------------------------------
// Redux Slice
// ---------------------------------------------------
interface VehicleState {
  brands: { label: string; value: string; image?: string; key?: string }[];
  models: { label: string; value: string }[];
  variants: { label: string; value: string }[];
  states: { label: string; value: string }[];
  cities: { label: string; value: string }[];
  loading: boolean;
  error: string | null;
}

const initialState: VehicleState = {
  brands: [],
  models: [],
  variants: [],
  states: [],
  cities: [],
  loading: false,
  error: null,
};

const sellVehicleSlice = createSlice({
  name: "vehicle",
  initialState,
  reducers: {
    clearModelsAndVariants: (state) => {
      state.models = [];
      state.variants = [];
    },
    clearVariants: (state) => {
      state.variants = [];
    },
    clearCities: (state) => {
      state.cities = [];
    },
    clearBrands: (state) => {
      state.brands = [];
    },
    resetSellVehicleState: () => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        (action: any) => action.type.endsWith("/pending"),
        (state) => {
          state.loading = true;
          state.error = null;
        }
      )
      .addMatcher(
        (action: any) => action.type.endsWith("/fulfilled"),
        (state, action: any) => {
          state.loading = false;
          if (action.type.includes("fetchBrands")) {
            const brands = Array.isArray(action.payload) ? action.payload : [];
            state.brands = brands;
          } else if (action.type.includes("fetchModels")) {
            const models = Array.isArray(action.payload) ? action.payload : [];
            state.models = models;
          } else if (action.type.includes("fetchVariants")) {
            const variants = Array.isArray(action.payload)
              ? action.payload
              : [];
            state.variants = variants;
          } else if (action.type.includes("fetchStates")) {
            const states = Array.isArray(action.payload) ? action.payload : [];
            state.states = states;
          } else if (action.type.includes("fetchCities")) {
            const cities = Array.isArray(action.payload) ? action.payload : [];
            state.cities = cities;
          }
        }
      )
      .addMatcher(
        (action: any) => action.type.endsWith("/rejected"),
        (state, action: any) => {
          state.loading = false;
          state.error = action.error?.message ?? "An error occurred";
        }
      );
  },
});

export const {
  clearModelsAndVariants,
  clearVariants,
  clearCities,
  clearBrands,
  resetSellVehicleState,
} = sellVehicleSlice.actions;

export default sellVehicleSlice.reducer;
