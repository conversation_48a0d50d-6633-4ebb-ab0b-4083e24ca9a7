// components/payment/RazorpayCheckout.tsx
import React, { useState, useEffect } from 'react';
import { Platform, Alert } from 'react-native';
import { RazorpayOptions, RazorpaySuccessResponse } from '@/types/payment';
import ExpoRazorpayPayment from './ExpoRazorpayPayment';
import { processRazorpayPayment, isRazorpayAvailable } from '@/utils/nativeRazorpay';

interface RazorpayCheckoutProps {
  visible: boolean;
  options: RazorpayOptions;
  onClose: () => void;
  onPaymentSuccess: (response: RazorpaySuccessResponse) => void;
  onPaymentError: (error: string) => void;
  preferWebView?: boolean; // Set to true to force WebView implementation
}

/**
 * A unified component for Razorpay payments that tries to use the native SDK first,
 * and falls back to WebView if the native SDK is not available.
 */
const RazorpayCheckout: React.FC<RazorpayCheckoutProps> = ({
  visible,
  options,
  onClose,
  onPaymentSuccess,
  onPaymentError,
  preferWebView = false
}) => {
  const [useWebView, setUseWebView] = useState(preferWebView);
  const [isProcessing, setIsProcessing] = useState(false);

  // Check if native Razorpay is available on component mount
  useEffect(() => {
    if (!preferWebView) {
      console.log('Checking native Razorpay SDK availability...');
      checkRazorpayAvailability();
    } else {
      console.log('WebView implementation preferred by user setting');
      setUseWebView(true);
    }
  }, [preferWebView]);

  // When visibility changes to true, start the payment process
  useEffect(() => {
    if (!visible) return;

    console.log('RazorpayCheckout visibility changed to visible');
    console.log('Using implementation:', useWebView ? 'WebView' : 'Native SDK');

    if (!useWebView && !isProcessing) {
      console.log('Starting native payment process');
      handleNativePayment();
    } else if (useWebView) {
      console.log('Using WebView payment implementation');
    }
  }, [visible, useWebView, options]);

  // Log when options change
  useEffect(() => {
    if (options) {
      console.log('RazorpayCheckout options updated:', {
        key: options.key.substring(0, 4) + '...',
        order_id: options.order_id,
        amount: options.amount
      });
    }
  }, [options]);

  // Check if native Razorpay SDK is available
  const checkRazorpayAvailability = async () => {
    try {
      console.log('Starting native Razorpay SDK availability check...');
      const available = await isRazorpayAvailable();

      if (available) {
        console.log('✅ Native Razorpay SDK is available and will be used');
        setUseWebView(false);
      } else {
        console.log('❌ Native Razorpay SDK is not available, falling back to WebView');
        setUseWebView(true);
      }
    } catch (error) {
      console.error('Error checking Razorpay availability:', error);
      console.log('Falling back to WebView implementation due to error');
      setUseWebView(true);
    }
  };

  // Handle payment using the native SDK
  const handleNativePayment = async () => {
    if (!options || isProcessing) {
      console.log('Cannot start payment: options missing or already processing', {
        hasOptions: !!options,
        isProcessing
      });
      return;
    }

    console.log('Starting native Razorpay payment with options:', {
      key: options.key.substring(0, 4) + '...',
      order_id: options.order_id,
      amount: options.amount,
      currency: options.currency
    });

    setIsProcessing(true);
    try {
      // Add a small delay to ensure UI state is updated
      await new Promise(resolve => setTimeout(resolve, 500));

      const success = await processRazorpayPayment(
        options,
        (response) => {
          console.log('Native payment successful:', response.razorpay_payment_id);
          setIsProcessing(false);
          onPaymentSuccess(response);
        },
        (error) => {
          console.log('Native payment error:', error);
          setIsProcessing(false);

          // If there's an error with the native SDK, try WebView as fallback
          if (error.includes('SDK') || error.includes('not available') ||
            error.includes('failed') || error.includes('Failed')) {
            console.log('Falling back to WebView implementation due to native SDK error');
            setUseWebView(true);
          } else {
            onPaymentError(error);
          }
        }
      );

      if (!success) {
        console.log('Native payment initialization failed, falling back to WebView');
        setIsProcessing(false);
        setUseWebView(true); // Fall back to WebView if native payment couldn't be initiated
      }
    } catch (error) {
      console.error('Error in native payment:', error);
      setIsProcessing(false);
      setUseWebView(true); // Fall back to WebView on error
    }
  };

  // If using WebView, render the WebView component
  if (useWebView) {
    console.log('Rendering ExpoRazorpayPayment WebView component');
    return (
      <ExpoRazorpayPayment
        visible={visible}
        options={options}
        onClose={() => {
          console.log('WebView payment closed');
          onClose();
        }}
        onPaymentSuccess={(response) => {
          console.log('WebView payment successful');
          onPaymentSuccess(response);
        }}
        onPaymentError={(error) => {
          console.log('WebView payment error:', error);
          onPaymentError(error);
        }}
      />
    );
  }

  // If using native SDK, we don't need to render anything
  // as the native SDK will show its own UI
  console.log('Using native SDK - no UI rendered by React Native');
  return null;
};

export default RazorpayCheckout;
