import React, { useState } from 'react';
import {
    View,
    TextInput,
    StyleSheet,
    Alert,
    ActivityIndicator,
    TouchableOpacity,
    Platform,
    KeyboardAvoidingView,
    SafeAreaView,
    StatusBar,
    Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from '@/components/common/ThemedText';
import ThemedButton from '@/components/common/ThemedButton';
import { COLORS, SPACING } from '@/constants/theme';
import { useAppDispatch } from '@/store/hooks';
import { registerWithPhone } from '@/store/slices/authSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Formik } from 'formik';
import * as Yup from 'yup';

const { width } = Dimensions.get('window');

const PhoneValidationSchema = Yup.object().shape({
    phone: Yup.string()
        .matches(/^\+?[\d\s-]{10,}$/, 'Please enter a valid phone number')
        .required('Phone number is required'),
});

export default function PhoneInputScreen() {
    const dispatch = useAppDispatch();
    const router = useRouter();
    const { pendingUid } = useLocalSearchParams<{ pendingUid: string }>();
    const [loader, setLoader] = useState(false);

    const handlePhoneSubmit = async (values: { phone: string }, { setFieldError }: any) => {
        if (!pendingUid) {
            Alert.alert('Error', 'Missing user information');
            return;
        }

        try {
            setLoader(true);
            const result = await dispatch(
                registerWithPhone({
                    uid: pendingUid,
                    phone: values.phone.trim()
                })
            ).unwrap();

            if (result.status && result.authorisation?.token) {
                await AsyncStorage.setItem('token', result.authorisation.token);
                await AsyncStorage.setItem('user', JSON.stringify(result.user));
                router.replace('/(auth)/(tabs)');
            } else {
                throw new Error(result.message || 'Phone registration failed');
            }
        } catch (error: any) {
            setFieldError('phone', error.message || 'Failed to register phone');
        } finally {
            setLoader(false);
        }
    };

    return (
        <SafeAreaView style={styles.container}>
            <StatusBar barStyle="dark-content" backgroundColor={COLORS.background} />

            {/* Fixed header with properly positioned back button */}
            <View style={styles.headerWrapper}>
                <TouchableOpacity
                    onPress={() => router.navigate("/auth/register")}
                    style={styles.backButton}
                    activeOpacity={0.7}
                >
                    <Ionicons name="chevron-back" size={24} color={COLORS.text.primary} />
                </TouchableOpacity>
                <View style={styles.headerContainer}>
                    <ThemedText style={styles.headerTitle}>Login to account</ThemedText>
                </View>
            </View>

            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={styles.contentContainer}
            >
                <View style={styles.formWrapper}>
                    <ThemedText style={styles.title}>Welcome Back</ThemedText>
                    <ThemedText style={styles.subtitle}>Enter your phone number to continue</ThemedText>

                    <Formik
                        initialValues={{ phone: '' }}
                        validationSchema={PhoneValidationSchema}
                        onSubmit={handlePhoneSubmit}
                    >
                        {({
                            handleChange,
                            handleBlur,
                            handleSubmit,
                            values,
                            errors,
                            touched
                        }) => (
                            <View style={styles.formContainer}>
                                <View style={styles.inputContainer}>
                                    <Ionicons
                                        name="call-outline"
                                        size={20}
                                        color={COLORS.text.secondary}
                                        style={styles.inputIcon}
                                    />
                                    <TextInput
                                        style={[
                                            styles.phoneInput,
                                            touched.phone && errors.phone && styles.inputError
                                        ]}
                                        value={values.phone}
                                        onChangeText={handleChange('phone')}
                                        onBlur={handleBlur('phone')}
                                        placeholder="Phone Number (e.g., +1234567890)"
                                        placeholderTextColor="#666"
                                        keyboardType="phone-pad"
                                        autoFocus
                                    />
                                </View>

                                {touched.phone && errors.phone && (
                                    <ThemedText style={styles.errorText}>
                                        {errors.phone}
                                    </ThemedText>
                                )}

                                <ThemedButton
                                    onPress={handleSubmit}
                                    disabled={loader}
                                    style={styles.submitButton}
                                >
                                    {loader ? (
                                        <View style={styles.loadingContainer}>
                                            <ActivityIndicator
                                                color="#000"
                                                style={styles.activityIndicator}
                                            />
                                            <ThemedText style={styles.submitButtonText}>
                                                Submitting...
                                            </ThemedText>
                                        </View>
                                    ) : (
                                        <ThemedText style={styles.submitButtonText}>
                                            Submit
                                        </ThemedText>
                                    )}
                                </ThemedButton>

                                <ThemedText style={styles.tosText}>
                                    By continuing, you agree to our{' '}
                                    <ThemedText style={styles.link}>Terms of Service</ThemedText> and{' '}
                                    <ThemedText style={styles.link}>Privacy Policy</ThemedText>.
                                </ThemedText>
                            </View>
                        )}
                    </Formik>
                </View>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.background,
    },
    headerWrapper: {
        position: 'relative',
        paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
        width: '100%',
    },
    headerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: 60,
        paddingTop: Platform.OS === 'ios' ? 10 : 0,
    },
    backButton: {
        position: 'absolute',
        left: SPACING.lg,
        top: Platform.OS === 'ios' ? (StatusBar.currentHeight || 20) + 16 : (StatusBar.currentHeight || 20) + 10,
        zIndex: 10,
        padding: SPACING.xs,
        backgroundColor: COLORS.background,
        borderRadius: 20,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: COLORS.text.primary,
    },
    contentContainer: {
        flex: 1,
        justifyContent: 'center',
    },
    formWrapper: {
        paddingHorizontal: SPACING.lg,
        width: '100%',
        alignSelf: 'center',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        color: COLORS.text.primary,
        marginBottom: SPACING.xs,
        textAlign: 'center',
    },
    subtitle: {
        textAlign: 'center',
        fontSize: 16,
        marginBottom: SPACING.xl,
        color: COLORS.text.secondary,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
        marginBottom: SPACING.md,
    },
    inputIcon: {
        marginLeft: SPACING.md,
    },
    phoneInput: {
        flex: 1,
        padding: 12,
        fontSize: 16,
        color: COLORS.text.primary,
    },
    formContainer: {
        width: '100%',
    },
    submitButton: {
        width: '100%',
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: SPACING.md,
        marginBottom: SPACING.md,
    },
    submitButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    activityIndicator: {
        marginRight: 8,
    },
    tosText: {
        fontSize: 12,
        textAlign: 'center',
        marginTop: SPACING.md,
        color: COLORS.text.secondary,
    },
    link: {
        textDecorationLine: 'underline',
        color: "blue",
        fontWeight: '500',
    },
    inputError: {
        borderColor: COLORS.error || 'red',
    },
    errorText: {
        color: COLORS.error || 'red',
        fontSize: 13,
        marginTop: -4,
        marginBottom: SPACING.sm,
        marginLeft: 4,
    },
});