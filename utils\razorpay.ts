// utils/razorpay.ts
import { RazorpayOptions, RazorpaySuccessResponse } from "@/types/payment";
import { WebView } from "react-native-webview";
import { Alert, Linking } from "react-native";

// HTML template for Razorpay checkout
const generateRazorpayHTML = (options: RazorpayOptions) => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
      <meta http-equiv="Content-Security-Policy" content="default-src * 'self' 'unsafe-inline' 'unsafe-eval' data: gap: https://ssl.gstatic.com https://checkout.razorpay.com;">
      <title>Razorpay Payment</title>
      <style>
        html, body {
          margin: 0;
          padding: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
          width: 100%;
          height: 100%;
          overflow: hidden;
          background-color: #f5f5f5;
          -webkit-tap-highlight-color: transparent;
        }
        body {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100vh;
        }
        .container {
          text-align: center;
          padding: 20px;
          width: 100%;
          max-width: 400px;
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h3 {
          margin-top: 0;
          color: #333;
        }
        p {
          color: #666;
          margin-bottom: 20px;
        }
        button {
          background-color: #528FF0;
          color: white;
          border: none;
          padding: 12px 24px;
          font-size: 16px;
          border-radius: 4px;
          cursor: pointer;
          width: 100%;
          transition: background-color 0.2s;
        }
        button:hover {
          background-color: #4070D0;
        }
        #razorpay-container {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 1000;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h3>Razorpay Payment</h3>
        <p>Please wait while we initialize the payment gateway...</p>
        <div id="razorpay-container"></div>
      </div>

      <script src="https://checkout.razorpay.com/v1/checkout.js" crossorigin="anonymous"></script>
      <script>
        // Prevent multiple initializations
        var razorpayInitialized = false;
        var razorpayInstance = null;
        var initializationAttempts = 0;
        var maxAttempts = 5; // Increased max attempts
        var scriptLoadAttempts = 0;
        var maxScriptLoadAttempts = 3;

        // Function to check if Razorpay script is loaded
        function isRazorpayLoaded() {
          return typeof Razorpay !== 'undefined';
        }

        // Function to load Razorpay script manually if needed
        function loadRazorpayScript() {
          if (isRazorpayLoaded() || scriptLoadAttempts >= maxScriptLoadAttempts) return;

          scriptLoadAttempts++;
          console.log('Loading Razorpay script manually, attempt ' + scriptLoadAttempts);

          var script = document.createElement('script');
          script.src = 'https://checkout.razorpay.com/v1/checkout.js';
          script.async = true;
          script.crossOrigin = 'anonymous';
          script.onload = function() {
            console.log('Razorpay script loaded manually');
            initRazorpay();
          };
          script.onerror = function() {
            console.error('Failed to load Razorpay script manually');
            if (scriptLoadAttempts < maxScriptLoadAttempts) {
              setTimeout(loadRazorpayScript, 1500);
            } else {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                error: true,
                error_description: 'Failed to load Razorpay script after multiple attempts'
              }));
            }
          };
          document.head.appendChild(script);
        }

        function initRazorpay() {
          if (razorpayInitialized || initializationAttempts >= maxAttempts) return;

          initializationAttempts++;
          console.log('Initializing Razorpay, attempt ' + initializationAttempts);

          try {
            // Check if Razorpay is available
            if (!isRazorpayLoaded()) {
              console.log('Razorpay not loaded yet, retrying in 1 second...');

              // Try to load the script manually if we've already tried a few times
              if (initializationAttempts > 2) {
                loadRazorpayScript();
              } else {
                setTimeout(initRazorpay, 1000);
              }
              return;
            }

            razorpayInitialized = true;
            console.log('Razorpay SDK loaded successfully');

            // Parse options with error handling
            var options;
            try {
              options = ${JSON.stringify(options)};
              console.log('Razorpay options parsed successfully');
            } catch (parseError) {
              console.error('Error parsing Razorpay options:', parseError);
              window.ReactNativeWebView.postMessage(JSON.stringify({
                error: true,
                error_description: 'Invalid payment configuration'
              }));
              return;
            }

            // Validate required fields
            if (!options.key || !options.amount || !options.order_id) {
              console.error('Missing required Razorpay options:',
                !options.key ? 'key' : !options.amount ? 'amount' : 'order_id');
              window.ReactNativeWebView.postMessage(JSON.stringify({
                error: true,
                error_description: 'Invalid payment configuration: missing required fields'
              }));
              return;
            }

            // Configure handlers
            options.handler = function(response) {
              console.log('Payment successful');
              // Send the payment response back to React Native
              window.ReactNativeWebView.postMessage(JSON.stringify(response));
            };

            options.modal = {
              ondismiss: function() {
                console.log('Payment modal dismissed');
                window.ReactNativeWebView.postMessage(JSON.stringify({ cancelled: true }));
              },
              escape: false,
              backdropclose: false,
              animate: false
            };

            // Add error handling
            options.notes = options.notes || {};
            options.notes.platform = 'react-native-webview';
            options.notes.integration_type = 'webview';
            options.notes.attempt = initializationAttempts.toString();

            // Create Razorpay instance
            console.log('Creating Razorpay instance');
            try {
              razorpayInstance = new Razorpay(options);
              console.log('Razorpay instance created successfully');
            } catch (instanceError) {
              console.error('Error creating Razorpay instance:', instanceError);
              window.ReactNativeWebView.postMessage(JSON.stringify({
                error: true,
                error_description: instanceError.message || 'Failed to create Razorpay instance'
              }));
              return;
            }

            // Add event listeners
            razorpayInstance.on('payment.failed', function(response) {
              console.log('Payment failed:', response.error);
              window.ReactNativeWebView.postMessage(JSON.stringify({
                error: true,
                error_code: response.error.code,
                error_description: response.error.description,
                error_source: response.error.source,
                error_step: response.error.step,
                error_reason: response.error.reason
              }));
            });

            // Add network error handler
            razorpayInstance.on('payment.error', function(data) {
              console.error('Payment error event:', data);
              window.ReactNativeWebView.postMessage(JSON.stringify({
                error: true,
                error_description: 'Payment error: ' + (data.error?.description || 'Unknown error')
              }));
            });

            // Open Razorpay checkout after a delay to ensure the WebView is fully loaded
            console.log('Opening Razorpay checkout in 1 second...');
            setTimeout(function() {
              try {
                console.log('Opening Razorpay checkout now');
                razorpayInstance.open();
                console.log('Razorpay checkout opened successfully');

                // Set a timeout to check if the modal is actually visible
                setTimeout(function() {
                  if (document.querySelector('.razorpay-container') === null) {
                    console.warn('Razorpay container not found in DOM after open');
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                      error: true,
                      error_description: 'Payment form did not appear. Please try again.'
                    }));
                  }
                }, 3000);
              } catch (openError) {
                console.error('Error opening Razorpay:', openError);
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  error: true,
                  error_description: openError.message || 'Failed to open Razorpay checkout'
                }));
              }
            }, 1000);
          } catch (error) {
            console.error('Error initializing Razorpay:', error);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              error: true,
              error_description: error.message || 'Failed to initialize Razorpay'
            }));

            // Retry initialization if not reached max attempts
            if (initializationAttempts < maxAttempts) {
              console.log('Retrying Razorpay initialization...');
              setTimeout(initRazorpay, 1500);
            }
          }
        }

        // Initialize Razorpay when the page is fully loaded
        if (document.readyState === 'complete') {
          console.log('Document ready, initializing Razorpay');
          initRazorpay();
        } else {
          console.log('Document not ready, waiting for load event');
          window.addEventListener('load', function() {
            console.log('Document loaded, initializing Razorpay');
            initRazorpay();
          });
        }

        // Fallback initialization after a timeout
        console.log('Setting fallback initialization timeout');
        setTimeout(function() {
          console.log('Fallback initialization triggered');
          initRazorpay();
        }, 2000);

        // Handle errors
        window.onerror = function(message, source, lineno, colno, error) {
          console.error('JavaScript error:', message, 'at', source, lineno, colno);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            error: true,
            error_description: 'JavaScript error: ' + message
          }));
          return true;
        };

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', function(event) {
          console.error('Unhandled promise rejection:', event.reason);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            error: true,
            error_description: 'Unhandled promise rejection: ' + (event.reason?.message || event.reason || 'Unknown error')
          }));
        });
      </script>
    </body>
    </html>
  `;
};

// Function to handle WebView messages with enhanced error handling
const handleRazorpayResponse = (
  data: string,
  onSuccess: (response: RazorpaySuccessResponse) => void,
  onFailure: (error: string) => void,
  onCancel: () => void
) => {
  try {
    // Log the raw data for debugging
    console.log("Received WebView message:", data);

    // Check if data is empty or invalid
    if (!data || typeof data !== "string" || data.trim() === "") {
      console.error("Empty or invalid WebView message received");
      onFailure("Invalid response from payment gateway. Please try again.");
      return;
    }

    // Parse the response with error handling
    let response;
    try {
      response = JSON.parse(data);
    } catch (parseError) {
      console.error(
        "Error parsing Razorpay response:",
        parseError,
        "Raw data:",
        data
      );
      onFailure(
        "Invalid response format from payment gateway. Please try again."
      );
      return;
    }

    // Handle cancellation
    if (response.cancelled) {
      console.log("Payment was cancelled by user");
      onCancel();
      return;
    }

    // Handle explicit error from Razorpay
    if (response.error) {
      // Log detailed error information
      console.error("Razorpay error:", JSON.stringify(response, null, 2));

      // Build a more descriptive error message
      let errorMessage =
        response.error_description || "Payment failed. Please try again.";

      // Add more context based on error code if available
      if (response.error_code) {
        console.log("Error code:", response.error_code);

        switch (response.error_code) {
          case "BAD_REQUEST_ERROR":
            errorMessage =
              "Invalid payment details. Please check and try again.";
            break;
          case "NETWORK_ERROR":
            errorMessage =
              "Network error. Please check your internet connection.";
            break;
          case "GATEWAY_ERROR":
            errorMessage = "Payment gateway error. Please try again.";
            break;
          case "INTERNAL_SERVER_ERROR":
            errorMessage = "Server error. Please try again later.";
            break;
        }
      }

      // Add source and step information if available
      if (response.error_source && response.error_step) {
        console.log(
          `Error at ${response.error_source} during ${response.error_step}`
        );
      }

      onFailure(errorMessage);
      return;
    }

    // Handle successful payment
    if (response.razorpay_payment_id) {
      // Validate that we have all required fields for a successful payment
      if (!response.razorpay_order_id || !response.razorpay_signature) {
        console.warn("Incomplete success response:", response);
        onFailure(
          "Incomplete payment information received. Please contact support."
        );
        return;
      }

      console.log("Payment successful:", response.razorpay_payment_id);
      onSuccess(response as RazorpaySuccessResponse);
      return;
    }

    // Handle any other unexpected response
    console.warn(
      "Unexpected Razorpay response:",
      JSON.stringify(response, null, 2)
    );

    // Try to provide more context about the unexpected response
    let unexpectedMessage = "Payment could not be completed. Please try again.";
    if (Object.keys(response).length === 0) {
      unexpectedMessage =
        "Empty response received from payment gateway. Please try again.";
    } else if (Object.keys(response).join(",").includes("razorpay")) {
      unexpectedMessage =
        "Incomplete payment information. Please try again or contact support.";
    }

    onFailure(unexpectedMessage);
  } catch (error: any) {
    // Handle any unexpected errors in our own code
    console.error("Error handling Razorpay response:", error?.message || error);
    console.error("Raw data that caused the error:", data);
    onFailure("An unexpected error occurred. Please try again.");
  }
};

export { generateRazorpayHTML, handleRazorpayResponse };
