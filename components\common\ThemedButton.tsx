import React from 'react';
import { TouchableOpacity, StyleSheet, TextStyle, ViewStyle, StyleProp } from 'react-native';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS } from '@/constants/theme';

interface ThemedButtonProps {
    children: React.ReactNode;
    style?: StyleProp<ViewStyle>;
    containerStyle?: StyleProp<ViewStyle>;
    onPress?: () => void;
    disabled?: boolean;
    loading?: boolean;
    textStyle?: TextStyle;
}

export default function ThemedButton({ style, onPress, children, disabled = false, textStyle }: ThemedButtonProps) {
    return (
        <TouchableOpacity
            style={[styles.button, style, disabled && styles.buttonDisabled]}
            onPress={onPress}
            disabled={disabled}
        >
            <ThemedText style={[styles.buttonText, textStyle]}>{children}</ThemedText>
        </TouchableOpacity>
    );
}

const styles = StyleSheet.create({
    button: {
        width: '100%',
        backgroundColor: COLORS.primary,
        padding: 16,
        borderRadius: 12,
        alignItems: 'center',
        marginTop: 8,
        shadowColor: COLORS.primary,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    buttonText: {
        color: COLORS.white,
        fontSize: 16,
        fontWeight: 'bold',
    },
    buttonDisabled: {
        opacity: 0.7,
        backgroundColor: COLORS.primary + '80',
    },
});