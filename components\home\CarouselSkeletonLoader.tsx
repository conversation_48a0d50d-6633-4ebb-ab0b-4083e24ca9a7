import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { ShimmerPlaceholder } from '@/components/common/ShimmerPlaceholder';
import { SPACING } from '@/constants/theme';

export const CarouselSkeletonLoader = () => {
  const screenWidth = Dimensions.get('window').width;
  
  return (
    <View style={styles.carouselContainer}>
      <View style={styles.carouselWrapper}>
        <View style={[styles.carouselItemWrapper, { width: screenWidth - (SPACING.md * 2) }]}>
          <ShimmerPlaceholder style={styles.carouselImage} />
        </View>
        
        {/* Pagination dots skeleton */}
        <View style={styles.pagination}>
          {[...Array(3)].map((_, index) => (
            <View key={index} style={styles.paginationDot} />
          ))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  carouselContainer: {
    marginBottom: SPACING.md,
    paddingHorizontal: SPACING.md,
  },
  carouselWrapper: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  carouselItemWrapper: {
    // width will be set dynamically
  },
  carouselImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
  },
  pagination: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 10,
    alignSelf: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
});
