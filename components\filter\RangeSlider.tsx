import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, SPACING, FONTS, BORDERS, SHADOWS } from '@/constants/theme';
import { Slider } from '@miblanchard/react-native-slider';

interface RangeSliderProps {
  title: string;
  minValue: number;
  maxValue: number;
  step: number;
  initialMinValue?: number;
  initialMaxValue?: number;
  formatValue: (value: number) => string;
  onChange: (minValue: number, maxValue: number) => void;
  onValueChange?: (minValue: number, maxValue: number) => void;
}

const RangeSlider: React.FC<RangeSliderProps> = ({
  title,
  minValue,
  maxValue,
  step,
  initialMinValue,
  initialMaxValue,
  formatValue,
  onChange,
  onValueChange
}) => {
  // Initialize with props or defaults
  const [rangeValues, setRangeValues] = useState<[number, number]>([
    initialMinValue !== undefined ? initialMinValue : minValue,
    initialMaxValue !== undefined ? initialMaxValue : maxValue
  ]);

  // Update local state when props change
  useEffect(() => {
    const newValues: [number, number] = [
      initialMinValue !== undefined ? initialMinValue : rangeValues[0],
      initialMaxValue !== undefined ? initialMaxValue : rangeValues[1]
    ];
    setRangeValues(newValues);
  }, [initialMinValue, initialMaxValue]);

  // Handle slider value change
  const handleValueChange = (values: number | number[]) => {
    const newValues: [number, number] = Array.isArray(values) ? [values[0], values[1]] : [values, rangeValues[1]];
    setRangeValues(newValues);

    // Call the optional onValueChange callback if provided
    if (onValueChange) {
      onValueChange(newValues[0], newValues[1]);
    }
  };

  // Handle slider sliding complete
  const handleSlidingComplete = (values: number | number[]) => {
    const newValues: [number, number] = Array.isArray(values) ? [values[0], values[1]] : [values, rangeValues[1]];
    onChange(newValues[0], newValues[1]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>

      <View style={styles.rangeLabels}>
        <Text style={styles.rangeLabel}>{formatValue(rangeValues[0])}</Text>
        <Text style={styles.rangeLabel}>{formatValue(rangeValues[1])}</Text>
      </View>

      <View style={styles.sliderContainer}>
        <Slider
          minimumValue={minValue}
          maximumValue={maxValue}
          step={step}
          value={rangeValues}
          onValueChange={handleValueChange}
          onSlidingComplete={handleSlidingComplete}
          minimumTrackTintColor={COLORS.primary}
          maximumTrackTintColor={COLORS.gray[300]}
          thumbTintColor={COLORS.primary}
          trackClickable={true}
        />
      </View>

      <View style={styles.rangeLabels}>
        <Text style={styles.rangeHint}>Min</Text>
        <Text style={styles.rangeHint}>Max</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDERS.radius.md,
    ...SHADOWS.xs,
  },
  title: {
    fontWeight: '600',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    marginBottom: SPACING.md,
    letterSpacing: 0.3,
  },
  rangeLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs,
  },
  rangeLabel: {
    fontWeight: '500',
    fontSize: FONTS.size.caption,
    color: COLORS.primary,
  },
  rangeHint: {
    fontWeight: '400',
    fontSize: FONTS.size.small,
    color: COLORS.text.secondary,
  },
  sliderContainer: {
    width: '100%',
    height: 40,
    marginVertical: SPACING.md,
  },
});

export default RangeSlider;
