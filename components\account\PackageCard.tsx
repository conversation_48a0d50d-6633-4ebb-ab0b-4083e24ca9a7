import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import PackagePurchaseDialog from './PackagePurchaseDialog'; // Import the dialog component

export default function PackageCard({
    pkg,
    appliedDiscount,
    referralCode,
    referralError,
    isProcessing,
    onReferralCodeChange,
    onApplyReferral,
    onClearReferral,
    onPurchasePackage // New prop for handling package purchase
}:
    {
        pkg: any;
        appliedDiscount: any;
        referralCode: string;
        referralError: string;
        isProcessing: boolean;
        onReferralCodeChange: (text: string) => void;
        onApplyReferral: () => void;
        onClearReferral: () => void;
        onPurchasePackage: (data: { package: { id: number; name: string; }; finalAmount: number; paymentMethod: string; appliedDiscount: any; referralCode: string; paymentId?: string; }) => Promise<void>;
    }
) {
    // State to control dialog visibility
    const [dialogVisible, setDialogVisible] = useState(false);

    // Handle purchase confirmation
    const handleBuyPackage = (data: { packageId: number; paymentMethod: string; finalAmount: number; paymentId?: string; }) => {
        onPurchasePackage({
            package: pkg,
            appliedDiscount: appliedDiscount,
            referralCode: referralCode,
            paymentId: data.paymentId,
            ...data
        });
    };

    return (
        <View style={styles.packageCard}>
            <View style={styles.packageHeader}>
                <Text style={styles.packageTitle}>{pkg.name}</Text>
            </View>

            <View style={styles.packageContent}>
                {renderPackagePrice(pkg, appliedDiscount)}

                <View style={styles.bulletList}>
                    {pkg.description.map((desc: string[], index: React.Key | null | undefined) => (
                        <View key={index} style={styles.bulletItem}>
                            <Text style={styles.bulletPoint}>•</Text>
                            <Text style={styles.bulletText}>
                                {desc[0].replace(/&nbsp;/g, ' ')}
                            </Text>
                        </View>
                    ))}
                </View>

                <View style={styles.referralWrapper}>
                    <View style={styles.referralContainer}>
                        <TextInput
                            style={[
                                styles.referralInput,
                                appliedDiscount && styles.referralInputSuccess,
                                referralError && styles.referralInputError
                            ]}
                            placeholder="Enter Referral Code"
                            placeholderTextColor="#999"
                            value={referralCode}
                            onChangeText={onReferralCodeChange}
                            editable={!appliedDiscount}
                        />
                        {appliedDiscount ? (
                            <TouchableOpacity
                                style={styles.clearButton}
                                onPress={onClearReferral}
                            >
                                <Text style={styles.clearButtonText}>Remove</Text>
                            </TouchableOpacity>
                        ) : (
                            <TouchableOpacity
                                style={[
                                    styles.applyButton,
                                    isProcessing && styles.applyButtonDisabled
                                ]}
                                onPress={onApplyReferral}
                                disabled={isProcessing}
                            >
                                {isProcessing ? (
                                    <ActivityIndicator color="#fff" size="small" />
                                ) : (
                                    <Text style={styles.applyButtonText}>Apply</Text>
                                )}
                            </TouchableOpacity>
                        )}
                    </View>
                    {referralError ? (
                        <Text style={styles.errorMessage}>{referralError}</Text>
                    ) : null}
                    {appliedDiscount ? (
                        <Text style={styles.successMessage}>Discount Applied: ₹ {appliedDiscount.discount_amount}</Text>
                    ) : null}
                </View>

                <TouchableOpacity
                    style={styles.getPackageButton}
                    onPress={() => setDialogVisible(true)}
                >
                    <Text style={styles.getPackageButtonText}>Get Package</Text>
                </TouchableOpacity>
            </View>

            {/* Purchase Dialog */}
            <PackagePurchaseDialog
                visible={dialogVisible}
                onClose={() => setDialogVisible(false)}
                packageData={pkg}
                appliedDiscount={appliedDiscount}
                onBuyPackage={handleBuyPackage}
                referralCode={referralCode}
                referralError={referralError}
            />
        </View>
    );
};

const renderPackagePrice = (pkg: { amount: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | null | undefined; }, appliedDiscount: { final_amount: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; discount_amount: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; }) => {
    if (appliedDiscount) {
        return (
            <View style={styles.priceContainer}>
                <Text style={styles.originalPrice}>₹ {pkg.amount} /-</Text>
                <Text style={styles.discountedPrice}>₹ {appliedDiscount.final_amount} /-</Text>
                <Text style={styles.savedAmount}>
                    You save: ₹ {appliedDiscount.discount_amount}
                </Text>
            </View>
        );
    }

    return <Text style={styles.price}>₹ {pkg.amount} /-</Text>;
};

// Styles remain the same as in your original code
const styles = StyleSheet.create({
    packageCard: {
        borderRadius: 8,
        marginBottom: 24,
        backgroundColor: 'white',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        overflow: 'hidden',
        borderWidth: 1,
        borderColor: '#eee',
    },
    packageHeader: {
        backgroundColor: '#FF5722', // Orange
        padding: 12,
        alignItems: 'center',
    },
    packageTitle: {
        color: 'white',
        fontWeight: '500',
        fontSize: 16,
    },
    packageContent: {
        padding: 16,
    },
    price: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 16,
    },
    bulletList: {
        marginBottom: 16,
    },
    bulletItem: {
        flexDirection: 'row',
        marginBottom: 6,
    },
    bulletPoint: {
        marginRight: 8,
        fontSize: 16,
    },
    bulletText: {
        flex: 1,
        fontSize: 14,
    },
    blueLinkText: {
        color: '#2196F3',
    },
    referralWrapper: {
        marginBottom: 16,
        marginTop: 8,
    },
    referralContainer: {
        flexDirection: 'row',
        marginBottom: 4,
    },
    referralInput: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 4,
        padding: 8,
        fontSize: 14,
    },
    referralInputError: {
        borderColor: '#FF0000',
    },
    errorMessage: {
        color: '#FF0000',
        fontSize: 14,
        textAlign: 'center',
        marginTop: 4,
        marginBottom: 8,
    },
    successMessage: {
        color: '#008000',
        fontSize: 14,
        textAlign: 'center',
        marginTop: 4,
        marginBottom: 8,
    },
    applyButton: {
        backgroundColor: '#FF5722',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 4,
        marginLeft: 8,
        justifyContent: 'center',
    },
    applyButtonText: {
        color: 'white',
        fontSize: 14,
    },
    getPackageButton: {
        backgroundColor: '#1E2A47',
        padding: 12,
        borderRadius: 4,
        alignItems: 'center',
    },
    getPackageButtonText: {
        color: 'white',
        fontWeight: '500',
        fontSize: 16,
    },
    priceContainer: {
        alignItems: 'center',
        marginBottom: 16,
    },
    originalPrice: {
        fontSize: 18,
        textDecorationLine: 'line-through',
        color: '#757575',
    },
    discountedPrice: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#008000',
        marginLeft: 8,
    },
    savedAmount: {
        fontSize: 14,
        color: '#4CAF50',
        marginTop: 4,
    },
    referralInputSuccess: {
        borderColor: '#4CAF50',
        backgroundColor: '#E8F5E9',
    },
    clearButton: {
        backgroundColor: '#FF5722',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 4,
        marginLeft: 8,
        justifyContent: 'center',
    },
    clearButtonText: {
        color: 'white',
        fontSize: 14,
    },
    applyButtonDisabled: {
        opacity: 0.7,
    },
});