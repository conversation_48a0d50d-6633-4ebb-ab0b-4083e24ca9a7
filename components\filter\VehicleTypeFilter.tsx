import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { COLORS, SPACING, FONTS, BORDERS, SHADOWS } from '@/constants/theme';
import { VehicleType } from '@/store/slices/vehicleSlice';
import { Ionicons } from '@expo/vector-icons';

interface VehicleTypeFilterProps {
  selectedTypes: string[];
  onChange: (types: string[]) => void;
}

const VehicleTypeFilter: React.FC<VehicleTypeFilterProps> = ({ selectedTypes, onChange }) => {
  const vehicleTypes = [
    { id: 'car', label: 'Car', icon: 'car-sport' },
    { id: 'bike', label: 'Bike', icon: 'bicycle' },
    { id: 'scooter', label: 'Scooter', icon: 'bicycle' },
    { id: 'commercial', label: 'Commercial', icon: 'bus' },
  ];

  const handleTypeSelect = (type: string) => {
    // For vehicle type, we only allow one selection
    onChange([type]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Vehicle Type</Text>
      <View style={styles.typeContainer}>
        {vehicleTypes.map((type) => (
          <TouchableOpacity
            key={type.id}
            style={[
              styles.typeButton,
              selectedTypes.includes(type.id) && styles.selectedTypeButton,
            ]}
            onPress={() => handleTypeSelect(type.id)}
          >
            <Ionicons
              name={type.icon as any}
              size={24}
              color={selectedTypes.includes(type.id) ? COLORS.white : COLORS.text.primary}
            />
            <Text
              style={[
                styles.typeText,
                selectedTypes.includes(type.id) && styles.selectedTypeText,
              ]}
            >
              {type.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDERS.radius.md,
    ...SHADOWS.xs,
  },
  title: {
    fontWeight: '600',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    marginBottom: SPACING.md,
    letterSpacing: 0.3,
  },
  typeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -SPACING.xs / 2,
  },
  typeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface.secondary,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: BORDERS.radius.md,
    marginRight: SPACING.sm,
    marginBottom: SPACING.sm,
    borderWidth: 1,
    borderColor: COLORS.border.primary,
    minWidth: 90,
    justifyContent: 'center',
  },
  selectedTypeButton: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
    ...SHADOWS.xs,
  },
  typeText: {
    fontWeight: '500',
    fontSize: FONTS.size.caption,
    color: COLORS.text.primary,
    marginLeft: SPACING.xs,
  },
  selectedTypeText: {
    color: COLORS.white,
    fontWeight: '600',
  },
});

export default VehicleTypeFilter;
