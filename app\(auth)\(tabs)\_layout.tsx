import { router, Tabs } from 'expo-router';
import React from 'react';
import { Platform, TouchableOpacity } from 'react-native';
import { COLORS } from '@/constants/theme';

// Import Ionicons library for all icons
import { Ionicons } from '@expo/vector-icons';

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={({
        headerShown: false,
        tabBarActiveTintColor: "#FC6316",
        tabBarInactiveTintColor: COLORS.text.secondary,
        tabBarStyle: {
          ...Platform.select({
            ios: {
              backgroundColor: COLORS.white,
              height: 88,
              paddingBottom: 32,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: -3 },
              shadowOpacity: 0.05,
              shadowRadius: 6,
              elevation: 5,
              borderTopWidth: 0, // Remove top border
            },
            android: {
              backgroundColor: COLORS.white,
              paddingBottom: 12,
              elevation: 8, // Android shadow
              borderTopWidth: 0, // Remove top border
            },
          }),
          borderTopColor: 'transparent', // Make border transparent as fallback
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
          marginTop: 0,
        },
        tabBarIconStyle: {
          marginBottom: -2,
        },
        tabBarShowLabel: true,
        tabBarHideOnKeyboard: true,
      })}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name="home"
              size={focused ? size + 4 : size}
              color={color}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="sale"
        options={{
          title: 'Sale',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name="key-outline"
              size={focused ? size + 4 : size}
              color={color}
            />
          ),
          headerShown: true,
          headerTitle: 'Sell',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: 'bold',
          },
          headerStyle: { backgroundColor: COLORS.primary },
          headerTitleAlign: 'center',
          headerTintColor: COLORS.white,
          headerLeft(props) {
            return (
              <TouchableOpacity onPress={() => router.back()}>
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color="white"
                  style={{ marginLeft: 16 }}
                />
              </TouchableOpacity>
            );
          },
        }}
      />
      <Tabs.Screen
        name="buy"
        options={{
          title: 'Buy',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name="car-outline"
              size={focused ? size + 4 : size}
              color={color}
            />
          ),
          headerShown: true,
          headerTitle: 'Buy',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: 'bold',
          },
          headerStyle: { backgroundColor: COLORS.primary },
          headerTitleAlign: 'center',
          headerTintColor: COLORS.white,
          headerLeft(props) {
            return (
              <TouchableOpacity onPress={() => router.back()}>
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color="white"
                  style={{ marginLeft: 16 }}
                />
              </TouchableOpacity>
            );
          },
        }}
      />
      {/* <Tabs.Screen
        name="auction"
        options={{
          title: 'Auction',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name="pricetag-outline"
              size={focused ? size + 4 : size}
              color={color}
            />
          ),
          headerShown: true,
          headerTitle: 'Auction',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: 'bold',
          },
          headerStyle: { backgroundColor: COLORS.primary },
          headerTitleAlign: 'center',
          headerTintColor: COLORS.white,
          headerLeft(props) {
            return (
              <TouchableOpacity onPress={() => router.back()}>
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color="white"
                  style={{ marginLeft: 16 }}
                />
              </TouchableOpacity>
            );
          },

        }}
      /> */}
      <Tabs.Screen
        name="account"
        options={{
          title: 'Account',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name="person-outline"
              size={focused ? size + 4 : size}
              color={color}
            />
          ),
          headerShown: true,
          headerTitle: 'Account',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: 'bold',
          },
          headerStyle: { backgroundColor: COLORS.primary },
          headerTitleAlign: 'center',
          headerTintColor: COLORS.white,
          headerLeft(props) {
            return (
              <TouchableOpacity onPress={() => router.back()}>
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color="white"
                  style={{ marginLeft: 16 }}
                />
              </TouchableOpacity>
            );
          },
        }}
      />
    </Tabs>
  );
}