import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { COLORS, SPACING, FONTS, BORDERS, SHADOWS } from '@/constants/theme';
import { Ionicons } from '@expo/vector-icons';

interface TransmissionFilterProps {
  selectedTransmissions: string[];
  onChange: (transmissions: string[]) => void;
}

const TransmissionFilter: React.FC<TransmissionFilterProps> = ({ selectedTransmissions, onChange }) => {
  const transmissionTypes = [
    { id: 'Automatic', label: 'Automatic' },
    { id: 'Manual', label: 'Manual' },
    { id: 'Hybrid', label: 'Hybrid' },
  ];

  const handleTransmissionSelect = (transmission: string) => {
    if (selectedTransmissions.includes(transmission)) {
      onChange(selectedTransmissions.filter(t => t !== transmission));
    } else {
      onChange([...selectedTransmissions, transmission]);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Transmission</Text>
      <View style={styles.checkboxContainer}>
        {transmissionTypes.map((transmission) => (
          <TouchableOpacity
            key={transmission.id}
            style={styles.checkboxRow}
            onPress={() => handleTransmissionSelect(transmission.id)}
          >
            <View style={styles.checkboxWrapper}>
              <View
                style={[
                  styles.checkbox,
                  selectedTransmissions.includes(transmission.id) && styles.checkboxSelected,
                ]}
              >
                {selectedTransmissions.includes(transmission.id) && (
                  <Ionicons name="checkmark" size={16} color={COLORS.white} />
                )}
              </View>
            </View>
            <Text style={styles.checkboxLabel}>{transmission.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDERS.radius.md,
    ...SHADOWS.xs,
  },
  title: {
    fontWeight: '600',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    marginBottom: SPACING.md,
    letterSpacing: 0.3,
  },
  checkboxContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: SPACING.md,
    paddingRight: SPACING.sm,
  },
  checkboxWrapper: {
    marginRight: SPACING.sm,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: BORDERS.radius.sm,
    borderWidth: 2,
    borderColor: COLORS.border.primary,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.surface.secondary,
  },
  checkboxSelected: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
    ...SHADOWS.xs,
  },
  checkboxLabel: {
    fontWeight: '500',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
  },
});

export default TransmissionFilter;