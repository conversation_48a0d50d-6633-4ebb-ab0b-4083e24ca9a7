import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { SkeletonLoader } from '@/components/common/VehicleCardSkeleton';
import { COLORS, SPACING } from '@/constants/theme';

interface EditVehicleFormSkeletonProps {
  vehicleType?: string;
}

export const EditVehicleFormSkeleton: React.FC<EditVehicleFormSkeletonProps> = ({
  vehicleType = 'car'
}) => {
  return (
    <ScrollView style={styles.scrollContainer}>
      {/* Title Skeleton */}
      <View style={styles.titleContainer}>
        <SkeletonLoader style={styles.titleSkeleton}>
          <View style={styles.skeletonBackground} />
        </SkeletonLoader>
      </View>

      <View style={styles.form}>
        {/* Brand/Type Dropdown Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <SkeletonLoader style={styles.dropdownSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
        </View>

        {/* Model Dropdown Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <SkeletonLoader style={styles.dropdownSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
        </View>

        {/* Variant Dropdown Skeleton (only for cars) */}
        {vehicleType === 'car' && (
          <View style={styles.fieldContainer}>
            <SkeletonLoader style={styles.labelSkeleton}>
              <View style={styles.skeletonBackground} />
            </SkeletonLoader>
            <SkeletonLoader style={styles.dropdownSkeleton}>
              <View style={styles.skeletonBackground} />
            </SkeletonLoader>
          </View>
        )}

        {/* Year Input Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <SkeletonLoader style={styles.inputSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
        </View>

        {/* Fuel Type Chips Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <View style={styles.chipContainer}>
            {[1, 2, 3, 4].map((index) => (
              <SkeletonLoader key={index} style={styles.chipSkeleton}>
                <View style={styles.skeletonBackground} />
              </SkeletonLoader>
            ))}
          </View>
        </View>

        {/* Transmission Chips Skeleton (only for car and commercial) */}
        {['car', 'commercial'].includes(vehicleType || 'car') && (
          <View style={styles.fieldContainer}>
            <SkeletonLoader style={styles.labelSkeleton}>
              <View style={styles.skeletonBackground} />
            </SkeletonLoader>
            <View style={styles.chipContainer}>
              {[1, 2].map((index) => (
                <SkeletonLoader key={index} style={styles.chipSkeleton}>
                  <View style={styles.skeletonBackground} />
                </SkeletonLoader>
              ))}
            </View>
          </View>
        )}

        {/* KM Driven Input Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <SkeletonLoader style={styles.inputSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
        </View>

        {/* Owners Chips Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <View style={styles.chipContainer}>
            {[1, 2, 3, 4, 5].map((index) => (
              <SkeletonLoader key={index} style={styles.chipSkeleton}>
                <View style={styles.skeletonBackground} />
              </SkeletonLoader>
            ))}
          </View>
        </View>

        {/* Insurance Chips Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <View style={styles.chipContainer}>
            {[1, 2, 3, 4].map((index) => (
              <SkeletonLoader key={index} style={styles.chipSkeleton}>
                <View style={styles.skeletonBackground} />
              </SkeletonLoader>
            ))}
          </View>
        </View>

        {/* State Dropdown Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <SkeletonLoader style={styles.dropdownSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
        </View>

        {/* City Dropdown Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <SkeletonLoader style={styles.dropdownSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
        </View>

        {/* Title Input Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <SkeletonLoader style={styles.inputSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
        </View>

        {/* Description TextArea Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <SkeletonLoader style={styles.textAreaSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
        </View>

        {/* Photos Grid Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <View style={styles.photoGrid}>
            {[1, 2, 3, 4].map((index) => (
              <SkeletonLoader key={index} style={styles.photoSkeleton}>
                <View style={styles.skeletonBackground} />
              </SkeletonLoader>
            ))}
          </View>
        </View>

        {/* Price Input Skeleton */}
        <View style={styles.fieldContainer}>
          <SkeletonLoader style={styles.labelSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
          <SkeletonLoader style={styles.inputSkeleton}>
            <View style={styles.skeletonBackground} />
          </SkeletonLoader>
        </View>

        {/* Submit Button Skeleton */}
        <SkeletonLoader style={styles.submitButtonSkeleton}>
          <View style={styles.skeletonBackground} />
        </SkeletonLoader>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
  },
  titleContainer: {
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  titleSkeleton: {
    height: 28,
    width: '60%',
    borderRadius: 4,
  },
  form: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  fieldContainer: {
    marginBottom: 16,
  },
  skeletonBackground: {
    flex: 1,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
  },
  labelSkeleton: {
    height: 16,
    width: '30%',
    borderRadius: 4,
    marginBottom: 8,
  },
  dropdownSkeleton: {
    height: 50,
    borderRadius: 8,
  },
  inputSkeleton: {
    height: 50,
    borderRadius: 8,
  },
  textAreaSkeleton: {
    height: 100,
    borderRadius: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chipSkeleton: {
    height: 36,
    width: 80,
    borderRadius: 18,
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 8,
  },
  photoSkeleton: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  submitButtonSkeleton: {
    height: 50,
    borderRadius: 8,
    marginTop: 16,
  },
});
