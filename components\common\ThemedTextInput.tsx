import React from 'react';
import { TextInput, StyleSheet, View, TextInputProps, ViewStyle, TextStyle } from 'react-native';
import { COLORS, SPACING } from '@/constants/theme';
import { ThemedText } from './ThemedText';

interface ThemedTextInputProps extends TextInputProps {
    error?: string | boolean;
}

export default function ThemedTextInput({ error, style, ...props }: ThemedTextInputProps) {
    return (
        <View style={styles.container as ViewStyle}>
            <TextInput
                style={[
                    styles.input as TextStyle,
                    error && (styles.inputError as TextStyle),
                    style
                ]}
                placeholderTextColor={COLORS.text.secondary}
                {...props}
            />
            {error && typeof error === 'string' && (
                <ThemedText style={styles.errorText as TextStyle}>{error}</ThemedText>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        width: '100%',
    },
    input: {
        width: '100%',
        height: 48,
        backgroundColor: COLORS.white,
        borderRadius: 12,
        paddingHorizontal: SPACING.md,
        fontSize: 16,
        color: COLORS.text.primary,
        borderWidth: 1,
        borderColor: COLORS.border.primary,
    },
    inputError: {
        borderColor: COLORS.error,
    },
    errorText: {
        color: COLORS.error,
        fontSize: 12,
        marginTop: 4,
        marginLeft: 4,
    },
}); 