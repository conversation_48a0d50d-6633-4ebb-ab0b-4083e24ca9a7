import * as WebBrowser from 'expo-web-browser';
import * as Google from 'expo-auth-session/providers/google';
import { useEffect, useState } from 'react';
import { auth } from '@/config/firebase';
import { GoogleAuthProvider, signInWithCredential, User } from 'firebase/auth';
import { useAppDispatch } from '@/store/hooks';
import { Platform } from 'react-native';
import { googleSignIn } from '@/store/slices/authSlice';

// Register your web client ID in the Firebase console
// For Android: Add your SHA certificate fingerprints
// For iOS: Add your bundle identifier
const WEB_CLIENT_ID = process.env.EXPO_PUBLIC_WEB_CLIENT_ID; // Replace with your Web client ID
const ANDROID_CLIENT_ID = process.env.EXPO_PUBLIC_ANDROID_CLIENT_ID; // Replace with your Android client ID
const IOS_CLIENT_ID = process.env.EXPO_PUBLIC_IOS_CLIENT_ID; // Replace with your iOS client ID

// Ensure the browser redirects properly
WebBrowser.maybeCompleteAuthSession();

export function useGoogleAuth() {
    const [userInfo, setUserInfo] = useState<User | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const dispatch = useAppDispatch();

    const [request, response, promptAsync] = Google.useAuthRequest({
        clientId: WEB_CLIENT_ID,
        androidClientId: ANDROID_CLIENT_ID,
        iosClientId: IOS_CLIENT_ID,
    });

    useEffect(() => {
        if (response?.type === 'success') {
            handleSignInWithGoogle(response.authentication);
        } else if (response?.type === 'error') {
            setError(response.error?.message || 'Google authentication failed');
        }
    }, [response]);

    const handleSignInWithGoogle = async (authentication: any) => {
        if (!authentication?.idToken) {
            setError('No ID token present in the response');
            return;
        }

        try {
            setLoading(true);
            setError(null);

            // Create a Google credential with the token
            const credential = GoogleAuthProvider.credential(
                authentication.idToken,
                authentication.accessToken
            );

            // Sign in with the credential
            const userCredential = await signInWithCredential(auth, credential);

            // Get the user information
            const user = userCredential.user;

            // Set the user info
            setUserInfo(user);

            // Dispatch the googleSignIn action to update Redux store
            await dispatch(googleSignIn(user)).unwrap();

            return user;
        } catch (error: any) {
            setError(error.message || 'Failed to sign in with Google');
            console.error('Google sign-in error:', error);
            return null;
        } finally {
            setLoading(false);
        }
    };

    return {
        userInfo,
        loading,
        error,
        request,
        promptAsync,
    };
} 