{"project_info": {"project_number": "218472441831", "firebase_url": "https://ndcar-1262b-default-rtdb.firebaseio.com", "project_id": "ndcar-1262b", "storage_bucket": "ndcar-1262b.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:218472441831:android:92b4885b2c45e2bfaf7f42", "android_client_info": {"package_name": "com.factcoding.secondcar"}}, "oauth_client": [{"client_id": "218472441831-7dlrp1b3k64gc45d775m7ntsvufb3cr2.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.factcoding.secondcar", "certificate_hash": "7eb5e89eb1791950b497874f8204fab27a2fb49f"}}, {"client_id": "218472441831-3a12b39vt1babebffpprr61aq8kk3khb.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBwcGZC-24JwOCAyi9i9xx5X05WJ5YVqYo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "218472441831-3a12b39vt1babebffpprr61aq8kk3khb.apps.googleusercontent.com", "client_type": 3}, {"client_id": "218472441831-hjqqsgrl0tnheaekqa5oknd9r4lt4bt5.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.factcoding.secondcar"}}]}}}], "configuration_version": "1"}