import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/common/ThemedText';
import ThemedButton from '@/components/common/ThemedButton';
import OTPInput from '@/components/auth/OTPInput';
import { COLORS } from '@/constants/theme';

export default function VerifyOtpForm({ email, otp, setOtp, onVerifySuccess, loading }: { email: string, otp: string, setOtp: (otp: string) => void, onVerifySuccess: () => void, loading: boolean }) {
    return (
        <>
            <ThemedText style={styles.title}>Verify OTP</ThemedText>
            <ThemedText style={styles.subtitle}>Enter the 6-digit code sent to your email</ThemedText>

            <OTPInput
                otp={otp}
                setOtp={setOtp}
            />

            <ThemedButton
                style={styles.button}
                onPress={onVerifySuccess}
                disabled={otp.length !== 6 || loading}
            >
                {loading ? 'Verifying...' : 'Verify OTP'}
            </ThemedButton>
        </>
    );
}

const styles = StyleSheet.create({
    title: {
        fontSize: 24,
        marginBottom: 8,
        textAlign: 'center',
        color: COLORS.text.primary,
    },
    subtitle: {
        fontSize: 16,
        marginBottom: 24,
        textAlign: 'center',
        color: COLORS.text.secondary,
    },
    button: {
        width: '100%',
        marginTop: 8,
    },
});