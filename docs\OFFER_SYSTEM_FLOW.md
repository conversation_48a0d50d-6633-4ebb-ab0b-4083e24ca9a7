# Offer System Data Flow

## Overview
The offer system has been updated to handle existing offers from the vehicle detail API and synchronize new offers across the application state.

## Data Flow

### 1. Initial Load (Vehicle Detail Page)
```
Vehicle Detail API (/api/display/vehicals/detail?id=8)
    ↓
Returns vehicle data with make_offer array
    ↓
vehicleDetailSlice stores both vehicle data and offers
    ↓
OffersList component displays existing offers
```

### 2. Making a New Offer
```
User fills MakeOfferModal
    ↓
Validates offer (minimum 20% of vehicle price)
    ↓
Calls Make Offer API (/api/make/offer)
    ↓
API returns updated offers list
    ↓
offerSlice updates with new offers
    ↓
Vehicle detail page re-fetches vehicle data to sync
    ↓
Both slices now have consistent offer data
```

## State Management

### vehicleDetailSlice
- **Primary source** for displaying offers
- Stores `offers` array extracted from vehicle API response
- Updates when vehicle detail is fetched
- Has `updateOffers` action for manual synchronization

### offerSlice
- **Secondary state** for offer submission
- Handles loading/error states for making offers
- Updates when new offer is successfully submitted
- Has `setOffers` action for synchronization

## API Endpoints

### Vehicle Detail API
- **Endpoint**: `GET /api/display/vehicals/detail?id={id}&type={type}`
- **Response**: Vehicle data with `make_offer` array
- **Usage**: Primary source for existing offers

### Make Offer API
- **Endpoint**: `POST /api/make/offer`
- **Request**: `{ "id": number, "type": string, "make_offer": number }`
- **Response**: `{ "status": boolean, "message": string, "data": Offer[] }`
- **Usage**: Submit new offers and get updated offers list

## Components

### OffersList
- Displays offers from `vehicleDetailSlice.offers`
- Shows empty state when no offers exist
- Handles profile pictures with fallback to default avatar

### MakeOfferModal
- Form for submitting new offers
- Validates minimum offer amount (20% of vehicle price)
- Shows success/error messages via Alert
- Automatically closes on successful submission

### Vehicle Detail Page
- Integrates both components
- Manages modal visibility
- Handles offer synchronization between slices
- Re-fetches vehicle data after successful offer submission

## Error Handling
- Network errors are caught and displayed via Alert
- Invalid offer amounts are validated before submission
- Authentication errors are handled gracefully
- Loading states prevent multiple submissions

## Future Improvements
- Real-time offer updates via WebSocket
- Offer history and status tracking
- Push notifications for new offers
- Offer acceptance/rejection workflow
