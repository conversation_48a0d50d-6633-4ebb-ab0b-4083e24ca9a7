import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

interface Ad {
    id: number;
    title: string;
    compnay_name: string;
    link: string;
    image: string;
    ad_price: string;
    status: string;
    expires_at: string;
}

interface AdState {
    ads: Ad[];
    loading: boolean;
    error: string | null;
}

const initialState: AdState = {
    ads: [],
    loading: false,
    error: null,
};

export const fetchAds = createAsyncThunk<
    Ad[],
    void,
    { rejectValue: string }
>(
    'ads/fetchAds',
    async (_, { rejectWithValue }) => {
        try {
            const response = await fetch(`${API_BASE_URL}/display/ads`);

            // Check if response is ok
            if (!response.ok) {
                throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
            }

            // Check content type to ensure it's JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error(`Expected JSON response but got ${contentType || 'unknown content type'}`);
            }

            // Parse JSON with error handling
            let data;
            try {
                data = await response.json();
            } catch (parseError) {
                console.error('JSON Parse error:', parseError);
                throw new Error('Failed to parse server response as JSON');
            }

            if (!data.status) {
                throw new Error(data.message || 'Failed to fetch ads');
            }

            return data.data || [];
        } catch (error) {
            console.error('Fetch ads error:', error);
            return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch ads');
        }
    }
);

const adSlice = createSlice({
    name: 'ads',
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchAds.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchAds.fulfilled, (state, action) => {
                state.loading = false;
                state.ads = action.payload;
            })
            .addCase(fetchAds.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || 'Failed to fetch ads';
            });
    },
});

export default adSlice.reducer; 