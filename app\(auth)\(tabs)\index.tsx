import { SectionHeader } from '@/components/common/SectionHeader';
import { ThemedText } from '@/components/common/ThemedText';
import { HomeHeader } from '@/components/home/<USER>';
import { SearchBar } from '@/components/home/<USER>';
import { VehicleCard } from '@/components/home/<USER>';
import { VehicleTypeSelector } from '@/components/home/<USER>';
import { COLORS, SPACING } from '@/constants/theme';
import { fetchVehicles, setVehicleType } from '@/store/slices/vehicleSlice';
import { AppDispatch, RootState } from '@/store/store';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Dimensions, Image, Platform, SafeAreaView, ScrollView, StatusBar, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { fetchBrands } from '@/store/slices/brandSlice';
import { fetchAds } from '@/store/slices/adSlice';
import LoadingIndicator from '@/components/common/LoadingIndicator';
import { router, useFocusEffect } from 'expo-router';

type VehicleType = 'car' | 'bike' | 'scooter' | 'commercial';

const STATUSBAR_HEIGHT = StatusBar.currentHeight || 0;

export default function HomeScreen() {
  const [selectedVehicleType, setSelectedVehicleType] = useState<VehicleType>('car');
  const [activeSlide, setActiveSlide] = useState(0);
  const [search, setSearch] = useState('');
  const scrollViewRef = useRef<ScrollView>(null);
  const screenWidth = Dimensions.get('window').width;

  const dispatch = useDispatch<AppDispatch>();
  const { vehicles, loading, error } = useSelector((state: RootState) => state.vehicles);
  const { brands, loading: brandsLoading } = useSelector((state: RootState) => state.brands);
  const { ads: carouselData, loading: adsLoading } = useSelector((state: RootState) => state.ads);

  useEffect(() => {
    dispatch(fetchVehicles({ type: selectedVehicleType as VehicleType }));
    dispatch(fetchBrands({ type: selectedVehicleType }));
    dispatch(fetchAds());
  }, [selectedVehicleType, dispatch]);

  useFocusEffect(
    useCallback(() => {
      dispatch(fetchVehicles({ type: selectedVehicleType as VehicleType }));
      return () => {
        // Optional cleanup if needed
      };
    }, [selectedVehicleType, dispatch])
  );

  const handleVehicleTypeSelect = (type: VehicleType) => {
    dispatch(setVehicleType(type))
    setSelectedVehicleType(type);
  };

  // Get the appropriate placeholder text based on the selected vehicle type
  const getSearchPlaceholder = () => {
    switch (selectedVehicleType) {
      case 'car':
        return 'Search cars...';
      case 'bike':
      case 'scooter':
        return 'Search two wheelers...';
      case 'commercial':
        return 'Search commercial vehicles...';
      default:
        return 'Search vehicles...';
    }
  };

  // Navigate to the buy page when search bar is clicked
  const handleSearchPress = () => {
    router.push({
      pathname: '/buy',
      params: {
        type: selectedVehicleType
      }
    });
  };

  // Navigate to the sale page when sell button is clicked
  const handleSellPress = () => {
    router.push('/sale');
  };

  // Navigate to the buy page when buy button is clicked
  const handleBuyPress = () => {
    router.push('/buy');
  };

  // Handle brand icon click
  const handleBrandPress = (brandName: string) => {
    // Force a new navigation instance by adding a timestamp to ensure parameters are refreshed
    router.push({
      pathname: '/buy',
      params: {
        search: brandName,
        type: selectedVehicleType,
        _t: Date.now() // Add timestamp to force new navigation
      }
    });
  };

  const renderVehicleCards = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ThemedText style={{ color: COLORS.black }}>Loading...</ThemedText>
        </View>
      )
    }

    if (error) {
      return (
        <View style={styles.errorContainer}>
          <ThemedText style={styles.errorText}>{error}</ThemedText>
        </View>
      );
    }

    if (vehicles.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <ThemedText style={{ color: COLORS.text.primary }}>No vehicles found for this category</ThemedText>
        </View>
      );
    }

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.recommendationsScroll}
      >
        {vehicles.map((vehicle) => (
          <VehicleCard
            key={vehicle.id}
            vehicle={{
              id: vehicle.id.toString(),
              price: `₹ ${parseInt(vehicle.price).toLocaleString('en-IN')}`,
              family: vehicle.brand,
              name: vehicle.title,
              year: vehicle.year,
              kilometers: `${vehicle.kilometers_driven}k km`,
              fuelType: vehicle.fuel_type,
              transmission: vehicle.transmission || 'N/A',
              location: vehicle.location,
              image: { uri: vehicle.primary_image },
              variant: vehicle.variant,
              type: selectedVehicleType,
            }}
          />
        ))}
      </ScrollView>
    );
  };

  const renderCarouselItems = () => {
    if (adsLoading) {
      return (
        <View style={[styles.carouselItemWrapper]}>
          <ThemedText style={{ color: COLORS.black, textAlign: 'center' }}>Loading ads...</ThemedText>
        </View>
      );
    }

    return carouselData.map((ad) => (
      <View key={ad.id} style={styles.carouselItemWrapper}>
        <Image
          source={{ uri: ad.image }}
          style={styles.carouselImage}
          resizeMode="cover"
        />
      </View>
    ));
  };


  const budgetRanges = [
    'Up to 1 Lac',
    '1 Lac - 2 Lac',
    '2 Lac - 3 Lac',
    '3 Lac - 4 Lac',
    '4 Lac - 5 Lac',
    '5 Lac and above'
  ];

  const yearRanges = [
    'Up to 1 Years',
    'Up to 3 years',
    'Up to 5 years',
    'Up to 7 years',
    'Up to 9 years',
    'Above 9 years'
  ];


  useEffect(() => {
    const interval = setInterval(() => {
      const nextSlide = (activeSlide + 1) % carouselData.length;
      setActiveSlide(nextSlide);
      scrollViewRef.current?.scrollTo({
        x: nextSlide * screenWidth,
        animated: true,
      });
    }, 3000);

    return () => clearInterval(interval);
  }, [activeSlide]);

  const handleScroll = (event: any) => {
    const slideSize = event.nativeEvent.layoutMeasurement.width;
    const offset = event.nativeEvent.contentOffset.x;
    const activeSlide = Math.floor(offset / slideSize);
    setActiveSlide(activeSlide);
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={COLORS.background}
        translucent
      />
      <ScrollView style={styles.container}>
        <HomeHeader />
        <VehicleTypeSelector
          selectedType={selectedVehicleType}
          onTypeSelect={handleVehicleTypeSelect}
        />
        <SearchBar
          value={search}
          onChangeText={setSearch}
          placeholder={getSearchPlaceholder()}
          isNavigationBar={true}
          onNavigationPress={handleSearchPress}
        />

        {/* Featured Carousel Section */}
        <View style={styles.carouselContainer}>
          <View style={styles.carouselWrapper}>
            <ScrollView
              ref={scrollViewRef}
              horizontal
              pagingEnabled
              showsHorizontalScrollIndicator={false}
              onScroll={handleScroll}
              scrollEventThrottle={16}
            >
              {renderCarouselItems()}
            </ScrollView>
            <View style={styles.pagination}>
              {carouselData.map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.paginationDot,
                    index === activeSlide && styles.paginationDotActive,
                  ]}
                />
              ))}
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.saleButton} onPress={handleSellPress}>
            <ThemedText style={styles.buttonText}>Sell</ThemedText>
          </TouchableOpacity>
          <TouchableOpacity style={styles.buyButton} onPress={handleBuyPress}>
            <ThemedText style={styles.buyButtonText}>Buy</ThemedText>
          </TouchableOpacity>
        </View>

        {/* Recommendations Section */}
        <View style={[styles.section, styles.lastSection]}>
          <SectionHeader
            title={"Recommendations"}
            onViewAllPress={() => { }}
          />
          {renderVehicleCards()}
        </View>

        {/* Explore by Brand Section */}
        <View style={styles.section}>
          <SectionHeader title="Explore by Brand" onViewAllPress={() => { }} />
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.brandsScroll}
          >
            {brandsLoading ? (
              <ThemedText>Loading brands...</ThemedText>
            ) : (
              brands.map((brand) => (
                <TouchableOpacity
                  key={brand.id}
                  style={styles.brandItem}
                  onPress={() => handleBrandPress(brand.name)}
                >
                  <Image
                    source={{ uri: brand.image_url }}
                    style={styles.brandLogo}
                    resizeMode="contain"
                  />
                </TouchableOpacity>
              ))
            )}
          </ScrollView>
        </View>

        {/* Tell your Budget Section */}
        <View style={styles.section}>
          <SectionHeader title="Tell your Budget" onViewAllPress={() => { }} />
          <View style={styles.budgetGrid}>
            {budgetRanges.map((budget) => (
              <TouchableOpacity key={budget} style={styles.budgetItem} onPress={() => router.push('/buy')}>
                <ThemedText style={styles.budgetText}>{budget}</ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Select Year Section */}
        <View style={styles.section}>
          <SectionHeader title="Select Year" onViewAllPress={() => { }} />
          <View style={styles.yearGrid}>
            {yearRanges.map((year) => (
              <TouchableOpacity key={year} style={styles.yearItem} onPress={() => router.push('/buy')}>
                <ThemedText style={styles.yearText}>{year}</ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: COLORS.background,
    paddingTop: Platform.OS === 'android' ? STATUSBAR_HEIGHT : 0,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  carouselContainer: {
    marginBottom: SPACING.md,
    paddingHorizontal: SPACING.md,
  },
  carouselWrapper: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  carouselItemWrapper: {
    width: Dimensions.get('window').width - (SPACING.md * 2),
  },
  carouselImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
  },
  pagination: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 10,
    alignSelf: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: COLORS.white,
  },
  actionButtons: {
    flexDirection: 'row',
    marginHorizontal: SPACING.md,
    marginBottom: SPACING.lg,
    gap: SPACING.md,
  },
  saleButton: {
    flex: 1,
    backgroundColor: COLORS.primary,
    padding: SPACING.md,
    borderRadius: 8,
    alignItems: 'center',
  },
  buyButton: {
    flex: 1,
    backgroundColor: COLORS.secondary,
    padding: SPACING.md,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: '600',
  },
  buyButtonText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    marginTop: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  lastSection: {
    marginBottom: SPACING.xl,
  },
  brandsScroll: {
    paddingLeft: SPACING.md,
    paddingVertical: SPACING.xs,
  },
  brandItem: {
    width: 80,
    height: 80,
    backgroundColor: COLORS.white,
    borderRadius: 45,
    marginRight: SPACING.md,
    marginVertical: SPACING.xs,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    padding: SPACING.xs,
  },
  brandLogo: {
    width: 45,
    height: 45,
  },
  budgetGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: SPACING.md,
    gap: SPACING.sm,
  },
  budgetItem: {
    width: '48%',
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  budgetText: {
    fontSize: 14,
    color: COLORS.text.primary,
  },
  yearGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: SPACING.md,
    gap: SPACING.sm,
  },
  yearItem: {
    width: '48%',
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  yearText: {
    fontSize: 14,
    color: COLORS.text.primary,
  },
  recommendationsScroll: {
    paddingLeft: SPACING.md,
  },
  loadingContainer: {
    padding: SPACING.md,
    alignItems: 'center',
  },
  errorContainer: {
    padding: SPACING.md,
    alignItems: 'center',
  },
  errorText: {
    color: COLORS.error,
  },
  emptyContainer: {
    padding: SPACING.md,
    alignItems: 'center',
  },
});
