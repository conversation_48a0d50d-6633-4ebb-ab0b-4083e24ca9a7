// VehicleCard.tsx

import React from 'react';
import {
    View,
    Image,
    Pressable,
    StyleSheet,
    StyleProp,
    ViewStyle,
    TextStyle
} from 'react-native';
import { Link, router } from 'expo-router';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, FONTS, SPACING } from '@/constants/theme';
import LocationPin from '@/assets/icon/location-pin-svg.svg';

interface VehicleCardProps {
    vehicle: {
        id: string;
        price: string;
        family: string;
        name: string;
        year: string;
        kilometers: string;
        fuelType: string;
        transmission: string;
        location: string;
        image: { uri: string } | number;
        variant: string;
        type?: string;
    };
    cardStyle?: StyleProp<ViewStyle>; // <-- Accept optional style overrides
}

export const VehicleCard: React.FC<VehicleCardProps> = ({
    vehicle,
    cardStyle
}) => {
    const handlePress = () => {
        router.push({
            pathname: '/vehicle/[id]',
            params: { id: vehicle.id, type: vehicle.type || 'car' }
        });
    };

    return (
        // <Link
        //     href={{
        //         pathname: '/vehicle/[id]',
        //         params: { id: vehicle.id, type: vehicle.type || 'car' }
        //     }}
        //     asChild
        // >
        <Pressable
            style={({ pressed }) => [
                styles.carCard,
                cardStyle,
                pressed && styles.pressedState
            ]}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel={`${vehicle.name} - ${vehicle.price}`}
            onPress={handlePress}
        >
            {/* Rest of the component remains unchanged */}
            <Image
                source={
                    typeof vehicle.image === 'number'
                        ? vehicle.image
                        : { uri: vehicle.image.uri }
                }
                style={styles.carImage}
                resizeMode="cover"
            />

            <View style={styles.carInfo}>
                <View style={styles.priceRow}>
                    <ThemedText style={styles.carPrice}>{vehicle.price}</ThemedText>
                </View>

                <ThemedText
                    style={styles.carName}
                    numberOfLines={2}
                    ellipsizeMode="tail"
                >
                    {vehicle.name}
                </ThemedText>

                <ThemedText style={styles.carFamily}>{vehicle.variant}</ThemedText>

                <View style={styles.carTags}>
                    <View style={styles.tag}>
                        <ThemedText style={styles.tagText}>{vehicle.year}</ThemedText>
                    </View>
                    <View style={styles.tag}>
                        <ThemedText style={styles.tagText}>{vehicle.kilometers}</ThemedText>
                    </View>
                    <View style={styles.tag}>
                        <ThemedText style={styles.tagText}>{vehicle.fuelType}</ThemedText>
                    </View>
                    <View style={styles.tag}>
                        <ThemedText style={styles.tagText}>
                            {vehicle.transmission}
                        </ThemedText>
                    </View>
                </View>

                <View style={styles.locationRow}>
                    <LocationPin width={14} height={14} fill={COLORS.text.secondary} />
                    <ThemedText style={styles.locationText} numberOfLines={1}>
                        {vehicle.location}
                    </ThemedText>
                </View>
            </View>
        </Pressable>
        // </Link>
    );
};

const styles = StyleSheet.create({
    carCard: {
        width: 280, // default width
        backgroundColor: COLORS.white,
        borderRadius: 12,
        marginRight: SPACING.md,
        marginBottom: SPACING.sm,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
        overflow: 'hidden'
    },
    carImage: {
        width: '100%',
        height: 180,
        borderTopLeftRadius: 12,
        borderTopRightRadius: 12
    },
    carInfo: {
        padding: SPACING.sm,
        paddingBottom: SPACING.md
    },
    priceRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    carPrice: {
        fontSize: 18,
        ...FONTS.bold,
        color: COLORS.text.primary
    } as TextStyle,
    carName: {
        fontSize: 16,
        ...FONTS.bold,
        marginVertical: SPACING.xs,
        color: COLORS.text.primary
    } as TextStyle,
    carFamily: {
        fontSize: 14,
        ...FONTS.regular,
        color: COLORS.text.secondary
    } as TextStyle,
    carTags: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: SPACING.xs,
        marginVertical: SPACING.xs,
        marginBottom: SPACING.sm
    },
    tag: {
        backgroundColor: COLORS.background,
        paddingHorizontal: SPACING.xs,
        paddingVertical: 4,
        borderRadius: 4,
        borderWidth: 1,
        borderColor: COLORS.text.secondary
    },
    tagText: {
        fontSize: 12,
        ...FONTS.regular,
        color: '#fc6316'
    } as TextStyle,
    locationRow: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    locationText: {
        fontSize: 14,
        ...FONTS.regular,
        color: COLORS.text.secondary
    } as TextStyle,
    pressedState: {
        opacity: 0.7
    }
});
