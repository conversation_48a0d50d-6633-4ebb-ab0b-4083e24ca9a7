# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# Security sensitive files
# @2ndcar2025__second-car.jks
# google-services.json
# .env
# .env.*


# Expo generated
# expo-env.d.ts
# declarations.d.ts

# IDE specific
.vscode/
.idea/

app-example

# ndcar-1262b-5fd0f6ba457d.json