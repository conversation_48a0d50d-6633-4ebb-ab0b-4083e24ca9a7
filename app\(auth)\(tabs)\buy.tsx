// BuyScreen.tsx

import { ThemedText } from '@/components/common/ThemedText';
import { SearchBar } from '@/components/home/<USER>';
import { VehicleCard } from '@/components/home/<USER>';
import { COLORS, SPACING, SHADOWS, BORDERS, FONTS } from '@/constants/theme';
import { fetchVehicles, VehicleType, clearVehicles } from '@/store/slices/vehicleSlice';
import { resetFilters } from '@/store/slices/filterSlice';
import { AppDispatch, RootState } from '@/store/store';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import React, { useEffect, useState, useRef, useCallback } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View, Keyboard, TextInput, ActivityIndicator } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import FilterModal from '@/components/filter/FilterModal';

export default function BuyScreen() {
    const {
        search: initialSearch,
        budget: initialBudget,
        year: initialYear,
        type: initialType,
        _t
    } = useLocalSearchParams<{
        search: string;
        budget: string;
        year: string;
        type: string;
        _t: string
    }>();
    const [search, setSearch] = useState(initialSearch || '');
    const [debouncedSearch, setDebouncedSearch] = useState(initialSearch || '');
    const [filterModalVisible, setFilterModalVisible] = useState(false);

    // Get vehicles, loading, error, and the selected vehicle type from Redux.
    // If no type is selected, default to 'car'.
    const { vehicles, loading, error, selectedVehicleType } = useSelector((state: RootState) => state.vehicles);
    const {
        vehicles: filteredVehiclesList,
        loading: filterLoading,
        error: filterError,
        isFilterApplied
    } = useSelector((state: RootState) => state.filter);

    const vehicleType: VehicleType = selectedVehicleType || 'car';

    const searchInputRef = useRef<TextInput>(null);
    const lastParamsRef = useRef({
        search: initialSearch,
        budget: initialBudget,
        year: initialYear,
        type: initialType,
        _t
    });

    const dispatch = useDispatch<AppDispatch>();

    // Clear vehicles when the component unmounts
    useEffect(() => {
        return () => {
            dispatch(clearVehicles());
            dispatch(resetFilters());
        };
    }, [dispatch]);

    // Handle URL parameter changes for search, budget, and year
    useEffect(() => {
        const currentParams = {
            search: initialSearch,
            budget: initialBudget,
            year: initialYear,
            type: initialType,
            _t
        };
        const lastParams = lastParamsRef.current;

        // Check if any relevant parameters have changed
        const hasParamsChanged =
            currentParams._t !== lastParams._t ||
            currentParams.search !== lastParams.search ||
            currentParams.budget !== lastParams.budget ||
            currentParams.year !== lastParams.year ||
            currentParams.type !== lastParams.type;

        if (hasParamsChanged) {
            // Update search state if search parameter is provided
            if (initialSearch !== undefined) {
                setSearch(initialSearch);
                setDebouncedSearch(initialSearch);
            }

            // If budget or year parameters are provided, the filters should already be set in Redux
            // from the home page navigation, so we just need to ensure vehicles are fetched
            if (initialBudget || initialYear) {
                // Filters are already applied from home page, just ensure we have the right vehicle type
            } else {
                // Regular search or navigation without filters
                dispatch(fetchVehicles({
                    type: vehicleType,
                    search: initialSearch || ''
                }));
            }

            lastParamsRef.current = currentParams;
        }
    }, [dispatch, initialSearch, initialBudget, initialYear, initialType, _t, vehicleType]);

    // Focus the search input when navigating with an initial search
    useEffect(() => {
        if (initialSearch && searchInputRef.current) {
            setTimeout(() => {
                searchInputRef.current?.focus();
            }, 300);
        }
    }, [initialSearch]);

    // Refetch data when the screen comes into focus
    useFocusEffect(
        useCallback(() => {
            if (debouncedSearch || vehicleType) {
                dispatch(fetchVehicles({
                    type: vehicleType,
                    search: debouncedSearch
                }));
            }
        }, [dispatch, debouncedSearch, vehicleType])
    );

    // Clear search
    const handleClearSearch = () => {
        setSearch('');
        setDebouncedSearch('');
        router.setParams({});
        dispatch(fetchVehicles({
            type: vehicleType,
            search: ''
        }));
        searchInputRef.current?.focus();
    };

    // Handle search text change
    const handleSearchChange = (text: string) => {
        setSearch(text);
    };

    // Handle the debounced search result
    const handleDebouncedSearch = (text: string) => {
        setDebouncedSearch(text);
        dispatch(fetchVehicles({
            type: vehicleType,
            search: text
        }));
    };

    // Dismiss keyboard when search is submitted
    const handleSearchSubmit = () => {
        Keyboard.dismiss();
    };

    // Handle opening the filter modal
    const handleOpenFilterModal = () => {
        setFilterModalVisible(true);
    };

    // Handle closing the filter modal
    const handleCloseFilterModal = () => {
        setFilterModalVisible(false);
    };

    // Determine which vehicles to display based on filter state
    const displayVehicles = isFilterApplied
        ? filteredVehiclesList
        : vehicles.filter((vehicle) =>
            vehicle.title?.toLowerCase().includes(debouncedSearch.toLowerCase())
        );

    // Determine loading state based on filter state
    const isLoading = isFilterApplied ? filterLoading : loading;

    // Determine error state based on filter state
    const displayError = isFilterApplied ? filterError : error;

    // Render the vehicle cards or corresponding status messages
    const renderVehicleCards = () => {
        if (isLoading) {
            return (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={COLORS.primary} />
                    <ThemedText style={{ color: COLORS.black, marginTop: SPACING.sm }}>Loading...</ThemedText>
                </View>
            );
        }

        if (displayError) {
            return (
                <View style={styles.errorContainer}>
                    <ThemedText style={styles.errorText}>{displayError}</ThemedText>
                </View>
            );
        }

        if (displayVehicles.length === 0) {
            return (
                <View style={styles.emptyContainer}>
                    <ThemedText style={{ color: COLORS.text.primary }}>
                        No vehicles found
                    </ThemedText>
                    <ThemedText style={{ color: COLORS.text.secondary, marginTop: 8 }}>
                        Try adjusting your search criteria or filters
                    </ThemedText>
                </View>
            );
        }

        return (
            <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.cardsContainer}
                keyboardShouldPersistTaps="handled"
            >
                {displayVehicles.map((vehicle: any) => (
                    <VehicleCard
                        key={vehicle.id}
                        cardStyle={styles.fullWidthCard}
                        vehicle={{
                            id: vehicle.id.toString(),
                            price: `₹ ${parseInt(vehicle.price || '0', 10).toLocaleString('en-IN')}`,
                            family: vehicle.brand,
                            name: vehicle.title,
                            year: vehicle.year,
                            kilometers: `${vehicle.kilometers_driven}k km`,
                            fuelType: vehicle.fuel_type,
                            transmission: vehicle.transmission || 'N/A',
                            location: vehicle.location,
                            image: { uri: vehicle.primary_image },
                            variant: vehicle.variant,
                            type: vehicleType,
                        }}
                    />
                ))}
            </ScrollView>
        );
    };

    return (
        <View style={styles.container} onTouchStart={Keyboard.dismiss}>
            <View style={styles.searchContainer}>
                <TouchableOpacity style={styles.filterButton} onPress={handleOpenFilterModal}>
                    <Ionicons name="options" size={20} color={COLORS.primary} />
                </TouchableOpacity>
                <SearchBar
                    ref={searchInputRef}
                    value={search}
                    onChangeText={handleSearchChange}
                    onSearch={handleDebouncedSearch}
                    debounceTime={500}
                    placeholder={`Search ${vehicleType === 'car'
                        ? 'cars'
                        : vehicleType === 'commercial'
                            ? 'commercial vehicles'
                            : 'two wheelers'
                        }...`}
                    isNavigationBar={false}
                    returnKeyType="search"
                    autoFocus={!!initialSearch}
                />

                <View style={styles.buttonContainer}>

                    {search ? (
                        <TouchableOpacity style={styles.clearButton} onPress={handleClearSearch}>
                            <Ionicons name="close-circle" size={24} color={COLORS.text.secondary} />
                        </TouchableOpacity>
                    ) : null}

                </View>
            </View>

            {/* Filter indicator */}
            {isFilterApplied && (
                <View style={styles.filterIndicator}>
                    <ThemedText style={styles.filterIndicatorText}>
                        Filters applied
                    </ThemedText>
                    <TouchableOpacity onPress={() => dispatch(resetFilters())}>
                        <ThemedText style={styles.clearFiltersText}>Clear</ThemedText>
                    </TouchableOpacity>
                </View>
            )}

            <View style={{ flex: 1 }}>{renderVehicleCards()}</View>

            {/* Filter Modal */}
            <FilterModal
                visible={filterModalVisible}
                onClose={handleCloseFilterModal}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.white,
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: SPACING.sm,
        paddingVertical: SPACING.xs,
        backgroundColor: COLORS.white,
        borderBottomWidth: 1,
        borderBottomColor: COLORS.border.primary,
        ...SHADOWS.xs,
    },
    clearButton: {
        padding: SPACING.xs,
        marginLeft: SPACING.xs,
    },
    filterButton: {
        padding: SPACING.sm,
        marginRight: SPACING.xs,
        borderRadius: BORDERS.radius.full,
        backgroundColor: COLORS.surface.secondary,
        alignItems: 'center',
        justifyContent: 'center',
        width: 42,
        height: 42,
    },
    buttonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    cardsContainer: {
        paddingHorizontal: 16,
        paddingTop: 16,
        paddingBottom: 40,
    },
    fullWidthCard: {
        width: '100%',
        marginRight: 0,
        marginBottom: 16,
    },
    loadingContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    errorContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 16,
    },
    errorText: {
        color: 'red',
        textAlign: 'center',
    },
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 16,
    },
    filterIndicator: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: COLORS.primary,
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.sm,
        marginHorizontal: SPACING.md,
        marginVertical: SPACING.sm,
        borderRadius: BORDERS.radius.md,
        ...SHADOWS.sm,
    },
    filterIndicatorText: {
        fontSize: FONTS.size.caption,
        color: COLORS.white,
        fontWeight: '500',
    },
    clearFiltersText: {
        fontSize: FONTS.size.caption,
        color: COLORS.white,
        fontWeight: '700',
        textDecorationLine: 'underline',
    },
});