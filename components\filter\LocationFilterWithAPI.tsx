import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { COLORS, SPACING, FONTS, BORDERS } from '@/constants/theme';
import { MultiSelect } from 'react-native-element-dropdown';
import axios from 'axios';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

interface LocationFilterProps {
  selectedStates: string[];
  onStateChange: (stateIds: string[], stateNames: string[]) => void;
}

interface State {
  id: string;
  name: string;
  country_id: string;
  country_code: string;
  country_name: string;
  state_code: string;
  latitude: string;
  longitude: string;
}

const LocationFilterWithAPI: React.FC<LocationFilterProps> = ({
  selectedStates,
  onStateChange,
}) => {
  const [states, setStates] = useState<{ label: string; value: string }[]>([]);
  const [statesLoading, setStatesLoading] = useState(false);
  const [statesError, setStatesError] = useState<string | null>(null);
  const [statesFocus, setStatesFocus] = useState(false);

  // Fetch states on component mount
  useEffect(() => {
    fetchStates();
  }, []);

  const fetchStates = async () => {
    setStatesLoading(true);
    setStatesError(null);
    try {
      // For testing purposes, use mock data if API is not available
      // In production, this should be removed and only use the API
      try {
        const response = await axios.get(`${API_BASE_URL}/states/101`);
        if (response.data && response.data.data) {
          const formattedStates = response.data.data.map((state: State) => ({
            label: state.name,
            value: state.id,
          }));
          setStates(formattedStates);
          return;
        }
      } catch (apiError) {
        console.warn('API error, using mock data:', apiError);
      }

      // Fallback to mock data if API fails
      const mockStates = [
        { label: 'Delhi', value: '1' },
        { label: 'Maharashtra', value: '2' },
        { label: 'Karnataka', value: '3' },
        { label: 'Tamil Nadu', value: '4' },
        { label: 'Uttar Pradesh', value: '5' },
      ];
      setStates(mockStates);
    } catch (error) {
      console.error('Error fetching states:', error);
      setStatesError('Failed to load states. Please try again.');
    } finally {
      setStatesLoading(false);
    }
  };

  const handleStateChange = (stateIds: string[]) => {
    // Get the state names from the IDs
    const stateNames = stateIds.map(id => {
      const state = states.find(s => s.value === id);
      return state ? state.label : '';
    }).filter(Boolean);

    onStateChange(stateIds, stateNames);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Location</Text>

      {/* State Dropdown */}
      <Text style={styles.label}>Select States</Text>
      {statesLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={COLORS.primary} />
        </View>
      ) : statesError ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{statesError}</Text>
        </View>
      ) : (
        <MultiSelect
          style={[styles.dropdown, statesFocus && { borderColor: COLORS.primary }]}
          placeholderStyle={styles.placeholderStyle}
          selectedTextStyle={styles.selectedTextStyle}
          inputSearchStyle={styles.inputSearchStyle}
          iconStyle={styles.iconStyle}
          data={states}
          search
          maxHeight={300}
          labelField="label"
          valueField="value"
          placeholder="Select states"
          searchPlaceholder="Search states..."
          value={selectedStates}
          onChange={handleStateChange}
          onFocus={() => setStatesFocus(true)}
          onBlur={() => setStatesFocus(false)}
          activeColor={COLORS.surface.secondary}
          keyboardAvoiding={true}
          mode="default"
          searchField="label"
          alwaysRenderSelectedItem={true}
          showsVerticalScrollIndicator={true}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDERS.radius.md,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  title: {
    fontWeight: '600',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    marginBottom: SPACING.md,
    letterSpacing: 0.3,
  },
  label: {
    fontWeight: '500',
    fontSize: FONTS.size.caption,
    color: COLORS.text.secondary,
    marginBottom: SPACING.xs,
  },
  dropdown: {
    height: 50,
    borderColor: COLORS.gray[300],
    borderWidth: 1,
    borderRadius: BORDERS.radius.sm,
    paddingHorizontal: SPACING.sm,
  },
  placeholderStyle: {
    fontSize: FONTS.size.caption,
    color: COLORS.text.secondary,
  },
  selectedTextStyle: {
    fontSize: FONTS.size.caption,
    color: COLORS.text.primary,
  },
  loadingContainer: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: COLORS.gray[300],
    borderWidth: 1,
    borderRadius: BORDERS.radius.sm,
  },
  errorContainer: {
    padding: SPACING.sm,
    backgroundColor: '#FFEBEE', // Light red color
    borderRadius: BORDERS.radius.sm,
  },
  errorText: {
    color: '#B71C1C', // Dark red color
    fontSize: FONTS.size.small,
  },
  inputSearchStyle: {
    borderRadius: BORDERS.radius.sm,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
});

export default LocationFilterWithAPI;

