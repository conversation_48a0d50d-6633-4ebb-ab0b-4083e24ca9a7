{"expo": {"name": "2nd Car", "slug": "second-car", "version": "1.0.9", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.factcoding.secondcar", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true}}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.factcoding.secondcar", "versionCode": 13, "enableProguardInReleaseBuilds": true, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png", "config": {"firebase": {"apiKey": "${process.env.EXPO_PUBLIC_FIREBASE_API_KEY}", "authDomain": "${process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN}", "databaseURL": "${process.env.EXPO_PUBLIC_FIREBASE_DATABASE_URL}", "projectId": "${process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID}", "storageBucket": "${process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET}", "messagingSenderId": "${process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID}", "appId": "${process.env.EXPO_PUBLIC_FIREBASE_APP_ID}", "measurementId": "${process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID}"}}}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-build-properties", {"ios": {"useFrameworks": "static"}}], "expo-secure-store"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "b20f2400-ee31-4066-bb39-06fada796d21"}}, "owner": "2ndcar2025"}}