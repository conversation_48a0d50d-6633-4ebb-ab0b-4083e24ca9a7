import ThemedButton from '@/components/common/ThemedButton';
import ThemedInput from '@/components/common/ThemedInput';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS } from '@/constants/theme';
import { Formik } from 'formik';
import React from 'react';
import { StyleSheet } from 'react-native';
import * as Yup from 'yup';

const emailSchema = Yup.object().shape({
    email: Yup.string()
        .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Please enter a valid email address')
        .required('Email is required'),
});

export default function EmailForm({ onEmailSubmit, onRegister, loading }: { onEmailSubmit: (email: string) => void, onRegister: () => void, loading: boolean }) {
    return (
        <Formik
            initialValues={{ email: '' }}
            validationSchema={emailSchema}
            onSubmit={(values) => onEmailSubmit(values.email)}
        >
            {({ handleChange, handleSubmit, values, errors, touched }) => (
                <>
                    <ThemedText style={styles.title}>Welcome Back!</ThemedText>
                    <ThemedText style={styles.subtitle}>Enter your email to continue</ThemedText>



                    <ThemedInput
                        value={values.email}
                        onChangeText={handleChange('email')}
                        placeholder="Email address"
                        keyboardType="email-address"
                        autoCapitalize="none"
                    />
                    {touched.email && errors.email && (
                        <ThemedText style={styles.errorText}>{errors.email}</ThemedText>
                    )}

                    <ThemedButton
                        style={styles.button}
                        onPress={handleSubmit}
                        disabled={loading}
                    >
                        {loading ? 'Loading...' : 'Continue'}
                    </ThemedButton>
                </>
            )}
        </Formik>
    );
}

const styles = StyleSheet.create({
    title: {
        fontSize: 24,
        marginBottom: 8,
        textAlign: 'center',
        color: COLORS.text.primary,
    },
    subtitle: {
        fontSize: 16,
        marginBottom: 24,
        textAlign: 'center',
        color: COLORS.text.primary,
    },
    button: {
        width: '100%',
        marginTop: 8,
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginTop: -8,
        marginBottom: 8,
        marginLeft: 16,
        alignSelf: 'flex-start',
    },
});