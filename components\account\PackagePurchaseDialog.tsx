import React, { useState, useEffect } from 'react';
import { View, Text, Modal, TouchableOpacity, StyleSheet, TouchableWithoutFeedback, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { createRazorpayOrder } from '@/store/slices/packagesSlice';
import { AppDispatch } from '@/store/store';
// Import Razorpay directly - this is the key to making it work
import RazorpayCheckout from 'react-native-razorpay';

const PackagePurchaseDialog = ({
    visible,
    onClose,
    packageData,
    appliedDiscount = null,
    onBuyPackage,
    referralCode,
    referralError
}: {
    visible: boolean,
    onClose: () => void,
    packageData: any,
    appliedDiscount?: any,
    onBuyPackage: (data: { packageId: number, paymentMethod: string, finalAmount: number, paymentId?: string }) => void,
    referralCode: string,
    referralError: string
}) => {
    const dispatch: AppDispatch = useAppDispatch();
    const { razorpayOrder, creatingOrder, error } = useAppSelector(state => state.packages);
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('online');

    // Calculate final price after discount (if any)
    const originalPrice = packageData?.amount || 0;
    const finalPrice = appliedDiscount ? appliedDiscount.final_amount : originalPrice;
    const discountAmount = appliedDiscount ? appliedDiscount.discount_amount : 0;
    const couponCode = referralCode ? referralCode : '';

    // Reset state when dialog is closed
    useEffect(() => {
        if (!visible) {
            setSelectedPaymentMethod('online');
        }
    }, [visible]);

    /**
     * Handles the online payment process
     */
    const handlePurchase = async () => {
        try {
            // Create order payload
            const orderPayload = {
                package_id: packageData?.id,
                ...(couponCode ? { coupon_code: couponCode } : {})
            };

            console.log('Creating order with payload:', orderPayload);

            // Create the order - this will also validate if the user has an active package
            const orderResult = await dispatch(createRazorpayOrder(orderPayload)).unwrap();

            console.log('Razorpay order created:', orderResult);

            // Open Razorpay checkout with the order details
            const options = {
                description: `Purchase of ${packageData?.name || 'Package'}`,
                image: 'https://i.imgur.com/3g7nmJC.png',
                currency: 'INR',
                key: process.env.EXPO_PUBLIC_RAZORPAY_KEY_ID || '',
                amount: orderResult?.razorpay_amount,
                order_id: orderResult?.order_id,
                name: '2nd Car',
                prefill: {
                    // You can add user details here if available
                },
                theme: { color: '#F37254' },
            };

            console.log("Opening Razorpay with options:", options);

            const data = await RazorpayCheckout.open(options);

            console.log("Payment successful:", data);

            // Process successful payment
            onBuyPackage({
                packageId: packageData?.id,
                paymentMethod: 'online',
                finalAmount: orderResult.final_amount,
                paymentId: data.razorpay_payment_id
            });

            onClose();
        } catch (error: any) {
            console.log('Payment process error:', error);

            // Error is handled by the Redux state and displayed in the UI
            // We don't need to show an additional alert here as the error
            // will be displayed in the dialog via the error state from Redux
        }
    };

    return (
        <>
            <Modal
                animationType="fade"
                transparent={true}
                visible={visible}
                onRequestClose={onClose}
            >
                <TouchableWithoutFeedback onPress={onClose}>
                    <View style={styles.modalOverlay}>
                        <TouchableWithoutFeedback onPress={e => e.stopPropagation()}>
                            <View style={styles.modalContent}>
                                {/* Close button */}
                                <TouchableOpacity
                                    style={styles.closeButton}
                                    onPress={onClose}
                                >
                                    <Ionicons name="close" size={24} color="#000" />
                                </TouchableOpacity>

                                {/* Package title */}
                                <Text style={styles.packageTitle}>
                                    {packageData?.name || "Package"}
                                </Text>

                                {/* Price display */}
                                <View style={styles.priceContainer}>
                                    {appliedDiscount ? (
                                        <>
                                            <Text style={styles.originalPrice}>₹ {originalPrice} /-</Text>
                                            <Text style={styles.price}>₹ {finalPrice} /-</Text>
                                        </>
                                    ) : (
                                        <Text style={styles.price}>₹ {originalPrice} /-</Text>
                                    )}
                                </View>

                                {/* Discount information (shown only when discount is applied) */}
                                {appliedDiscount && (
                                    <View style={styles.discountInfoContainer}>
                                        <View style={styles.discountRow}>
                                            <Text style={styles.discountLabel}>Coupon Applied</Text>
                                            <Text style={styles.discountValue}>{couponCode}</Text>
                                        </View>
                                        <View style={styles.discountRow}>
                                            <Text style={styles.discountLabel}>Discount Amount</Text>
                                            <Text style={styles.discountValue}>₹ {discountAmount} /-</Text>
                                        </View>
                                    </View>
                                )}

                                {/* Payment method info */}
                                <Text style={styles.sectionTitle}>Payment Method</Text>
                                <View style={styles.paymentInfoContainer}>
                                    <Text style={styles.paymentInfoText}>Secure Online Payment via Razorpay</Text>
                                </View>

                                {/* Error message */}
                                {error && (
                                    <View style={styles.errorContainer}>
                                        <Ionicons name="alert-circle" size={20} color="#FF0000" style={styles.errorIcon} />
                                        <Text style={styles.errorMessage}>{error}</Text>
                                    </View>
                                )}

                                {/* Buy button */}
                                <TouchableOpacity
                                    style={[
                                        styles.buyButton,
                                        creatingOrder && styles.disabledButton
                                    ]}
                                    onPress={handlePurchase}
                                    disabled={creatingOrder}
                                >
                                    {creatingOrder ? (
                                        <ActivityIndicator color="#fff" size="small" />
                                    ) : (
                                        <Text style={styles.buyButtonText}>Buy Package</Text>
                                    )}
                                </TouchableOpacity>
                            </View>
                        </TouchableWithoutFeedback>
                    </View>
                </TouchableWithoutFeedback>
            </Modal>
        </>
    );
};

const styles = StyleSheet.create({
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: '85%',
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        alignItems: 'center',
        position: 'relative',
    },
    closeButton: {
        position: 'absolute',
        top: 10,
        right: 10,
        zIndex: 1,
    },
    packageTitle: {
        fontSize: 20,
        fontWeight: '600',
        marginBottom: 24,
        marginTop: 10,
    },
    priceContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 24,
    },
    originalPrice: {
        fontSize: 20,
        textDecorationLine: 'line-through',
        color: '#757575',
        marginRight: 10,
    },
    price: {
        fontSize: 24,
        fontWeight: 'bold',
    },
    discountInfoContainer: {
        width: '100%',
        backgroundColor: '#F5F5F5',
        padding: 12,
        borderRadius: 8,
        marginBottom: 24,
    },
    discountRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    discountLabel: {
        color: '#757575',
    },
    discountValue: {
        fontWeight: '500',
    },
    sectionTitle: {
        alignSelf: 'flex-start',
        fontSize: 16,
        fontWeight: '500',
        marginBottom: 12,
    },
    paymentInfoContainer: {
        width: '100%',
        backgroundColor: '#E8EAF6',
        padding: 12,
        borderRadius: 6,
        marginBottom: 24,
        alignItems: 'center',
    },
    paymentInfoText: {
        color: '#1E2A47',
        fontWeight: '500',
        fontSize: 14,
    },
    errorContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FFEBEE',
        padding: 10,
        borderRadius: 6,
        marginBottom: 16,
        width: '100%',
    },
    errorIcon: {
        marginRight: 8,
    },
    errorMessage: {
        color: '#FF0000',
        fontSize: 14,
        flex: 1,
    },
    buyButton: {
        backgroundColor: '#1E2A47',
        width: '100%',
        paddingVertical: 14,
        borderRadius: 6,
        alignItems: 'center',
    },
    disabledButton: {
        opacity: 0.7,
    },
    buyButtonText: {
        color: 'white',
        fontWeight: '500',
        fontSize: 16,
    },
});

export default PackagePurchaseDialog;