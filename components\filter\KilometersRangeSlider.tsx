import React from 'react';
import RangeSlider from './RangeSlider';

interface KilometersRangeSliderProps {
  minKm?: number;
  maxKm?: number;
  onChange: (minKm: number, maxKm: number) => void;
  onValueChange?: (minKm: number, maxKm: number) => void;
}

const KilometersRangeSlider: React.FC<KilometersRangeSliderProps> = ({
  minKm,
  maxKm,
  onChange,
  onValueChange
}) => {
  // Default kilometers range
  const MIN_KM = 0;
  const MAX_KM = 500000;
  const STEP = 1000;

  // Format kilometers for display
  const formatKm = (km: number) => {
    if (km >= 1000) {
      return `${(km / 1000).toFixed(0)}k km`;
    } else {
      return `${km} km`;
    }
  };

  return (
    <RangeSlider
      title="Kilometers Driven"
      minValue={MIN_KM}
      maxValue={MAX_KM}
      step={STEP}
      initialMinValue={minKm}
      initialMaxValue={maxKm}
      formatValue={formatKm}
      onChange={onChange}
      onValueChange={onValueChange}
    />
  );
};

export default KilometersRangeSlider;
