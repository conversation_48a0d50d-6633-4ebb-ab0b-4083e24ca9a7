import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { User as FirebaseUser } from "firebase/auth";

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

console.log("API_BASE_URL", API_BASE_URL);

interface RejectValue {
  message?: string;
  status: boolean;
}

interface User {
  name: string;
  email: string;
  phone: string;
  status?: string;
  id?: number;
  dob?: string;
  gender?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  profile_picture?: string;
}

interface GoogleRegisterResponse {
  status: boolean;
  phone: boolean;
  message: string;
  user: User;
  purchase: {
    headers: any;
    original: {
      status: boolean;
      message: string;
    };
    exception: any;
  };
  authorisation: {
    token: string;
    type: string;
  };
}

interface AuthState {
  user: User | FirebaseUser | null;
  token: string | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  forgotPasswordLoading: boolean;
  verifyOtpLoading: boolean;
  resetPasswordLoading: boolean;
  currentUid: string | null;
  requiresPhone: boolean;
  userData: any;
  purchase: any[];
}

interface ErrorResponse {
  message: string;
  status: boolean;
}

const initialState: AuthState = {
  user: null,
  token: null,
  loading: true,
  error: null,
  isAuthenticated: false,
  forgotPasswordLoading: false,
  verifyOtpLoading: false,
  resetPasswordLoading: false,
  currentUid: null,
  requiresPhone: false,
  userData: null,
  purchase: [],
};

// Check if email exists
export const checkEmail = createAsyncThunk(
  "auth/checkEmail",
  async (email: string, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/email/check`, {
        email,
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || "Email check failed",
        status: false,
      } as ErrorResponse);
    }
  }
);

// Login user
export const login = createAsyncThunk(
  "auth/login",
  async (
    credentials: { email: string; password: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/login`, credentials);
      if (response.data.status) {
        await AsyncStorage.setItem("token", response.data.authorisation.token);
        await AsyncStorage.setItem("user", JSON.stringify(response.data.user));
      }
      return response.data;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || "Login failed",
        status: false,
      } as ErrorResponse);
    }
  }
);

// Register user
export const register = createAsyncThunk(
  "auth/register",
  async (
    userData: { email: string; name: string; phone: string; password: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/register`, userData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data || { message: "Registration failed" }
      );
    }
  }
);

// Verify OTP
export const verifyOtp = createAsyncThunk(
  "auth/verifyOtp",
  async (data: { email: string; otp: string }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/register-otp`, data);
      if (response.data.status) {
        await AsyncStorage.setItem("token", response.data.authorisation.token);
        await AsyncStorage.setItem("user", JSON.stringify(response.data.user));
      }
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data || { message: "OTP verification failed" }
      );
    }
  }
);

// Initialize auth state
export const initializeAuth = createAsyncThunk(
  "auth/initialize",
  async (_, { getState }) => {
    const state = getState() as { auth: AuthState };

    // If we already have user data, don't reset it
    if (state.auth.user && state.auth.token) {
      return {
        token: state.auth.token,
        user: state.auth.user,
      };
    }

    // Otherwise check storage
    const token = await AsyncStorage.getItem("token");
    const userStr = await AsyncStorage.getItem("user");
    const user = userStr ? JSON.parse(userStr) : null;
    return { token, user };
  }
);

// Logout
export const logout = createAsyncThunk(
  "auth/logout",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: AuthState };
      if (state.auth.token) {
        // Call logout API if needed
        await axios.post(
          `${API_BASE_URL}/logout`,
          {},
          {
            headers: {
              Authorization: `Bearer ${state.auth.token}`,
            },
          }
        );
      }
      // Clear storage
      await AsyncStorage.removeItem("token");
      await AsyncStorage.removeItem("user");
      return true;
    } catch (error) {
      // Even if API fails, clear local storage
      await AsyncStorage.removeItem("token");
      await AsyncStorage.removeItem("user");
      return true;
    }
  }
);

// Add this helper function to check stored data
export const checkStoredAuth = createAsyncThunk(
  "auth/checkStored",
  async () => {
    const token = await AsyncStorage.getItem("token");
    const user = await AsyncStorage.getItem("user");
    console.log("Stored token:", token);
    console.log("Stored user:", user);
    return { token, user: user ? JSON.parse(user) : null };
  }
);

// Add these new async thunks
export const forgotPassword = createAsyncThunk(
  "auth/forgotPassword",
  async (email: string, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/forgot-password`, {
        email,
      });
      console.log("Forgot password response:", response.data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || "Failed to send OTP",
        status: false,
      });
    }
  }
);

export const verifyForgotPasswordOtp = createAsyncThunk(
  "auth/verifyForgotPasswordOtp",
  async (data: { email: string; otp: string }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/verify-otp`, data);
      console.log("Verify forgot password OTP response:", response.data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || "OTP verification failed",
        status: false,
      });
    }
  }
);

export const resetPassword = createAsyncThunk(
  "auth/resetPassword",
  async (
    data: { email: string; newPassword: string; otp: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/reset-password`, data);
      if (response.data.status) {
        await AsyncStorage.setItem("token", response.data.authorisation.token);
        await AsyncStorage.setItem("user", JSON.stringify(response.data.user));
      }
      return response.data;
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || "Password reset failed",
        status: false,
      });
    }
  }
);

// Add this new action for Google sign-in
export const googleSignIn = createAsyncThunk(
  "auth/googleSignIn",
  async (user: FirebaseUser, { rejectWithValue }) => {
    try {
      // Get the ID token from Firebase user
      const idToken = await user.getIdToken();

      // Store the token and user in AsyncStorage
      await AsyncStorage.setItem("token", idToken);
      await AsyncStorage.setItem("user", JSON.stringify(user));

      return {
        user,
        token: idToken,
        status: true,
      };
    } catch (error: any) {
      return rejectWithValue({
        message: error.message || "Google sign-in failed",
        status: false,
      } as ErrorResponse);
    }
  }
);

// Add these new actions for Google sign-in and phone registration
export const registerWithGoogle = createAsyncThunk(
  "auth/registerWithGoogle",
  async (uid: string, { rejectWithValue }) => {
    try {
      const response = await fetch(`${API_BASE_URL}/register-google`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ uid }),
      });

      const data = await response.json();
      console.log("Google registration API response:", data);

      // If this is a new user that needs phone verification
      if (data.user?.status === "0" || !data.authorisation?.token) {
        // Instead of rejecting, return a special success result that indicates phone is required
        console.log("inside if google registration");
        return {
          ...data,
          requiresPhone: true,
          uid,
        };
      }

      // Normal success case - user is registered and authenticated
      return data;
    } catch (error: any) {
      // Only reject for actual errors
      return rejectWithValue({
        message: error.message || "Registration failed",
        status: false,
      });
    }
  }
);

export const registerWithPhone = createAsyncThunk(
  "auth/registerWithPhone",
  async (
    { uid, phone }: { uid: string; phone: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await fetch(`${API_BASE_URL}/register-phone`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ uid, phone }),
      });

      const data = await response.json();

      if (data.status && data.authorisation?.token) {
        // Store auth data in AsyncStorage
        await AsyncStorage.setItem("token", data.authorisation.token);
        await AsyncStorage.setItem("user", JSON.stringify(data.user));
        return data;
      }

      return rejectWithValue({
        message: data.message || "Phone registration failed",
      });
    } catch (error: any) {
      return rejectWithValue({
        message: error.message || "Phone registration failed",
      });
    }
  }
);

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    resetAuthState: (state) => {
      state.error = null;
      state.loading = false;
      state.requiresPhone = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // Initialize cases
      .addCase(initializeAuth.pending, (state) => {
        state.loading = true;
      })
      .addCase(initializeAuth.fulfilled, (state, action) => {
        console.log("Initialize auth payload:", action.payload);
        state.token = action.payload.token;
        state.user = action.payload.user;
        state.isAuthenticated = !!action.payload.token;
        state.loading = false;
      })
      .addCase(initializeAuth.rejected, (state) => {
        state.loading = false;
        state.isAuthenticated = false;
      })
      // Login cases
      .addCase(login.fulfilled, (state, action) => {
        console.log("Login response:", action.payload);
        if (action.payload.status) {
          state.user = action.payload.user;
          state.token = action.payload.authorisation.token;
          state.isAuthenticated = true;
          state.error = null;
          AsyncStorage.setItem("token", action.payload.authorisation.token);
          AsyncStorage.setItem("user", JSON.stringify(action.payload.user));
        }
      })
      .addCase(login.rejected, (state, action) => {
        // ✅ Type-safe handling of errors
        if (
          action.payload &&
          typeof action.payload === "object" &&
          "message" in action.payload
        ) {
          state.error = action.payload.message as string;
        } else {
          state.error = "Login failed";
        }
        state.isAuthenticated = false;
      })
      // Register cases
      .addCase(register.fulfilled, (state, action) => {
        state.error = null;
      })
      .addCase(register.rejected, (state, action) => {
        if (
          action.payload &&
          typeof action.payload === "object" &&
          "message" in action.payload
        ) {
          state.error = action.payload.message as string;
        } else {
          state.error = "Registration failed";
        }
      })
      // Verify OTP cases
      .addCase(verifyOtp.fulfilled, (state, action) => {
        console.log("OTP verify response:", action.payload);
        if (action.payload.status) {
          state.user = action.payload.user;
          state.token = action.payload.authorisation.token;
          state.isAuthenticated = true;
          state.error = null;
          AsyncStorage.setItem("token", action.payload.authorisation.token);
          AsyncStorage.setItem("user", JSON.stringify(action.payload.user));
        }
      })
      .addCase(verifyOtp.rejected, (state, action) => {
        if (
          action.payload &&
          typeof action.payload === "object" &&
          "message" in action.payload
        ) {
          state.error = action.payload.message as string;
        } else {
          state.error = "OTP verification failed";
        }
      })
      // Logout cases
      .addCase(logout.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.error = null;
      })
      // Add checkStoredAuth cases
      .addCase(checkStoredAuth.pending, (state) => {
        state.loading = true;
      })
      .addCase(checkStoredAuth.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload.user) {
          state.user = action.payload.user;
          state.token = action.payload.token;
          state.isAuthenticated = true;
        }
      })
      .addCase(checkStoredAuth.rejected, (state) => {
        state.loading = false;
      })
      // Forgot password cases
      .addCase(forgotPassword.pending, (state) => {
        state.forgotPasswordLoading = true;
        state.error = null;
      })
      .addCase(forgotPassword.fulfilled, (state) => {
        state.forgotPasswordLoading = false;
        state.error = null;
      })
      .addCase(forgotPassword.rejected, (state, action) => {
        state.forgotPasswordLoading = false;
        state.error =
          (action.payload as RejectValue)?.message || "Failed to send OTP";
      })
      // Verify OTP cases
      .addCase(verifyForgotPasswordOtp.pending, (state) => {
        state.verifyOtpLoading = true;
        state.error = null;
      })
      .addCase(verifyForgotPasswordOtp.fulfilled, (state) => {
        state.verifyOtpLoading = false;
        state.error = null;
      })
      .addCase(verifyForgotPasswordOtp.rejected, (state, action) => {
        state.verifyOtpLoading = false;
        state.error =
          (action.payload as RejectValue)?.message || "OTP verification failed";
      })
      // Reset password cases
      .addCase(resetPassword.pending, (state) => {
        state.resetPasswordLoading = true;
        state.error = null;
      })
      .addCase(resetPassword.fulfilled, (state, action) => {
        state.resetPasswordLoading = false;
        if (action.payload.status) {
          state.user = action.payload.user;
          state.token = action.payload.authorisation.token;
          state.isAuthenticated = true;
          state.error = null;
        }
      })
      .addCase(resetPassword.rejected, (state, action) => {
        state.resetPasswordLoading = false;
        state.error =
          (action.payload as RejectValue)?.message || "Password reset failed";
      })
      // Google sign-in cases
      .addCase(googleSignIn.fulfilled, (state, action) => {
        if (action.payload.status) {
          state.user = action.payload.user;
          state.token = action.payload.token;
          state.isAuthenticated = true;
          state.error = null;
        }
      })
      .addCase(googleSignIn.rejected, (state, action) => {
        if (
          action.payload &&
          typeof action.payload === "object" &&
          "message" in action.payload
        ) {
          state.error = action.payload.message as string;
        } else {
          state.error = "Google sign-in failed";
        }
        state.isAuthenticated = false;
      })
      // Google registration cases
      .addCase(registerWithGoogle.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerWithGoogle.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload.status) {
          state.user = action.payload.user;
          state.token = action.payload.authorisation.token;
          state.isAuthenticated = true;
          state.requiresPhone = false;
          state.currentUid = null;
        }
      })
      .addCase(registerWithGoogle.rejected, (state, action) => {
        state.loading = false;
        if (
          action.payload &&
          typeof action.payload === "object" &&
          "requiresPhone" in action.payload
        ) {
          state.requiresPhone = true;
          state.currentUid = (action.payload as any).uid;
        } else {
          state.error =
            (action.payload as any)?.message || "Google registration failed";
        }
      })
      // Phone registration cases
      .addCase(registerWithPhone.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerWithPhone.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload.status) {
          state.user = action.payload.user;
          state.token = action.payload.authorisation.token;
          state.isAuthenticated = true;
          state.requiresPhone = false;
          state.currentUid = null;
        }
      })
      .addCase(registerWithPhone.rejected, (state, action) => {
        state.loading = false;
        state.error =
          (action.payload as any)?.message || "Phone registration failed";
      });
  },
});

export const { resetAuthState } = authSlice.actions;
export default authSlice.reducer;
