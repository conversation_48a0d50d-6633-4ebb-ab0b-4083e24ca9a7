export interface Vehicle {
  id: number;
  user_id: number;
  title: string;
  brand: string;
  model: string;
  variant: string;
  year: string;
  fuel_type: string;
  kilometers_driven: number;
  price: string;
  make_offer: string | null;
  transmission: string;
  ownership: string;
  location: string;
  description: string;
  images: string[];
  is_verified: boolean;
  status: string;
  primary_image: string;
  type: string;
  auction: boolean;
  insurance_type?: string;
  user: {
    id: number;
    name: string;
    phone: string;
    email: string;
    role: string;
    status: string;
    dob: string;
    gender: string;
    address: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
    profile_picture: string;
    created_at: string;
    updated_at: string;
  };
}

export interface VehicleResponse {
  status: boolean;
  message: string;
  type: string;
  data: Vehicle[];
}
