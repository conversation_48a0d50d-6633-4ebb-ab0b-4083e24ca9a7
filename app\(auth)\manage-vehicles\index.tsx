import React, { use<PERSON>allback, useMemo, useState } from 'react';
import {
    View,
    StyleSheet,
    TouchableOpacity,
    Image,
    FlatList,
    StatusBar,
    TextStyle,
    ViewStyle,
    ImageStyle,
    ActivityIndicator,
    Alert,
    Linking,
    Platform,
} from 'react-native';
import { useFocusEffect, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from '@/components/common/ThemedText';
import { VehicleTypeSelector } from '@/components/home/<USER>';
import SellBuyTabs from '@/components/account/SellBuyTabs';
import { SearchBar } from '@/components/home/<USER>';
import { COLORS, FONTS, SPACING, BORDERS } from '@/constants/theme';
import Constants from 'expo-constants';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store/store';
import {
    listManageVehicles,
    setSelectedType,
    updateVehicleStatus
} from '@/store/slices/manageVehiclesSlice';
import { deleteVehicle } from '@/store/slices/editVehicleSlice';
import { VehicleType } from '@/store/slices/vehicleSlice';
import { VehicleSkeletonList } from '@/components/common/VehicleCardSkeleton';
import { Vehicle } from '@/types/vehicle';
import { ManageVehicle } from '@/types/manageVehicle';

// Extract the VehicleCard component for better organization
const VehicleCard = React.memo(({
    vehicle,
    onCallPress,
    onChatPress,
    onListingToggle,
    onAuctionToggle,
    onEditPress,
    onDeletePress,
    loading,
    deleting
}: {
    vehicle: ManageVehicle;
    onCallPress: (vehicle: any) => void;
    onChatPress: (vehicle: any) => void;
    onListingToggle: (vehicleId: number) => void;
    onAuctionToggle: (vehicleId: number) => void;
    onEditPress: (vehicle: ManageVehicle) => void;
    onDeletePress: (vehicle: ManageVehicle) => void;
    loading: boolean;
    deleting: boolean;
}) => {
    const handleImageError = () => {
        console.warn(`Failed to load image for vehicle ${vehicle.id}`);
    };

    return (
        <View style={styles.vehicleCard}>
            <View style={styles.vehicleImageContainer}>
                <Image
                    source={{ uri: vehicle.primary_image }}
                    style={styles.carImage}
                    resizeMode="cover"
                    defaultSource={require('@/assets/images/car2Image.png')}
                    onError={handleImageError}
                    accessible={true}
                    accessibilityLabel={`Image of ${vehicle.title}`}
                />
            </View>

            <View style={styles.vehicleDetailsContainer}>
                <ThemedText style={styles.vehicleTitle}>{vehicle.title}</ThemedText>
                <ThemedText style={styles.vehicleModel}>
                    {vehicle.brand} {vehicle.model} {vehicle.variant}
                </ThemedText>
                <ThemedText style={styles.vehiclePrice}>₹ {vehicle.price}</ThemedText>

                {/* Action Buttons Row */}
                <View style={styles.actionButtonsRow}>
                    <TouchableOpacity
                        style={[styles.actionButton, styles.callButton]}
                        onPress={() => onCallPress(vehicle)}
                        accessibilityLabel={`Call about ${vehicle.title}`}
                        accessibilityRole="button"
                    >
                        <Ionicons name="call" size={16} color={COLORS.white} />
                        <ThemedText style={styles.actionButtonText}>Call</ThemedText>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.actionButton, styles.chatButton]}
                        onPress={() => onChatPress(vehicle)}
                        accessibilityLabel={`Message about ${vehicle.title}`}
                        accessibilityRole="button"
                    >
                        <Ionicons name="chatbubble" size={16} color={COLORS.white} />
                        <ThemedText style={styles.actionButtonText}>Chat</ThemedText>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.actionButton, styles.editButton]}
                        onPress={() => onEditPress(vehicle)}
                        accessibilityLabel={`Edit ${vehicle.title}`}
                        accessibilityRole="button"
                    >
                        <Ionicons name="pencil" size={16} color={COLORS.white} />
                        <ThemedText style={styles.actionButtonText}>Edit</ThemedText>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.actionButton, styles.deleteButton]}
                        onPress={() => onDeletePress(vehicle)}
                        accessibilityLabel={`Delete ${vehicle.title}`}
                        accessibilityRole="button"
                        disabled={deleting}
                    >
                        {deleting ? (
                            <ActivityIndicator size="small" color={COLORS.white} />
                        ) : (
                            <>
                                <Ionicons name="trash" size={16} color={COLORS.white} />
                                <ThemedText style={styles.actionButtonText}>Delete</ThemedText>
                            </>
                        )}
                    </TouchableOpacity>
                </View>

                {/* Status Toggle Buttons */}
                <View style={styles.statusRow}>
                    <TouchableOpacity
                        style={[
                            styles.statusButton,
                            vehicle.is_verified ? styles.statusButtonActive : styles.statusButtonInactive
                        ]}
                        onPress={() => onListingToggle(vehicle.id)}
                        disabled={loading}
                        accessibilityRole="checkbox"
                        accessibilityState={{ checked: vehicle.is_verified }}
                        accessibilityLabel={`Toggle listing for ${vehicle.title}`}
                    >
                        <View style={styles.checkboxWrapper}>
                            <View style={[
                                styles.checkbox,
                                vehicle.is_verified && styles.checkboxSelected
                            ]}>
                                {vehicle.is_verified && (
                                    <Ionicons name="checkmark" size={14} color={COLORS.white} />
                                )}
                            </View>
                        </View>
                        <ThemedText style={[
                            styles.statusButtonText,
                            vehicle.is_verified ? styles.statusButtonTextActive : styles.statusButtonTextInactive
                        ]}>
                            {loading ? 'Updating...' : vehicle.is_verified ? 'Listed' : 'Not Listed'}
                        </ThemedText>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[
                            styles.statusButton,
                            vehicle?.auction ? styles.statusButtonActive : styles.statusButtonInactive
                        ]}
                        onPress={() => onAuctionToggle(vehicle.id)}
                        accessibilityRole="checkbox"
                        accessibilityState={{ checked: vehicle?.auction }}
                        accessibilityLabel={`Toggle auction for ${vehicle.title}`}
                    >
                        <View style={styles.checkboxWrapper}>
                            <View style={[
                                styles.checkbox,
                                vehicle.auction && styles.checkboxSelected
                            ]}>
                                {vehicle.auction && (
                                    <Ionicons name="checkmark" size={14} color={COLORS.white} />
                                )}
                            </View>
                        </View>
                        <ThemedText style={[
                            styles.statusButtonText,
                            vehicle.auction ? styles.statusButtonTextActive : styles.statusButtonTextInactive
                        ]}>
                            {vehicle.auction ? 'In Auction' : 'Not in Auction'}
                        </ThemedText>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
});

// Create a separate component for empty state
const EmptyState = () => (
    <View style={styles.emptyContainer}>
        <Ionicons name="car-outline" size={64} color={COLORS.text.secondary} />
        <ThemedText style={styles.emptyText}>
            No vehicles found
        </ThemedText>
        <ThemedText style={styles.emptySubText}>
            Try adjusting your search or filters
        </ThemedText>
    </View>
);

// Create a separate component for error state
const ErrorState = ({ error, onRetry }: { error: string, onRetry: () => void }) => (
    <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color={COLORS.error} />
        <ThemedText style={styles.errorText}>{error}</ThemedText>
        <TouchableOpacity
            style={styles.retryButton}
            onPress={onRetry}
            accessibilityLabel="Retry loading vehicles"
            accessibilityRole="button"
        >
            <ThemedText style={styles.retryButtonText}>Retry</ThemedText>
        </TouchableOpacity>
    </View>
);

export default function ManageVehiclesScreen() {
    const router = useRouter();
    const [activeTab, setActiveTab] = useState<'sell' | 'buy'>('sell');
    const [selectedVehicleType, setSelectedVehicleType] = useState<VehicleType>('car');
    const [searchQuery, setSearchQuery] = useState('');
    const [isRefreshing, setIsRefreshing] = useState(false);

    const dispatch = useDispatch<AppDispatch>();
    const { vehicles, loading, error } = useSelector((state: RootState) => state.manageVehicles);
    const { deleting } = useSelector((state: RootState) => state.editVehicle);

    // Filter vehicles based on search query using useMemo for performance
    const filteredVehicles = useMemo(() =>
        vehicles.filter(vehicle =>
            vehicle.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            vehicle.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (vehicle.price && vehicle.price.toLowerCase().includes(searchQuery.toLowerCase()))
        ),
        [vehicles, searchQuery]
    );

    const handleVehicleTypeSelect = useCallback((type: VehicleType) => {
        setSelectedVehicleType(type);
        dispatch(setSelectedType(type));
    }, [dispatch]);

    const fetchVehicles = useCallback(() => {
        dispatch(listManageVehicles({
            type: selectedVehicleType,
            search: searchQuery
        }));
    }, [selectedVehicleType, searchQuery, dispatch]);

    // Use useFocusEffect with useCallback to prevent unnecessary re-renders
    useFocusEffect(
        useCallback(() => {
            fetchVehicles();
            // Optional cleanup function
            return () => {
                // Any cleanup code if needed
            };
        }, [fetchVehicles])
    );

    const handleRefresh = useCallback(() => {
        setIsRefreshing(true);
        fetchVehicles();
        setIsRefreshing(false);
    }, [fetchVehicles]);

    // Improved phone handling with proper validation
    const handleCallPress = useCallback((vehicle: Vehicle) => {
        if (!vehicle?.user?.phone) {
            Alert.alert('Error', 'Phone number not available');
            return;
        }

        const phoneNumber = vehicle.user.phone.trim();

        // iOS simulator check using Expo Constants
        if (Platform.OS === 'ios' && !Constants.isDevice) {
            Alert.alert('Error', 'Phone calls are not supported on the iOS simulator.');
            return;
        }

        // Clean the phone number, preserving the leading '+'
        const cleanedPhoneNumber = phoneNumber.startsWith('+')
            ? '+' + phoneNumber.slice(1).replace(/\D/g, '')
            : phoneNumber.replace(/\D/g, '');

        // Validate phone number length
        const numericPhoneNumber = cleanedPhoneNumber.startsWith('+')
            ? cleanedPhoneNumber.slice(1)
            : cleanedPhoneNumber;

        if (numericPhoneNumber.length < 10) {
            Alert.alert('Error', 'Invalid phone number');
            return;
        }

        // Choose URL scheme based on platform
        const urlScheme = Platform.OS === 'ios' ? 'telprompt:' : 'tel:';
        const phoneUrl = `${urlScheme}${cleanedPhoneNumber}`;

        Linking.canOpenURL(phoneUrl)
            .then(supported => {
                if (!supported) {
                    Alert.alert('Error', 'Phone calls are not supported on this device');
                    return;
                }
                return Linking.openURL(phoneUrl);
            })
            .catch(error => {
                console.error('Failed to open URL:', error);
                Alert.alert(
                    'Error',
                    'Could not initiate the phone call. Your device may not support this feature.'
                );
            });
    }, []);

    const handleChatPress = useCallback((vehicle: Vehicle) => {
        if (!vehicle?.user?.phone) {
            Alert.alert('Error', 'Phone number not available');
            return;
        }

        // Clean phone number (remove any non-numeric characters)
        const phoneNumber = vehicle.user.phone.trim();
        const cleanPhoneNumber = phoneNumber.replace(/\D/g, '');

        // Make sure we have a valid phone number
        if (!cleanPhoneNumber || cleanPhoneNumber.length < 10) {
            Alert.alert('Error', 'Invalid phone number');
            return;
        }

        // Add country code if not already present
        const phoneWithCountryCode = cleanPhoneNumber.startsWith('91')
            ? cleanPhoneNumber
            : `91${cleanPhoneNumber}`;

        const whatsappMessage = `Hi, I am interested in your ${vehicle.title} listed on 2ndCar. Is it still available?`;
        const whatsappUrl = `whatsapp://send?phone=${phoneWithCountryCode}&text=${encodeURIComponent(whatsappMessage)}`;

        Linking.canOpenURL(whatsappUrl)
            .then(async supported => {
                if (supported) {
                    return Linking.openURL(whatsappUrl);
                } else {
                    // Try web URL as fallback
                    const webWhatsappUrl = `https://wa.me/${phoneWithCountryCode}?text=${encodeURIComponent(whatsappMessage)}`;
                    try {
                        return await Linking.openURL(webWhatsappUrl);
                    } catch {
                        throw new Error('WhatsApp is not installed on your device');
                    }
                }
            })
            .catch(error => {
                console.error('Error opening WhatsApp:', error);
                Alert.alert(
                    'Error',
                    'Could not open WhatsApp. Please make sure WhatsApp is installed on your device.'
                );
            });
    }, []);

    const handleListingToggle = useCallback(async (vehicleId: number) => {
        try {
            const vehicle = vehicles.find(v => v.id === vehicleId);
            if (!vehicle) return;

            const result = await dispatch(updateVehicleStatus({
                vehicleId,
                status: !vehicle.is_verified,
                vehicleType: selectedVehicleType
            })).unwrap();

            if (result.status) {
                // Refresh the vehicles list
                fetchVehicles();
            }
        } catch (error) {
            console.error('Failed to update vehicle status:', error);
            Alert.alert(
                'Error',
                'Failed to update vehicle listing status. Please try again.'
            );
        }
    }, [dispatch, fetchVehicles, selectedVehicleType, vehicles]);

    const handleAuctionToggle = useCallback((vehicleId: number) => {
        // Implementation for auction toggle (placeholder)
        // dispatch(toggleAuctionStatus(vehicleId));
    }, []);

    const handleEditPress = useCallback((vehicle: ManageVehicle) => {
        // Navigate to edit screen for all vehicle types
        router.push(`/(auth)/manage-vehicles/edit/${vehicle.id}?type=${vehicle.type}` as any);
    }, [router]);

    const handleDeletePress = useCallback((vehicle: ManageVehicle) => {
        Alert.alert(
            'Delete Vehicle',
            `Are you sure you want to delete "${vehicle.title}"? This action cannot be undone.`,
            [
                {
                    text: 'Cancel',
                    style: 'cancel',
                },
                {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await dispatch(deleteVehicle({
                                vehicleId: vehicle.id.toString(),
                                vehicleType: vehicle.type
                            })).unwrap();

                            Alert.alert('Success', 'Vehicle deleted successfully!');
                            // Refresh the vehicles list
                            fetchVehicles();
                        } catch (error: any) {
                            console.error('Delete error:', error);
                            Alert.alert('Error', error?.message || 'Failed to delete vehicle. Please try again.');
                        }
                    },
                },
            ]
        );
    }, [dispatch, fetchVehicles]);

    // Use a memoized render function for the FlatList
    const renderVehicleItem = useCallback(({ item }: { item: ManageVehicle }) => (
        <VehicleCard
            vehicle={item}
            onCallPress={handleCallPress}
            onChatPress={handleChatPress}
            onListingToggle={handleListingToggle}
            onAuctionToggle={handleAuctionToggle}
            onEditPress={handleEditPress}
            onDeletePress={handleDeletePress}
            loading={loading}
            deleting={deleting}
        />
    ), [handleCallPress, handleChatPress, handleListingToggle, handleAuctionToggle, handleEditPress, handleDeletePress, loading, deleting]);

    // Memoize list empty component
    const ListEmptyComponent = useCallback(() => (
        loading ? <VehicleSkeletonList count={3} /> : <EmptyState />
    ), [loading]);

    // Memoize list header component
    const ListHeaderComponent = useCallback(() => (
        loading && filteredVehicles.length > 0 ? (
            <View style={styles.loadingBanner}>
                <ActivityIndicator size="small" color={COLORS.primary} />
                <ThemedText style={styles.loadingBannerText}>
                    Refreshing vehicles...
                </ThemedText>
            </View>
        ) : null
    ), [loading, filteredVehicles.length]);

    // Memoize list footer component
    const ListFooterComponent = useCallback(() => (
        loading ? <VehicleSkeletonList count={2} /> : null
    ), [loading]);

    return (
        <View style={styles.container}>
            <StatusBar barStyle="light-content" />

            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity
                    onPress={() => router.back()}
                    style={styles.backButton}
                    accessibilityLabel="Go back"
                    accessibilityRole="button"
                >
                    <Ionicons name="arrow-back" size={24} color={COLORS.white} />
                </TouchableOpacity>
                <ThemedText style={styles.headerTitle}>Manage Vehicles</ThemedText>
                <View style={{ width: 24 }} />
            </View>

            {/* Animated Sell/Buy Tabs */}
            <SellBuyTabs activeTab={activeTab} onTabChange={setActiveTab} />

            {/* Vehicle Type Selector */}
            <VehicleTypeSelector
                selectedType={selectedVehicleType}
                onTypeSelect={handleVehicleTypeSelect}
            />
            {/* Search Bar */}
            <View style={styles.searchContainer}>
                <SearchBar
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    placeholder="Search by title, model or price"
                />
            </View>
            {/* Main Content */}
            {error ? (
                <ErrorState error={error} onRetry={fetchVehicles} />
            ) : (
                <FlatList
                    data={filteredVehicles}
                    renderItem={renderVehicleItem}
                    keyExtractor={item => item.id.toString()}
                    contentContainerStyle={styles.vehiclesList}
                    showsVerticalScrollIndicator={false}
                    initialNumToRender={5}
                    maxToRenderPerBatch={10}
                    windowSize={10}
                    refreshing={isRefreshing}
                    onRefresh={handleRefresh}
                    ListEmptyComponent={ListEmptyComponent}
                    ListHeaderComponent={ListHeaderComponent}
                    ListFooterComponent={ListFooterComponent}
                    removeClippedSubviews={true}
                />
            )}
        </View>
    );
}

interface Styles {
    container: ViewStyle;
    header: ViewStyle;
    backButton: ViewStyle;
    headerTitle: TextStyle;
    vehiclesList: ViewStyle;
    searchContainer: ViewStyle;
    vehicleCard: ViewStyle;
    vehicleImageContainer: ViewStyle;
    carImage: ImageStyle;
    vehicleDetailsContainer: ViewStyle;
    vehicleTitle: TextStyle;
    vehicleModel: TextStyle;
    vehiclePrice: TextStyle;
    actionButtonsRow: ViewStyle;
    actionButton: ViewStyle;
    actionButtonText: TextStyle;
    callButton: ViewStyle;
    chatButton: ViewStyle;
    editButton: ViewStyle;
    deleteButton: ViewStyle;
    statusRow: ViewStyle;
    statusButton: ViewStyle;
    statusButtonActive: ViewStyle;
    statusButtonInactive: ViewStyle;
    checkboxWrapper: ViewStyle;
    checkbox: ViewStyle;
    checkboxSelected: ViewStyle;
    statusButtonText: TextStyle;
    statusButtonTextActive: TextStyle;
    statusButtonTextInactive: TextStyle;
    loadingBanner: ViewStyle;
    loadingBannerText: TextStyle;
    errorContainer: ViewStyle;
    errorText: TextStyle;
    retryButton: ViewStyle;
    retryButtonText: TextStyle;
    emptyContainer: ViewStyle;
    emptyText: TextStyle;
    emptySubText: TextStyle;
}

const styles = StyleSheet.create<Styles>({
    container: {
        flex: 1,
        backgroundColor: COLORS.background,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingTop: 16 + Constants.statusBarHeight,
        backgroundColor: COLORS.primary,
        paddingHorizontal: 16,
        paddingVertical: 14,
        elevation: 4,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    backButton: {
        padding: SPACING.xs,
        borderRadius: 20,
    },
    headerTitle: {
        fontSize: 20,
        ...FONTS.medium,
        fontWeight: 'bold',
        color: COLORS.white,
    } as TextStyle,
    vehiclesList: {
        paddingHorizontal: SPACING.md,
        paddingBottom: SPACING.xl,
    },
    searchContainer: {
        height: 70,
        paddingHorizontal: SPACING.md,
        marginVertical: SPACING.sm,
    },

    // Vehicle Card Styles
    vehicleCard: {
        backgroundColor: COLORS.white,
        borderRadius: 16,
        marginBottom: 20,
        marginHorizontal: 4,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.12,
        shadowRadius: 8,
        elevation: 4,
        overflow: 'hidden',
        borderWidth: 0.5,
        borderColor: '#F0F0F0',
    },
    vehicleImageContainer: {
        width: '100%',
        overflow: 'hidden',
        position: 'relative',
    },
    carImage: {
        width: '100%',
        height: 200,
        resizeMode: 'cover',
    },
    vehicleDetailsContainer: {
        padding: 20,
    },
    vehicleTitle: {
        fontSize: 18,
        fontWeight: '700' as const,
        color: '#1A1A1A',
        marginBottom: 6,
        lineHeight: 24,
    },
    vehicleModel: {
        fontSize: 14,
        color: '#666666',
        marginBottom: 10,
        lineHeight: 18,
    },
    vehiclePrice: {
        fontSize: 20,
        fontWeight: '700' as const,
        color: '#2E7D32', // Professional green for price
        marginBottom: 16,
    },
    // Action Buttons Row
    actionButtonsRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: SPACING.lg,
        marginBottom: SPACING.md,
        gap: SPACING.sm,
    },
    actionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 10,
        paddingHorizontal: 12,
        borderRadius: 8,
        minWidth: 70,
        flex: 1,
        gap: 4,
        // Enhanced shadow for better elevation
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.15,
        shadowRadius: 4,
        elevation: 3,
    },
    actionButtonText: {
        color: COLORS.white,
        fontSize: 12,
        fontWeight: '600' as const,
        textAlign: 'center',
    },
    callButton: {
        backgroundColor: '#2E7D32', // Professional green for call
    },
    chatButton: {
        backgroundColor: '#1B264F', // Professional blue for chat
    },
    editButton: {
        backgroundColor: '#F57C00', // Professional orange for edit
    },
    deleteButton: {
        backgroundColor: '#D32F2F', // Professional red for delete
    },

    // Status Row
    statusRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: SPACING.md,
        gap: SPACING.sm,
    },
    statusButton: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 14,
        borderRadius: 10,
        borderWidth: 1.5,
        // Add subtle shadow
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.08,
        shadowRadius: 3,
        elevation: 2,
    },
    statusButtonActive: {
        backgroundColor: '#E3F2FD', // Light blue background
        borderColor: '#1B264F', // Blue border
    },
    statusButtonInactive: {
        backgroundColor: '#FAFAFA', // Light gray background
        borderColor: '#E0E0E0', // Gray border
    },
    checkboxWrapper: {
        marginRight: 10,
    },
    checkbox: {
        width: 20,
        height: 20,
        borderRadius: 4,
        borderWidth: 2,
        borderColor: '#E0E0E0',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: COLORS.white,
    },
    checkboxSelected: {
        backgroundColor: '#1B264F', // Blue background when selected
        borderColor: '#1B264F',
    },
    statusButtonText: {
        fontSize: 13,
        fontWeight: '600' as const,
        flex: 1,
    },
    statusButtonTextActive: {
        color: '#1B264F', // Blue text when active
    },
    statusButtonTextInactive: {
        color: '#757575', // Gray text when inactive
    },

    // Loading states
    loadingBanner: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: COLORS.white,
        padding: SPACING.sm,
        borderRadius: BORDERS.radius.sm,
        marginBottom: SPACING.md,
        justifyContent: 'center',
    },
    loadingBannerText: {
        marginLeft: SPACING.sm,
        color: COLORS.text.secondary,
        fontSize: FONTS.size.caption,
    },

    // Error and empty states
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: SPACING.lg,
    },
    errorText: {
        color: COLORS.error,
        fontSize: FONTS.size.body,
        textAlign: 'center',
        marginTop: SPACING.sm,
        marginBottom: SPACING.md,
    },
    retryButton: {
        backgroundColor: COLORS.primary,
        paddingVertical: SPACING.sm,
        paddingHorizontal: SPACING.lg,
        borderRadius: BORDERS.radius.sm,
    },
    retryButtonText: {
        color: COLORS.white,
        fontWeight: '600' as const,
    },
    emptyContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: SPACING.xl * 2,
    },
    emptyText: {
        fontSize: FONTS.size.h3,
        fontWeight: '700' as const,
        color: COLORS.text.secondary,
        marginTop: SPACING.md,
    },
    emptySubText: {
        fontSize: FONTS.size.caption,
        color: COLORS.text.secondary,
        marginTop: SPACING.xs,
    },
});