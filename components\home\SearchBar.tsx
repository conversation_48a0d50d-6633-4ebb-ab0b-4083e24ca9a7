import React, { useRef, forwardRef, useEffect, useState } from 'react';
import { View, TextInput, StyleSheet, TextInputProps, TouchableOpacity, NativeSyntheticEvent, TextInputFocusEventData, TextStyle } from 'react-native';
import { COLORS, SPACING, FONTS, BORDERS } from '@/constants/theme';
import SearchIcon from '@/assets/icon/search-icon-svg.svg';

interface SearchBarProps extends TextInputProps {
    value: string;
    onChangeText: (text: string) => void;
    onSearch?: (text: string) => void;
    placeholder?: string;
    isNavigationBar?: boolean; // Flag to indicate if this is used for navigation
    onFocus?: ((e: NativeSyntheticEvent<TextInputFocusEventData>) => void) | (() => void);
    onNavigationPress?: () => void;
    debounceTime?: number; // Time in ms to wait before triggering search
}

export const SearchBar = forwardRef<TextInput, SearchBarProps>(({
    value,
    onChangeText,
    onSearch,
    placeholder = "Search cars",
    isNavigationBar = false,
    onFocus,
    onNavigationPress,
    debounceTime = 500, // Default debounce time of 500ms
    ...props
}, ref) => {
    const inputRef = useRef<TextInput>(null);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);
    const [localValue, setLocalValue] = useState(value);

    // Sync the local value with the prop value when it changes externally
    useEffect(() => {
        setLocalValue(value);
    }, [value]);

    // Handle user input with debouncing
    const handleTextChange = (text: string) => {
        // Update the local state immediately for UI feedback
        setLocalValue(text);

        // Clear any existing timeout
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        // Set a new timeout for the debounced update
        timeoutRef.current = setTimeout(() => {
            // Only call onChangeText after the debounce period
            onChangeText(text);

            // If search is provided, trigger it
            if (onSearch) {
                onSearch(text);
            }
        }, debounceTime);
    };

    // Handle submit event (e.g., when user presses enter/search)
    const handleSubmit = () => {
        // Clear any pending debounce
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
        }

        // Ensure the latest value is used
        onChangeText(localValue);
        if (onSearch) {
            onSearch(localValue);
        }
    };

    // Clean up timeout on unmount
    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    // If this is a navigation bar, we'll use a touchable wrapper
    if (isNavigationBar) {
        return (
            <TouchableOpacity
                style={styles.searchContainer}
                activeOpacity={0.7}
                onPress={onNavigationPress || (() => { })} // Use the navigation press handler
            >
                <SearchIcon width={20} height={20} />
                <TextInput
                    style={styles.searchInput}
                    placeholder={placeholder}
                    placeholderTextColor={COLORS.text.secondary}
                    value={localValue}
                    editable={false} // Make it non-editable
                    pointerEvents="none" // Disable pointer events
                />
            </TouchableOpacity>
        );
    }

    // Regular search bar with input functionality
    return (
        <View style={styles.searchContainer}>
            <SearchIcon width={20} height={20} />
            <TextInput
                ref={ref || inputRef}
                style={styles.searchInput}
                placeholder={placeholder}
                placeholderTextColor={COLORS.text.secondary}
                value={localValue}
                onChangeText={handleTextChange}
                onSubmitEditing={handleSubmit}
                returnKeyType="search"
                onFocus={onFocus}
                {...props}
            />
        </View>
    );
});

const styles = StyleSheet.create({
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: COLORS.white,
        flex: 1,
        padding: SPACING.sm,
        borderRadius: BORDERS.radius.md,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
        marginVertical: SPACING.xs,
    },
    searchInput: {
        flex: 1,
        fontSize: FONTS.size.body,
        color: COLORS.text.primary,
        padding: 0, // Remove default padding on Android
        marginLeft: SPACING.sm,
        ...FONTS.regular,
        height: 25,
    } as TextStyle,
});