import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Image, ActivityIndicator } from 'react-native';
import { COLORS, SPACING, FONTS, BORDERS, SHADOWS } from '@/constants/theme';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store/store';
import { fetchBrands } from '@/store/slices/sellVehicleSlice';
import { MultiSelect } from 'react-native-element-dropdown';
import { TextStyle } from 'react-native/Libraries/StyleSheet/StyleSheetTypes';

interface BrandFilterProps {
  selectedBrands: string[];
  vehicleType: string;
  onChange: (brands: string[]) => void;
}

const BrandFilter: React.FC<BrandFilterProps> = ({ selectedBrands, vehicleType, onChange }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { brands, loading } = useSelector((state: RootState) => state.sellVehicle);
  const [isFocus, setIsFocus] = useState(false);

  useEffect(() => {
    dispatch(fetchBrands(vehicleType as string));
  }, [dispatch, vehicleType]);

  // The brands from sellVehicleSlice are already formatted correctly
  const dropdownBrands = brands.map(brand => ({
    ...brand,
    // Ensure the value field is used for selection
    value: brand.label
  }));

  // Handle multiple brand selection
  const handleBrandSelect = (selectedValues: string[]) => {
    // According to the docs, onChange receives an array of valueField values
    onChange(selectedValues);
  };

  // Custom render item for dropdown with brand image
  const renderItem = (item: any) => {
    return (
      <View style={styles.dropdownItem}>
        {item.image ? (
          <Image source={{ uri: item.image }} style={styles.brandImage} />
        ) : null}
        <Text style={styles.dropdownItemText}>{item.label}</Text>
      </View>
    );
  };

  // Custom selected item rendering
  const renderSelectedItem = (item: any) => {
    return (
      <View style={styles.selectedItem}>
        {item.image ? (
          <Image source={{ uri: item.image }} style={styles.brandImageSmall} />
        ) : null}
        <Text style={styles.selectedItemText} numberOfLines={1}>{item.label}</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Brand</Text>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={COLORS.primary} />
        </View>
      ) : (
        <MultiSelect
          style={[styles.dropdown, isFocus && { borderColor: COLORS.primary }]}
          placeholderStyle={styles.placeholderStyle}
          selectedTextStyle={styles.selectedTextStyle}
          inputSearchStyle={styles.inputSearchStyle}
          iconStyle={styles.iconStyle}
          data={dropdownBrands}
          search
          searchPlaceholder="Search brands..."
          maxHeight={300}
          labelField="label"
          valueField="value"
          placeholder="Select brands"
          value={selectedBrands}
          onFocus={() => setIsFocus(true)}
          onBlur={() => setIsFocus(false)}
          onChange={handleBrandSelect}
          renderItem={renderItem}
          renderSelectedItem={renderSelectedItem}
          activeColor={COLORS.surface.secondary}
          keyboardAvoiding={false}
          mode="default"
          searchField="label"
          alwaysRenderSelectedItem={true}
          showsVerticalScrollIndicator={true}
          dropdownPosition="auto"
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDERS.radius.md,
    ...SHADOWS.xs,
  },
  title: {
    fontWeight: '600',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    marginBottom: SPACING.md,
    letterSpacing: 0.3,
  } as TextStyle,
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 100,
  },
  dropdown: {
    height: 50,
    borderColor: COLORS.gray[300],
    borderWidth: 1,
    borderRadius: BORDERS.radius.sm,
    paddingHorizontal: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  placeholderStyle: {
    fontSize: FONTS.size.caption,
    color: COLORS.text.secondary,
  },
  selectedTextStyle: {
    fontSize: FONTS.size.caption,
    color: COLORS.text.primary,
  },
  inputSearchStyle: {
    borderRadius: BORDERS.radius.sm,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
  },
  dropdownItemText: {
    ...FONTS.regular,
    fontSize: FONTS.size.caption,
    color: COLORS.text.primary,
    marginLeft: SPACING.xs,
  } as TextStyle,
  selectedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.xs,
    paddingVertical: SPACING.xs / 2,
    marginRight: SPACING.xs,
    marginBottom: SPACING.xs,
    backgroundColor: COLORS.primary,
    borderRadius: BORDERS.radius.sm,
  },
  selectedItemText: {
    ...FONTS.regular,
    fontSize: FONTS.size.caption,
    color: COLORS.white,
    marginLeft: SPACING.xs,
    maxWidth: 100,
  } as TextStyle,
  brandImage: {
    width: 24,
    height: 24,
  },
  brandImageSmall: {
    width: 16,
    height: 16,
  },
});

export default BrandFilter;
