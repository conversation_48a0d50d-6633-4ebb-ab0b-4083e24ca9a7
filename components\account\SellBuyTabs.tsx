import React, { useRef, useEffect, useState } from 'react';
import {
    View,
    TouchableOpacity,
    Text,
    Animated,
    StyleSheet,
    LayoutChangeEvent,
    Easing,
} from 'react-native';
import { SPACING } from '@/constants/theme';

interface SellBuyTabsProps {
    activeTab: 'sell' | 'buy';
    onTabChange: (tab: 'sell' | 'buy') => void;
}

const SellBuyTabs: React.FC<SellBuyTabsProps> = ({ activeTab, onTabChange }) => {
    const initialValue = activeTab === 'sell' ? 0 : 1;
    const animValue = useRef(new Animated.Value(initialValue)).current;
    const [containerWidth, setContainerWidth] = useState(0);

    useEffect(() => {
        Animated.timing(animValue, {
            toValue: activeTab === 'sell' ? 0 : 1,
            duration: 400, // Slightly longer for smoothness
            easing: Easing.bezier(0.25, 0.1, 0.25, 1), // Natural cubic bezier easing
            useNativeDriver: true,
        }).start();
    }, [activeTab]);

    const handleLayout = (event: LayoutChangeEvent) => {
        setContainerWidth(event.nativeEvent.layout.width);
    };

    const tabWidth = containerWidth / 2;
    const translateX = animValue.interpolate({
        inputRange: [0, 1],
        outputRange: [0, tabWidth],
    });

    return (
        <View style={styles.container} onLayout={handleLayout}>
            <View style={styles.tabContainer}>
                <Animated.View
                    style={[
                        styles.highlight,
                        {
                            width: tabWidth,
                            transform: [{ translateX }],
                        },
                    ]}
                />

                <TouchableOpacity
                    style={styles.tabButton}
                    activeOpacity={0.8}
                    onPress={() => onTabChange('sell')}
                >
                    <Text style={styles.tabText}>Sell</Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={styles.tabButton}
                    activeOpacity={0.8}
                    onPress={() => onTabChange('buy')}
                >
                    <Text style={styles.tabText}>Buy</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

export default SellBuyTabs;

const styles = StyleSheet.create({
    container: {
        marginHorizontal: SPACING.md,
        marginVertical: SPACING.md,
    },
    tabContainer: {
        flexDirection: 'row',
        backgroundColor: '#0F1D3A',
        borderRadius: 8,
        overflow: 'hidden',
        height: 50,
        position: 'relative',
    },
    highlight: {
        position: 'absolute',
        left: 0,
        top: 0,
        bottom: 0,
        backgroundColor: '#FF5722',
        // borderRadius: 8,
    },
    tabButton: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1,
    },
    tabText: {
        fontSize: 16,
        color: '#FFFFFF',
        fontWeight: 'bold',
    },
});
