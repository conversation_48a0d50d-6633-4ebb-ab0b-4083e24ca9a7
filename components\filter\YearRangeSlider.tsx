import React from 'react';
import RangeSlider from './RangeSlider';

interface YearRangeSliderProps {
  minYear?: number;
  maxYear?: number;
  onChange: (minYear: number, maxYear: number) => void;
  onValueChange?: (minYear: number, maxYear: number) => void;
}

const YearRangeSlider: React.FC<YearRangeSliderProps> = ({
  minYear,
  maxYear,
  onChange,
  onValueChange
}) => {
  // Default year range
  const MIN_YEAR = 1995;
  const MAX_YEAR = 2025;
  const STEP = 1;

  // Format year for display
  const formatYear = (year: number) => {
    return year.toString();
  };

  return (
    <RangeSlider
      title="Year Range"
      minValue={MIN_YEAR}
      maxValue={MAX_YEAR}
      step={STEP}
      initialMinValue={minYear}
      initialMaxValue={maxYear}
      formatValue={formatYear}
      onChange={onChange}
      onValueChange={onValueChange}
    />
  );
};

export default YearRangeSlider;
