import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { ShimmerPlaceholder } from '@/components/common/ShimmerPlaceholder';
import { COLORS, SPACING } from '@/constants/theme';

export const VehicleRecommendationsSkeleton = () => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.recommendationsScroll}
    >
      {[...Array(3)].map((_, index) => (
        <View key={index} style={styles.vehicleCard}>
          {/* Image Skeleton */}
          <ShimmerPlaceholder style={styles.imageSkeleton} />
          
          <View style={styles.detailsContainer}>
            {/* Title Skeleton */}
            <ShimmerPlaceholder style={styles.titleSkeleton} />
            
            {/* Model Skeleton */}
            <ShimmerPlaceholder style={styles.modelSkeleton} />
            
            {/* Price Skeleton */}
            <ShimmerPlaceholder style={styles.priceSkeleton} />
            
            {/* Details Row Skeleton */}
            <View style={styles.detailsRow}>
              <ShimmerPlaceholder style={styles.detailItemSkeleton} />
              <ShimmerPlaceholder style={styles.detailItemSkeleton} />
            </View>
            
            <View style={styles.detailsRow}>
              <ShimmerPlaceholder style={styles.detailItemSkeleton} />
              <ShimmerPlaceholder style={styles.detailItemSkeleton} />
            </View>
            
            {/* Location Skeleton */}
            <ShimmerPlaceholder style={styles.locationSkeleton} />
          </View>
        </View>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  recommendationsScroll: {
    paddingLeft: SPACING.md,
  },
  vehicleCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    marginRight: SPACING.md,
    width: 280,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 3,
    overflow: 'hidden',
  },
  imageSkeleton: {
    width: '100%',
    height: 180,
  },
  detailsContainer: {
    padding: SPACING.md,
  },
  titleSkeleton: {
    height: 20,
    width: '80%',
    borderRadius: 4,
    marginBottom: 8,
  },
  modelSkeleton: {
    height: 16,
    width: '60%',
    borderRadius: 4,
    marginBottom: 12,
  },
  priceSkeleton: {
    height: 24,
    width: '40%',
    borderRadius: 4,
    marginBottom: 16,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailItemSkeleton: {
    height: 14,
    width: '45%',
    borderRadius: 4,
  },
  locationSkeleton: {
    height: 14,
    width: '70%',
    borderRadius: 4,
    marginTop: 8,
  },
});
