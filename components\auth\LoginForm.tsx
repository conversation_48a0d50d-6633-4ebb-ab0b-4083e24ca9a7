import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { ThemedText } from '@/components/common/ThemedText';
import ThemedInput from '@/components/common/ThemedInput';
import ThemedButton from '@/components/common/ThemedButton';
import { COLORS } from '@/constants/theme';

export default function LoginForm({ email, password, setPassword, onLoginSuccess, onForgotPassword, loading }: { email: string, password: string, setPassword: (password: string) => void, onLoginSuccess: () => void, onForgotPassword: () => void, loading: boolean }) {
    return (
        <>
            <ThemedText style={styles.title}>Login</ThemedText>
            <ThemedInput
                value={email}
                editable={false}
                placeholder="Email"
                onChangeText={() => { }}
            />
            <ThemedInput
                value={password}
                onChangeText={setPassword}
                placeholder="Password"
                secureTextEntry
            />
            <TouchableOpacity onPress={onForgotPassword}>
                <ThemedText style={styles.forgotPasswordText}>Forgot Password?</ThemedText>
            </TouchableOpacity>
            <ThemedButton
                style={styles.button}
                onPress={onLoginSuccess}
                disabled={loading}
            >
                {loading ? 'Logging in...' : 'Login'}
            </ThemedButton>
        </>
    );
}

const styles = StyleSheet.create({
    title: {
        fontSize: 24,
        marginBottom: 8,
        textAlign: 'center',
        color: COLORS.primary,
    },
    button: {
        width: '100%',
        marginTop: 8,
    },
    forgotPasswordLink: {
        alignSelf: 'flex-end',
        marginTop: 8,
        backgroundColor: 'transparent',
    },
    forgotPasswordText: {
        textAlign: 'right',
        color: COLORS.primary,
        fontSize: 14,
        textDecorationLine: 'underline',
    },
});