import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, FONTS, SPACING } from '@/constants/theme';
import { useAppDispatch } from '@/store/hooks';
import { clearUserData, deleteAccount } from '@/store/slices/accountSlice';
import { logout } from '@/store/slices/authSlice';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View, TextInput, TextStyle, Alert, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Constants from 'expo-constants';

export default function DeleteAccountScreen() {
    const [deleteText, setDeleteText] = useState('');
    const [loading, setLoading] = useState(false);
    const dispatch = useAppDispatch();
    const router = useRouter();

    const handleDeleteAccount = async () => {
        if (deleteText === 'DELETE') {
            try {
                setLoading(true);
                const result = await dispatch(deleteAccount()).unwrap();
                if (result?.status) {
                    // Clear local data and logout
                    dispatch(clearUserData());
                    await dispatch(logout());
                    Alert.alert(
                        'Success',
                        result.message || 'Your Account Deleted Successfully',
                        [{
                            text: 'OK',
                            onPress: () => router.replace('/auth/register')
                        }]
                    );
                }
            } catch (error: any) {
                console.error('Delete account error details:', {
                    message: error?.message,
                    response: error?.response?.data,
                    status: error?.response?.status,
                    headers: error?.response?.headers
                });
                Alert.alert(
                    'Error',
                    error?.message || 'Failed to delete account. Please try again.'
                );
            } finally {
                setLoading(false);
            }
        }
    };

    return (
        <View style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
                    <Ionicons name="arrow-back" size={24} color={COLORS.white} />
                </TouchableOpacity>
                <ThemedText style={styles.headerTitle}>Delete Account</ThemedText>
                <View style={{ width: 24 }} />
            </View>

            {/* Content */}
            <View style={styles.content}>
                <View style={styles.iconContainer}>
                    <Ionicons name="trash-outline" size={40} color={COLORS.error} />
                </View>

                <ThemedText style={styles.title}>Delete Account</ThemedText>

                <ThemedText style={styles.description}>
                    Are you sure you want to delete your account? This action cannot be undone. All your data will be permanently deleted.
                </ThemedText>

                <View style={styles.inputContainer}>
                    <ThemedText style={styles.inputLabel}>
                        Type "DELETE" to confirm account deletion
                    </ThemedText>
                    <TextInput
                        style={styles.input}
                        value={deleteText}
                        onChangeText={setDeleteText}
                        placeholder="Type DELETE"
                        placeholderTextColor="#999"
                        editable={!loading}
                    />
                </View>

                <View style={styles.buttonContainer}>
                    <TouchableOpacity
                        style={styles.cancelButton}
                        onPress={() => router.back()}
                        disabled={loading}
                    >
                        <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[
                            styles.deleteButton,
                            { opacity: (deleteText === 'DELETE' && !loading) ? 1 : 0.5 }
                        ]}
                        onPress={handleDeleteAccount}
                        disabled={deleteText !== 'DELETE' || loading}
                    >
                        {loading ? (
                            <ActivityIndicator color="white" />
                        ) : (
                            <ThemedText style={styles.deleteButtonText}>Delete Account</ThemedText>
                        )}
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingTop: 16 + Constants.statusBarHeight,
        backgroundColor: '#1E2A47',
        paddingHorizontal: 16,
        paddingVertical: 14,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    backButton: {
        padding: SPACING.xs,
        borderRadius: 20,
    },
    headerTitle: {
        fontSize: 20,
        ...FONTS.medium,
        fontWeight: 'bold',
        color: COLORS.white,
        textAlign: 'center'
    } as TextStyle,
    content: {
        flex: 1,
        padding: SPACING.lg,
        alignItems: 'center',
    },
    iconContainer: {
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: '#FFEBEE',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: SPACING.lg,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: SPACING.md,
        color: COLORS.text.primary,
    },
    description: {
        fontSize: 16,
        color: COLORS.text.secondary,
        textAlign: 'center',
        marginBottom: SPACING.xl,
        lineHeight: 24,
    },
    inputContainer: {
        width: '100%',
        marginBottom: SPACING.xl,
    },
    inputLabel: {
        fontSize: 14,
        color: COLORS.text.secondary,
        marginBottom: SPACING.sm,
    },
    input: {
        width: '100%',
        height: 48,
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        paddingHorizontal: SPACING.md,
        fontSize: 16,
        backgroundColor: 'white',
        color: COLORS.text.primary,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        gap: SPACING.md,
    },
    cancelButton: {
        flex: 1,
        padding: SPACING.md,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
        borderWidth: 1,
        borderColor: '#ddd',
        alignItems: 'center',
    },
    cancelButtonText: {
        color: COLORS.text.primary,
        fontSize: 16,
        fontWeight: '500',
    },
    deleteButton: {
        flex: 1,
        padding: SPACING.md,
        borderRadius: 8,
        backgroundColor: COLORS.error,
        alignItems: 'center',
    },
    deleteButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '500',
    },
});