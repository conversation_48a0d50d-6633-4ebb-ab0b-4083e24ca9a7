import React from 'react';
import { View, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING } from '@/constants/theme';

export const HomeHeader = () => {
    return (
        <View style={styles.header}>
            <Image
                source={require('@/assets/images/logo.png')}
                style={styles.logo}
            />
            <TouchableOpacity style={styles.locationContainer}>
                <Image
                    source={require('@/assets/icon/location-pin-svg')}
                    style={styles.locationIcon}
                />
                <ThemedText style={styles.locationText}>Ahmedabad</ThemedText>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: SPACING.sm,
        backgroundColor: COLORS.white,
    },
    menuIcon: {
        width: 24,
        height: 24,
    },
    logo: {
        width: 40,
        height: 40,
        marginHorizontal: SPACING.sm,
    },
    locationContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        justifyContent: 'flex-end',
    },
    locationIcon: {
        width: 16,
        height: 16,
        marginRight: SPACING.xs,
    },
    locationText: {
        fontSize: 14,
        color: COLORS.text.secondary,
        marginRight: 8,
    },
}); 