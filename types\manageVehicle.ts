export interface ManageVehicle {
  id: number;
  auction_id: number | null;
  user_id: number;
  title: string;
  brand: string;
  model: string;
  variant: string;
  year: string;
  fuel_type: string;
  kilometers_driven: number;
  price: string;
  transmission: string;
  ownership: string;
  location: string;
  description: string;
  images: string[];
  is_verified: boolean;
  status: string;
  primary_image: string;
  type: string;
  auction: boolean;
  user: {
    id: number;
    name: string;
    phone: string;
    email: string;
    profile_picture: string;
  };
}

export interface ManageVehiclesResponse {
  status: boolean;
  message: string;
  type: string;
  data: ManageVehicle[];
}
