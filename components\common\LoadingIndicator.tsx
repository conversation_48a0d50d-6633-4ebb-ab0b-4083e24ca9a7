import React, { useEffect } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS } from '@/constants/theme';

const LoadingIndicator = ({ size = 96, color1 = "#2a2a2a", color2 = COLORS.secondary }) => {
    const spinValue = React.useRef(new Animated.Value(0)).current;
    const scaleValue = React.useRef(new Animated.Value(0.8)).current;

    useEffect(() => {
        const animation = Animated.loop(
            Animated.parallel([
                Animated.timing(spinValue, {
                    toValue: 1,
                    duration: 1500,
                    easing: Easing.linear,
                    useNativeDriver: true,
                }),
                Animated.spring(scaleValue, {
                    toValue: 1,
                    friction: 3,
                    tension: 40,
                    useNativeDriver: true,
                }),
            ])
        );

        animation.start();

        return () => {
            animation.stop();
        };
    }, [spinValue, scaleValue]);

    const spin = spinValue.interpolate({
        inputRange: [0, 1],
        outputRange: ['0deg', '360deg'],
    });

    return (
        <View style={styles.container}>
            <Animated.View style={[styles.gradientContainer, {
                transform: [{ rotate: spin }, { scale: scaleValue }],
                width: size,
                height: size,
            }]}>
                <LinearGradient
                    colors={[color1, color2]}
                    style={styles.gradient}
                    start={[0, 0]}
                    end={[1, 1]}
                >
                    <View style={styles.innerCircle} />
                </LinearGradient>
            </Animated.View>

            {/* Floating particles */}
            {[...Array(3)].map((_, index) => (
                <Animated.View
                    key={index}
                    style={[
                        styles.particle,
                        {
                            backgroundColor: color1,
                            transform: [
                                {
                                    rotate: spinValue.interpolate({
                                        inputRange: [0, 1],
                                        outputRange: ['0deg', `${(index + 1) * 360}deg`]
                                    })
                                }
                            ]
                        }
                    ]}
                />
            ))}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    gradientContainer: {
        borderRadius: 48,
        padding: 8,
        shadowColor: "#2a2a2a",
        shadowOffset: { width: 4, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 8,
    },
    gradient: {
        flex: 1,
        borderRadius: 48,
        justifyContent: 'center',
        alignItems: 'center',
    },
    innerCircle: {
        width: '80%',
        height: '80%',
        borderRadius: 40,
        backgroundColor: '#fff',
        shadowColor: '#fff',
        shadowOffset: { width: -4, height: -4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
    },
    particle: {
        position: 'absolute',
        width: 12,
        height: 12,
        borderRadius: 6,
        shadowColor: "#2a2a2a",
        shadowOffset: { width: 2, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
    },
});

export default LoadingIndicator;