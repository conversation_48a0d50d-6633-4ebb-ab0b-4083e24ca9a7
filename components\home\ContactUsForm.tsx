import React, { useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store/store';
import { submitContactForm, clearContactState } from '@/store/slices/contactSlice';
import { COLORS, SPACING } from '@/constants/theme';
import { ThemedText } from '@/components/common/ThemedText';
import ThemedTextInput from '@/components/common/ThemedTextInput';
import ThemedButton from '@/components/common/ThemedButton';

// Validation schema
const contactSchema = Yup.object().shape({
  name: Yup.string()
    .min(2, 'Name must be at least 2 characters')
    .required('Full name is required'),
  email: Yup.string()
    .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Please enter a valid email address')
    .required('Email is required'),
  number: Yup.string()
    .matches(/^[0-9]{10}$/, 'Please enter a valid 10-digit phone number')
    .required('Phone number is required'),
  message: Yup.string()
    .min(10, 'Message must be at least 10 characters')
    .required('Message is required'),
});

interface ContactFormData {
  name: string;
  email: string;
  number: string;
  message: string;
}

export default function ContactUsForm() {
  const dispatch = useDispatch<AppDispatch>();
  const { loading, error, success } = useSelector((state: RootState) => state.contact);

  const initialValues: ContactFormData = {
    name: '',
    email: '',
    number: '',
    message: '',
  };

  const handleSubmit = (values: ContactFormData, { resetForm }: any) => {
    dispatch(submitContactForm(values));
  };

  useEffect(() => {
    if (success) {
      Alert.alert(
        'Success',
        'Your message has been sent successfully. We will get back to you soon!',
        [
          {
            text: 'OK',
            onPress: () => dispatch(clearContactState()),
          },
        ]
      );
    }
  }, [success, dispatch]);

  useEffect(() => {
    if (error) {
      Alert.alert(
        'Error',
        error,
        [
          {
            text: 'OK',
            onPress: () => dispatch(clearContactState()),
          },
        ]
      );
    }
  }, [error, dispatch]);

  return (
    <View style={styles.container}>
      <Formik
        initialValues={initialValues}
        validationSchema={contactSchema}
        onSubmit={handleSubmit}
      >
        {({ handleChange, handleBlur, handleSubmit, values, errors, touched, resetForm }) => {
          // Reset form when success
          useEffect(() => {
            if (success) {
              resetForm();
            }
          }, [success, resetForm]);

          return (
            <View style={styles.form}>
              <ThemedTextInput
                value={values.name}
                onChangeText={handleChange('name')}
                onBlur={handleBlur('name')}
                placeholder="Full Name"
                error={touched.name && errors.name}
              />

              <ThemedTextInput
                value={values.email}
                onChangeText={handleChange('email')}
                onBlur={handleBlur('email')}
                placeholder="Email Address"
                keyboardType="email-address"
                autoCapitalize="none"
                error={touched.email && errors.email}
              />

              <ThemedTextInput
                value={values.number}
                onChangeText={handleChange('number')}
                onBlur={handleBlur('number')}
                placeholder="Phone Number"
                keyboardType="phone-pad"
                maxLength={10}
                error={touched.number && errors.number}
              />

              <ThemedTextInput
                value={values.message}
                onChangeText={handleChange('message')}
                onBlur={handleBlur('message')}
                placeholder="How can we help you?"
                multiline
                numberOfLines={4}
                style={styles.messageInput}
                error={touched.message && errors.message}
              />

              <ThemedButton
                onPress={handleSubmit}
                disabled={loading}
                style={styles.submitButton}
                textStyle={styles.submitButtonText}
              >
                {loading ? 'Sending...' : '💬 Send Message'}
              </ThemedButton>
            </View>
          );
        }}
      </Formik>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: SPACING.lg,
  },
  form: {
    gap: SPACING.sm,
  },
  messageInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  submitButton: {
    backgroundColor: COLORS.secondary,
    marginTop: SPACING.sm,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
