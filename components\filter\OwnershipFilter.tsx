import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { COLORS, SPACING, FONTS, BORDERS, SHADOWS } from '@/constants/theme';
import { Ionicons } from '@expo/vector-icons';

interface OwnershipFilterProps {
  selectedOwners: string[];
  onChange: (owners: string[]) => void;
}

export const OWNER_OPTIONS = [
  { value: "1st", label: "1st" },
  { value: "2nd", label: "2nd" },
  { value: "3rd", label: "3rd" },
  { value: "4th", label: "4th" },
  { value: "5+", label: "5+" },
];

const OwnershipFilter: React.FC<OwnershipFilterProps> = ({ selectedOwners, onChange }) => {
  const ownerOptions = OWNER_OPTIONS;

  const handleOwnerSelect = (ownerValue: string) => {
    if (selectedOwners.includes(ownerValue)) {
      onChange(selectedOwners.filter(o => o !== ownerValue));
    } else {
      onChange([...selectedOwners, ownerValue]);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Ownership</Text>
      <View style={styles.checkboxContainer}>
        {ownerOptions.map((owner) => (
          <TouchableOpacity
            key={owner.value}
            style={styles.checkboxRow}
            onPress={() => handleOwnerSelect(owner.value)}
          >
            <View style={styles.checkboxWrapper}>
              <View
                style={[
                  styles.checkbox,
                  selectedOwners.includes(owner.value) && styles.checkboxSelected,
                ]}
              >
                {selectedOwners.includes(owner.value) && (
                  <Ionicons name="checkmark" size={16} color={COLORS.white} />
                )}
              </View>
            </View>
            <Text style={styles.checkboxLabel}>{owner.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDERS.radius.md,
    ...SHADOWS.xs,
  },
  title: {
    fontWeight: '600',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    marginBottom: SPACING.md,
    letterSpacing: 0.3,
  },
  checkboxContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: SPACING.md,
    paddingRight: SPACING.sm,
  },
  checkboxWrapper: {
    marginRight: SPACING.sm,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: BORDERS.radius.sm,
    borderWidth: 2,
    borderColor: COLORS.border.primary,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.surface.secondary,
  },
  checkboxSelected: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  checkboxLabel: {
    fontWeight: '400',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    flex: 1,
  },
});

export default OwnershipFilter;
