import React from 'react';
import RangeSlider from './RangeSlider';

interface PriceRangeSliderProps {
  minPrice?: number;
  maxPrice?: number;
  onChange: (minPrice: number, maxPrice: number) => void;
  onValueChange?: (minPrice: number, maxPrice: number) => void;
}

const PriceRangeSlider: React.FC<PriceRangeSliderProps> = ({
  minPrice,
  maxPrice,
  onChange,
  onValueChange
}) => {
  // Default price range
  const MIN_PRICE = 0;
  const MAX_PRICE = 5000000; // 50 lakhs
  const STEP = 10000;

  // Format price for display
  const formatPrice = (price: number) => {
    if (price >= 100000) {
      return `₹${(price / 100000).toFixed(1)} Lakh`;
    } else {
      return `₹${price.toLocaleString('en-IN')}`;
    }
  };

  return (
    <RangeSlider
      title="Price Range"
      minValue={MIN_PRICE}
      maxValue={MAX_PRICE}
      step={STEP}
      initialMinValue={minPrice}
      initialMaxValue={maxPrice}
      formatValue={formatPrice}
      onChange={onChange}
      onValueChange={onValueChange}
    />
  );
};

export default PriceRangeSlider;
