// Sale.js
import React, { useEffect, useCallback } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    Image,
    TextInput,
    StyleSheet,
    Alert,
} from 'react-native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import * as ImagePicker from 'expo-image-picker';
import { Dropdown } from 'react-native-element-dropdown';
import { useDispatch, useSelector } from 'react-redux';
import { useFocusEffect } from 'expo-router';
import { AntDesign } from '@expo/vector-icons';

import { AppDispatch, RootState } from '@/store/store';
import {
    clearModelsAndVariants,
    clearVariants,
    clearCities,
    clearBrands,
    resetSellVehicleState,
    fetchBrands,
    fetchModels,
    fetchVariants,
    fetchStates,
    fetchCities,
    sellVehicle,
} from '@/store/slices/sellVehicleSlice';
import { COLORS } from '@/constants/theme';
import { INSURANCE_OPTIONS } from '@/constants/commercialVehicle';

interface DropdownItem {
    label: string;
    value: string;
    image?: string;
}

const VEHICLE_TYPES = [
    { label: 'Car', value: 'car' },
    { label: 'Scooter', value: 'scooter' },
    { label: 'Bike', value: 'bike' },
    { label: 'Commercial', value: 'commercial' },
];

const FUEL_TYPES = ['CNG', 'Hybrid', 'Diesel', 'Petrol', 'Electric'];
const TRANSMISSION_TYPES = ['Automatic', 'Hybrid', 'Manual'];
const OWNER_OPTIONS = ['1st', '2nd', '3rd', '4th', '5+'];

const validationSchema = Yup.object({
    title: Yup.string().required('Title is required'),
    vehicleType: Yup.string().required('Vehicle type is required'),
    brand: Yup.string().required('Brand is required'),
    brandId: Yup.string().required('Brand ID is required'),
    model: Yup.string().required('Model is required'),
    modelId: Yup.string().required('Model ID is required'),
    variant: Yup.string().when('vehicleType', {
        is: 'car',
        then: (schema) => schema.required('Variant is required for cars'),
        otherwise: (schema) => schema.optional(),
    }),
    year: Yup.number()
        .required('Year is required')
        .min(1900, 'Year cannot be before 1900')
        .max(new Date().getFullYear(), 'Year cannot exceed current year')
        .typeError('Year must be a number'),
    fuelType: Yup.string().required('Fuel type is required'),
    transmission: Yup.string().when('vehicleType', {
        is: (type: any) => ['car', 'commercial'].includes(type),
        then: (schema) => schema.required('Transmission is required'),
        otherwise: (schema) => schema.optional(),
    }),
    kmDriven: Yup.number()
        .required('KM Driven is required')
        .min(0, 'KM Driven must be >= 0')
        .typeError('KM Driven must be a number'),
    owners: Yup.string().required('Number of owners is required'),
    insuranceType: Yup.string().required('Insurance type is required'),
    state: Yup.string().required('State is required'),
    stateId: Yup.string().required('State ID is required'),
    city: Yup.string().required('City is required'),
    cityId: Yup.string().required('City ID is required'),
    description: Yup.string()
        .required('Description is required'),
    photos: Yup.array().min(1, 'At least one photo is required'),
    price: Yup.number()
        .required('Price is required')
        .min(0, 'Price must be >= 0')
        .typeError('Price must be a number'),
});

export default function Sale() {
    const dispatch = useDispatch<AppDispatch>();
    const { brands, models, variants, states, cities, loading } = useSelector(
        (state: RootState) => state.sellVehicle
    );

    // Track current vehicle type to ensure proper brand fetching
    const [currentVehicleType, setCurrentVehicleType] = React.useState('car');

    // Ensure all arrays are valid to prevent iterator errors
    const safeBrands = Array.isArray(brands) ? brands : [];
    const safeModels = Array.isArray(models) ? models : [];
    const safeVariants = Array.isArray(variants) ? variants : [];
    const safeStates = Array.isArray(states) ? states : [];
    const safeCities = Array.isArray(cities) ? cities : [];

    // On mount, fetch brands for default vehicle type and states
    useEffect(() => {
        dispatch(fetchBrands(currentVehicleType));
        dispatch(fetchStates());
    }, [dispatch]);

    // Refetch data when the screen comes into focus to ensure data persistence
    // This handles the case when returning from edit vehicle page
    useFocusEffect(
        useCallback(() => {
            // Fetch brands for the current vehicle type and states when focusing
            // This ensures we maintain the correct brands for the selected vehicle type
            dispatch(fetchBrands(currentVehicleType));
            dispatch(fetchStates());

            // Note: Models and variants will be refetched when user selects brand/model
            // Cities will be refetched when user selects state
        }, [dispatch, currentVehicleType])
    );

    // Helper function to pick images
    const pickImage = async (
        currentPhotos: string[],
        setFieldValue: (field: string, value: any, shouldValidate?: boolean) => void
    ) => {
        try {
            const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (permissionResult.status !== 'granted') {
                Alert.alert('Permission Required', 'Please grant camera roll permissions.');
                return;
            }
            const result = await ImagePicker.launchImageLibraryAsync({
                aspect: [4, 3],
                quality: 0.8,
                allowsMultipleSelection: true,
            });

            if (!result.canceled && result.assets && result.assets.length > 0) {
                const newPhotos = [...currentPhotos];
                result.assets.forEach((asset) => {
                    newPhotos.push(asset.uri);
                });
                setFieldValue('photos', newPhotos, true);
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to pick image. Please try again.');
        }
    };

    const initialValues = {
        title: '',
        vehicleType: 'car',
        brand: '',
        brandId: '',
        model: '',
        modelId: '',
        variant: '',
        year: '',
        fuelType: '',
        transmission: '',
        kmDriven: '',
        owners: '',
        insuranceType: '',
        state: '',
        stateId: '',
        city: '',
        cityId: '',
        description: '',
        photos: [] as string[],
        price: '',
    };

    const renderDropdownItem = (item: DropdownItem) => {
        return (
            <View style={styles.dropdownItem}>
                {item.image && <Image source={{ uri: item.image }} style={styles.brandImage} />}
                <Text style={styles.dropdownItemText}>{item.label}</Text>
            </View>
        );
    };

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.title}>Enter Vehicle Details</Text>
            <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                validateOnChange={true}
                validateOnBlur={true}
                onSubmit={async (values, { resetForm, setSubmitting }) => {

                    try {
                        const formattedData = {
                            type: values.vehicleType,
                            formData: {
                                title: values.title,
                                brand: values.brand,
                                brand_id: values.brandId,
                                model: values.model,
                                model_id: values.modelId,
                                variant: values.variant,
                                year: Number(values.year),
                                fuel_type: values.fuelType,
                                kilometers_driven: Number(values.kmDriven),
                                price: Number(values.price),
                                transmission: values.transmission,
                                ownership: values.owners,
                                insurance_type: values.insuranceType,
                                country: 'India',
                                state: values.state,
                                state_id: values.stateId,
                                city: values.city,
                                city_id: values.cityId,
                                location: `${values.city}, ${values.state}, India`,
                                description: values.description,
                                images: (values.photos || []).map((photo, index) => ({
                                    uri: photo,
                                    type: 'image/jpeg',
                                    name: `photo${index + 1}.jpg`,
                                })),
                            },
                        };

                        await dispatch(sellVehicle(formattedData)).unwrap();
                        resetForm();
                        Alert.alert('Success', 'Vehicle listed successfully!');
                    } catch (error: any) {
                        Alert.alert('Error', error?.message || error?.error || 'Failed to list vehicle. Please try again.');
                    } finally {
                        setSubmitting(false);
                    }
                }}
            >
                {({
                    handleChange,
                    handleBlur,
                    handleSubmit,
                    values,
                    errors,
                    touched,
                    setFieldValue,
                    setFieldTouched,
                    validateForm,
                    isSubmitting,
                }) => {
                    // For Commercial, both brand and model labels become "Select Type"
                    const brandLabel = values.vehicleType === 'commercial' ? 'Select Type' : 'Select Brand';
                    const modelLabel = values.vehicleType === 'commercial' ? 'Select Brand' : 'Select Model';

                    const resetDependentFields = () => {
                        setFieldValue('brand', '', true);
                        setFieldValue('brandId', '', true);
                        setFieldValue('model', '', true);
                        setFieldValue('modelId', '', true);
                        setFieldValue('variant', '', true);
                        setFieldValue('transmission', '', true);
                        dispatch(clearModelsAndVariants());
                    };

                    const handleVehicleTypeChange = (type: string) => {
                        setFieldValue('vehicleType', type, false);
                        setFieldTouched('vehicleType', true, false);
                        setCurrentVehicleType(type); // Update current vehicle type state
                        resetDependentFields();
                        // Clear existing brands before fetching new ones
                        dispatch(clearBrands());
                        dispatch(fetchBrands(type));
                        // Trigger validation after state updates
                        setTimeout(() => validateForm(), 0);
                    };

                    return (
                        <View style={styles.form}>
                            {/* Vehicle Type */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    Vehicle Type<Text style={styles.required}> *</Text>
                                </Text>
                                <View style={styles.chipContainer}>
                                    {(Array.isArray(VEHICLE_TYPES) ? VEHICLE_TYPES : []).map((type) => {
                                        const selected = values.vehicleType === type.value;
                                        return (
                                            <TouchableOpacity
                                                key={type.value}
                                                style={[styles.chip, selected && styles.chipSelected]}
                                                onPress={() => handleVehicleTypeChange(type.value)}
                                            >
                                                <Text style={[styles.chipText, selected && styles.chipTextSelected]}>
                                                    {type.label}
                                                </Text>
                                            </TouchableOpacity>
                                        );
                                    })}
                                </View>
                                {touched.vehicleType && errors.vehicleType && (
                                    <Text style={styles.error}>{errors.vehicleType}</Text>
                                )}
                            </View>

                            {/* Brand Selection */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    {brandLabel}<Text style={styles.required}> *</Text>
                                </Text>
                                <Dropdown
                                    style={[styles.dropdown, touched.brand && errors.brand && styles.dropdownError]}
                                    placeholderStyle={styles.placeholderStyle}
                                    selectedTextStyle={styles.selectedTextStyle}
                                    data={safeBrands}
                                    search
                                    searchPlaceholder="Search..."
                                    labelField="label"
                                    valueField="label"
                                    placeholder="Select brand name"
                                    value={values.brand}
                                    renderItem={renderDropdownItem}
                                    onChange={(item) => {
                                        setFieldValue('brand', item.label, false);
                                        setFieldValue('brandId', item.value, false);
                                        setFieldTouched('brand', true, false);
                                        setFieldTouched('brandId', true, false);
                                        // Reset model and variant when brand changes
                                        setFieldValue('model', '', true);
                                        setFieldValue('modelId', '', true);
                                        setFieldValue('variant', '', true);
                                        dispatch(clearModelsAndVariants());
                                        dispatch(fetchModels({ type: values.vehicleType, brandId: item.value }));
                                        // Validate after state updates
                                        setTimeout(() => validateForm(), 0);
                                    }}
                                    renderLeftIcon={() => (
                                        <AntDesign style={styles.icon} color="black" name="Safety" size={20} />
                                    )}
                                />
                                {values.vehicleType === 'commercial' ? touched.brand && errors.brand && <Text style={styles.error}>Type is required</Text> : touched.brand && errors.brand && <Text style={styles.error}>{errors.brand}</Text>}
                            </View>

                            {/* Model Selection */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    {modelLabel}<Text style={styles.required}> *</Text>
                                </Text>
                                <Dropdown
                                    style={[styles.dropdown, touched.model && errors.model && styles.dropdownError]}
                                    placeholderStyle={styles.placeholderStyle}
                                    selectedTextStyle={styles.selectedTextStyle}
                                    data={safeModels}
                                    search
                                    searchPlaceholder="Search..."
                                    labelField="label"
                                    valueField="label"
                                    placeholder="Select model name"
                                    value={values.model}
                                    renderItem={renderDropdownItem}
                                    onChange={(item) => {
                                        setFieldValue('model', item.label, false);
                                        setFieldValue('modelId', item.value, false);
                                        setFieldTouched('model', true, false);
                                        setFieldTouched('modelId', true, false);
                                        // Reset variant when model changes
                                        setFieldValue('variant', '', true);
                                        if (values.vehicleType === 'car') {
                                            dispatch(clearVariants());
                                            dispatch(fetchVariants(item.value));
                                        }
                                        // Validate after state updates
                                        setTimeout(() => validateForm(), 0);
                                    }}
                                    renderLeftIcon={() => (
                                        <AntDesign style={styles.icon} color="black" name="Safety" size={20} />
                                    )}
                                />
                                {values.vehicleType === 'commercial' ? touched.model && errors.model && <Text style={styles.error}>Brand is required</Text> : touched.model && errors.model && <Text style={styles.error}>{errors.model}</Text>}
                            </View>

                            {/* Variant (only for Car) */}
                            {values.vehicleType === 'car' && (
                                <View style={styles.fieldContainer}>
                                    <Text style={styles.label}>
                                        Select Variant<Text style={styles.required}> *</Text>
                                    </Text>
                                    <Dropdown
                                        style={[
                                            styles.dropdown,
                                            touched.variant && errors.variant && styles.dropdownError,
                                        ]}
                                        placeholderStyle={styles.placeholderStyle}
                                        selectedTextStyle={styles.selectedTextStyle}
                                        data={safeVariants}
                                        search
                                        searchPlaceholder="Search..."
                                        labelField="label"
                                        valueField="label"
                                        placeholder="Select variant"
                                        value={values.variant}
                                        renderItem={renderDropdownItem}
                                        onChange={(item) => {
                                            setFieldValue("variant", item.label, false);
                                            setFieldTouched("variant", true, false);
                                            // Validate after state updates
                                            setTimeout(() => validateForm(), 0);
                                        }}
                                        renderLeftIcon={() => (
                                            <AntDesign style={styles.icon} color="black" name="Safety" size={20} />
                                        )}
                                        // This prop will render when the data array is empty
                                        flatListProps={{
                                            ListEmptyComponent: (
                                                <View style={{
                                                    padding: 10,
                                                    alignItems: 'center',
                                                }}>
                                                    <Text style={{
                                                        color: 'gray',
                                                        fontSize: 16,
                                                    }}>No data found</Text>
                                                </View>
                                            ),
                                        }}
                                    />

                                    {touched.variant && errors.variant && <Text style={styles.error}>{errors.variant}</Text>}
                                </View>
                            )}

                            {/* Year */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    Select Year<Text style={styles.required}> *</Text>
                                </Text>
                                <TextInput
                                    style={[styles.input, touched.year && errors.year && styles.inputError]}
                                    onChangeText={(text) => {
                                        setFieldValue('year', text, true);
                                    }}
                                    onBlur={handleBlur('year')}
                                    value={values.year}
                                    keyboardType="numeric"
                                    placeholder="e.g. 2022"
                                />
                                {touched.year && errors.year && <Text style={styles.error}>{errors.year}</Text>}
                            </View>

                            {/* Fuel Type */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    Select Fuel<Text style={styles.required}> *</Text>
                                </Text>
                                <View style={styles.chipContainer}>
                                    {(Array.isArray(FUEL_TYPES) ? FUEL_TYPES : []).map((fuel) => {
                                        const selected = values.fuelType === fuel;
                                        return (
                                            <TouchableOpacity
                                                key={fuel}
                                                style={[styles.chip, selected && styles.chipSelected]}
                                                onPress={() => {
                                                    setFieldValue('fuelType', fuel, false);
                                                    setFieldTouched('fuelType', true, false);
                                                    // Validate after state updates
                                                    setTimeout(() => validateForm(), 0);
                                                }}
                                            >
                                                <Text style={[styles.chipText, selected && styles.chipTextSelected]}>
                                                    {fuel}
                                                </Text>
                                            </TouchableOpacity>
                                        );
                                    })}
                                </View>
                                {touched.fuelType && errors.fuelType && <Text style={styles.error}>{errors.fuelType}</Text>}
                            </View>

                            {/* Transmission (for Car and Commercial) */}
                            {['car', 'commercial'].includes(values.vehicleType) && (
                                <View style={styles.fieldContainer}>
                                    <Text style={styles.label}>
                                        Select Transmission<Text style={styles.required}> *</Text>
                                    </Text>
                                    <View style={styles.chipContainer}>
                                        {(Array.isArray(TRANSMISSION_TYPES) ? TRANSMISSION_TYPES : []).map((trans) => {
                                            const selected = values.transmission === trans;
                                            return (
                                                <TouchableOpacity
                                                    key={trans}
                                                    style={[styles.chip, selected && styles.chipSelected]}
                                                    onPress={() => {
                                                        setFieldValue('transmission', trans, false);
                                                        setFieldTouched('transmission', true, false);
                                                        // Validate after state updates
                                                        setTimeout(() => validateForm(), 0);
                                                    }}
                                                >
                                                    <Text style={[styles.chipText, selected && styles.chipTextSelected]}>
                                                        {trans}
                                                    </Text>
                                                </TouchableOpacity>
                                            );
                                        })}
                                    </View>
                                    {touched.transmission && errors.transmission && (
                                        <Text style={styles.error}>{errors.transmission}</Text>
                                    )}
                                </View>
                            )}

                            {/* KM Driven */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    KM Driven<Text style={styles.required}> *</Text>
                                </Text>
                                <TextInput
                                    style={[styles.input, touched.kmDriven && errors.kmDriven && styles.inputError]}
                                    onChangeText={(text) => {
                                        setFieldValue('kmDriven', text, true);
                                    }}
                                    onBlur={handleBlur('kmDriven')}
                                    value={values.kmDriven}
                                    keyboardType="numeric"
                                    placeholder="Enter KM"
                                />
                                {touched.kmDriven && errors.kmDriven && <Text style={styles.error}>{errors.kmDriven}</Text>}
                            </View>

                            {/* Number of Owners */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    No. of Owners<Text style={styles.required}> *</Text>
                                </Text>
                                <View style={styles.chipContainer}>
                                    {(Array.isArray(OWNER_OPTIONS) ? OWNER_OPTIONS : []).map((owner) => {
                                        const selected = values.owners === owner;
                                        return (
                                            <TouchableOpacity
                                                key={owner}
                                                style={[styles.chip, selected && styles.chipSelected]}
                                                onPress={() => {
                                                    setFieldValue('owners', owner, false);
                                                    setFieldTouched('owners', true, false);
                                                    // Validate after state updates
                                                    setTimeout(() => validateForm(), 0);
                                                }}
                                            >
                                                <Text style={[styles.chipText, selected && styles.chipTextSelected]}>
                                                    {owner}
                                                </Text>
                                            </TouchableOpacity>
                                        );
                                    })}
                                </View>
                                {touched.owners && errors.owners && <Text style={styles.error}>{errors.owners}</Text>}
                            </View>

                            {/* Insurance Type */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    Insurance Type<Text style={styles.required}> *</Text>
                                </Text>
                                <View style={styles.chipContainer}>
                                    {INSURANCE_OPTIONS.map((insurance) => {
                                        const selected = values.insuranceType === insurance.key;
                                        return (
                                            <TouchableOpacity
                                                key={insurance.key}
                                                style={[styles.chip, selected && styles.chipSelected]}
                                                onPress={() => {
                                                    setFieldValue('insuranceType', insurance.key, false);
                                                    setFieldTouched('insuranceType', true, false);
                                                    // Validate after state updates
                                                    setTimeout(() => validateForm(), 0);
                                                }}
                                            >
                                                <Text style={[styles.chipText, selected && styles.chipTextSelected]}>
                                                    {insurance.name}
                                                </Text>
                                            </TouchableOpacity>
                                        );
                                    })}
                                </View>
                                {touched.insuranceType && errors.insuranceType && <Text style={styles.error}>{errors.insuranceType}</Text>}
                            </View>

                            {/* State Selection */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    Select State<Text style={styles.required}> *</Text>
                                </Text>
                                <Dropdown
                                    style={[styles.dropdown, touched.state && errors.state && styles.dropdownError]}
                                    placeholderStyle={styles.placeholderStyle}
                                    selectedTextStyle={styles.selectedTextStyle}
                                    data={safeStates}
                                    search
                                    searchPlaceholder="Search..."
                                    labelField="label"
                                    valueField="label"
                                    placeholder="Select state"
                                    value={values.state}
                                    renderItem={renderDropdownItem}
                                    onChange={(item) => {
                                        setFieldValue('state', item.label, false);
                                        setFieldValue('stateId', item.value, false);
                                        setFieldTouched('state', true, false);
                                        setFieldTouched('stateId', true, false);
                                        // Reset city when state changes
                                        setFieldValue('city', '', true);
                                        setFieldValue('cityId', '', true);
                                        dispatch(clearCities());
                                        dispatch(fetchCities(item.value));
                                        // Validate after state updates
                                        setTimeout(() => validateForm(), 0);
                                    }}
                                    renderLeftIcon={() => (
                                        <AntDesign style={styles.icon} color="black" name="enviromento" size={20} />
                                    )}
                                />
                                {touched.state && errors.state && <Text style={styles.error}>{errors.state}</Text>}
                            </View>

                            {/* City Selection */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    Select City<Text style={styles.required}> *</Text>
                                </Text>
                                <Dropdown
                                    style={[styles.dropdown, touched.city && errors.city && styles.dropdownError]}
                                    placeholderStyle={styles.placeholderStyle}
                                    selectedTextStyle={styles.selectedTextStyle}
                                    data={safeCities}
                                    search
                                    searchPlaceholder="Search..."
                                    labelField="label"
                                    valueField="label"
                                    placeholder={values.stateId ? "Select city" : "Select state first"}
                                    value={values.city}
                                    renderItem={renderDropdownItem}
                                    disable={!values.stateId}
                                    onChange={(item) => {
                                        setFieldValue('city', item.label, false);
                                        setFieldValue('cityId', item.value, false);
                                        setFieldTouched('city', true, false);
                                        setFieldTouched('cityId', true, false);
                                        // Validate after state updates
                                        setTimeout(() => validateForm(), 0);
                                    }}
                                    renderLeftIcon={() => (
                                        <AntDesign style={styles.icon} color="black" name="enviromento" size={20} />
                                    )}
                                />
                                {touched.city && errors.city && <Text style={styles.error}>{errors.city}</Text>}
                            </View>

                            {/* Title */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    Title<Text style={styles.required}> *</Text>
                                </Text>
                                <TextInput
                                    style={[styles.input, touched.title && errors.title && styles.inputError]}
                                    onChangeText={handleChange('title')}
                                    onBlur={handleBlur('title')}
                                    value={values.title}
                                    placeholder="Enter a title for your listing"
                                />
                                {touched.title && errors.title && <Text style={styles.error}>{errors.title}</Text>}
                            </View>

                            {/* Description */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    Add Description<Text style={styles.required}> *</Text>
                                </Text>
                                <TextInput
                                    style={[
                                        styles.input,
                                        styles.textArea,
                                        touched.description && errors.description && styles.inputError,
                                    ]}
                                    onChangeText={handleChange('description')}
                                    onBlur={handleBlur('description')}
                                    value={values.description}
                                    multiline
                                    numberOfLines={4}
                                    placeholder="Mention the key features of your item (e.g. brand, model, age, type)"
                                />
                                {touched.description && errors.description && (
                                    <Text style={styles.error}>{errors.description}</Text>
                                )}
                            </View>

                            {/* Photos */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    Add Photos<Text style={styles.required}> *</Text>
                                </Text>
                                <View style={styles.photoGrid}>
                                    {(values.photos || []).map((uri, index) => (
                                        <View key={index} style={styles.photoContainer}>
                                            <Image source={{ uri }} style={styles.selectedPhoto} />
                                            {/* Cover Image Label */}
                                            {index === 0 && (
                                                <View style={styles.coverLabel}>
                                                    <Text style={styles.coverLabelText}>Cover</Text>
                                                </View>
                                            )}
                                            {/* Remove Icon */}
                                            <TouchableOpacity
                                                style={styles.removeIcon}
                                                onPress={() => {
                                                    const newPhotos = (values.photos || []).filter((_, i) => i !== index);
                                                    setFieldValue('photos', newPhotos, true);
                                                }}
                                            >
                                                <AntDesign name="closecircle" size={20} color="red" />
                                            </TouchableOpacity>
                                        </View>
                                    ))}
                                    <TouchableOpacity
                                        style={styles.photoButton}
                                        onPress={() => pickImage(values.photos, setFieldValue)}
                                    >
                                        <AntDesign name="plus" size={24} color={COLORS.primary} />
                                    </TouchableOpacity>
                                </View>
                                {touched.photos && errors.photos && (
                                    <Text style={styles.error}>{errors.photos}</Text>
                                )}
                            </View>

                            {/* Price */}
                            <View style={styles.fieldContainer}>
                                <Text style={styles.label}>
                                    Set Price<Text style={styles.required}> *</Text>
                                </Text>
                                <View style={styles.priceContainer}>
                                    <Text style={styles.currencySymbol}>₹</Text>
                                    <TextInput
                                        style={[styles.priceInput, touched.price && errors.price && styles.inputError]}
                                        onChangeText={(text) => {
                                            setFieldValue('price', text, true);
                                        }}
                                        onBlur={handleBlur('price')}
                                        value={values.price}
                                        keyboardType="numeric"
                                        placeholder="Enter Price"
                                    />
                                </View>
                                {touched.price && errors.price && <Text style={styles.error}>{errors.price}</Text>}
                            </View>

                            {/* Submit Button */}
                            <TouchableOpacity
                                style={[styles.submitBtn, (loading || isSubmitting) && styles.submitBtnDisabled]}
                                onPress={() => {
                                    validateForm().then(errors => {
                                        // If there are validation errors, mark all fields as touched to show errors
                                        if (errors && Object.keys(errors).length > 0) {
                                            Object.keys(values || {}).forEach(fieldName => {
                                                setFieldTouched(fieldName, true, false);
                                            });
                                            setTimeout(() => validateForm(), 0);
                                        }
                                        handleSubmit();
                                    });
                                }}
                                disabled={loading || isSubmitting}
                            >
                                <Text style={styles.submitBtnText}>
                                    {loading || isSubmitting ? 'Submitting...' : 'Sell Vehicle'}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    );
                }}
            </Formik>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F9F9F9',
        paddingHorizontal: 16,
        paddingTop: 20,
    },
    title: {
        fontSize: 22,
        fontWeight: '700',
        marginBottom: 20,
        color: '#333',
    },
    form: {
        paddingBottom: 40,
    },
    fieldContainer: {
        marginBottom: 16,
    },
    label: {
        fontSize: 15,
        fontWeight: '600',
        color: '#555',
        marginBottom: 8,
    },
    required: {
        color: 'red',
    },
    dropdown: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 12,
        backgroundColor: '#fff',
        height: 50,
        justifyContent: 'center',
    },
    dropdownError: {
        borderColor: 'red',
    },
    placeholderStyle: {
        color: '#999',
        fontSize: 14,
    },
    selectedTextStyle: {
        fontSize: 14,
        color: '#333',
    },
    input: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        fontSize: 14,
        backgroundColor: '#fff',
    },
    inputError: {
        borderColor: 'red',
    },
    textArea: {
        height: 100,
        textAlignVertical: 'top',
    },
    chipContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
    },
    chip: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        backgroundColor: COLORS.white,
        borderWidth: 0.2
    },
    chipSelected: {
        backgroundColor: '#F9E8DB',
        borderColor: '#FC6316'
    },
    chipText: {
        color: COLORS.black,
        fontSize: 14,
    },
    chipTextSelected: {
        color: COLORS.black,
    },
    photoGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 12,
        marginTop: 8,
        alignItems: 'center',
    },
    photoButton: {
        width: 100,
        height: 100,
        borderWidth: 2,
        borderStyle: 'dashed',
        borderColor: '#ddd',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    photoButtonText: {
        fontSize: 24,
        color: '#999',
    },
    selectedPhoto: {
        width: 100,
        height: 100,
        borderRadius: 8,
    },
    priceContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    currencySymbol: {
        fontSize: 18,
        marginRight: 6,
    },
    priceInput: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        fontSize: 14,
        backgroundColor: '#fff',
    },
    submitBtn: {
        backgroundColor: COLORS.primary,
        paddingVertical: 14,
        borderRadius: 8,
        marginTop: 16,
        alignItems: 'center',
    },
    submitBtnText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
    submitBtnDisabled: {
        backgroundColor: '#ccc',
    },
    error: {
        color: 'red',
        marginTop: 4,
        fontSize: 12,
    },
    dropdownItem: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 10,
    },
    brandImage: {
        width: 30,
        height: 30,
        marginRight: 10,
        resizeMode: 'contain',
    },
    dropdownItemText: {
        fontSize: 14,
        color: COLORS.primary,
    },
    icon: {
        marginRight: 5,
    },
    photoContainer: {
        position: 'relative',
        width: 100,
        height: 100,
    },
    coverLabel: {
        position: 'absolute',
        bottom: 4,
        left: 4,
        backgroundColor: 'rgba(0,0,0,0.7)',
        paddingHorizontal: 6,
        paddingVertical: 2,
        borderRadius: 4,
    },
    coverLabelText: {
        color: 'white',
        fontSize: 10,
        fontWeight: 'bold',
    },
    removeIcon: {
        position: 'absolute',
        top: -8,
        right: -8,
        backgroundColor: 'white',
        borderRadius: 10,
    },
});