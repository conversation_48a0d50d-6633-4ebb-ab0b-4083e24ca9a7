import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, SPACING, FONTS, BORDERS, SHADOWS } from '@/constants/theme';
import { Slider } from '@miblanchard/react-native-slider';

interface PriceRangeFilterProps {
  minPrice?: number;
  maxPrice?: number;
  onChange: (minPrice: number, maxPrice: number) => void;
}

const PriceRangeFilter: React.FC<PriceRangeFilterProps> = ({
  minPrice: initialMinPrice,
  maxPrice: initialMaxPrice,
  onChange
}) => {
  // Default price range
  const MIN_PRICE = 0;
  const MAX_PRICE = 5000000; // 50 lakhs

  const [minPrice, setMinPrice] = useState(initialMinPrice || MIN_PRICE);
  const [maxPrice, setMaxPrice] = useState(initialMaxPrice || MAX_PRICE);

  // Update local state when props change
  useEffect(() => {
    if (initialMinPrice !== undefined) setMinPrice(initialMinPrice);
    if (initialMaxPrice !== undefined) setMaxPrice(initialMaxPrice);
  }, [initialMinPrice, initialMaxPrice]);

  // Format price for display
  const formatPrice = (price: number) => {
    if (price >= 100000) {
      return `₹${(price / 100000).toFixed(1)} Lakh`;
    } else {
      return `₹${price.toLocaleString('en-IN')}`;
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Price Range</Text>

      <View style={styles.rangeLabels}>
        <Text style={styles.rangeLabel}>{formatPrice(minPrice)}</Text>
        <Text style={styles.rangeLabel}>{formatPrice(maxPrice)}</Text>
      </View>

      <View style={styles.slider}>
        <Slider
          minimumValue={MIN_PRICE}
          maximumValue={MAX_PRICE}
          step={10000}
          value={minPrice}
          onValueChange={(value) => setMinPrice(Array.isArray(value) ? value[0] : value)}
          onSlidingComplete={(value) => onChange(Array.isArray(value) ? value[0] : value, maxPrice)}
          minimumTrackTintColor={COLORS.primary}
          maximumTrackTintColor={COLORS.gray[300]}
          thumbTintColor={COLORS.primary}
          trackClickable={true}
        />
      </View>

      <View style={styles.slider}>
        <Slider
          minimumValue={MIN_PRICE}
          maximumValue={MAX_PRICE}
          step={10000}
          value={maxPrice}
          onValueChange={(value) => setMaxPrice(Array.isArray(value) ? value[0] : value)}
          onSlidingComplete={(value) => onChange(minPrice, Array.isArray(value) ? value[0] : value)}
          minimumTrackTintColor={COLORS.primary}
          maximumTrackTintColor={COLORS.gray[300]}
          thumbTintColor={COLORS.primary}
          trackClickable={true}
        />
      </View>

      <View style={styles.rangeLabels}>
        <Text style={styles.rangeHint}>Min</Text>
        <Text style={styles.rangeHint}>Max</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.xl,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: BORDERS.radius.md,
    ...SHADOWS.xs,
  },
  title: {
    fontWeight: '600',
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    marginBottom: SPACING.md,
    letterSpacing: 0.3,
  },
  rangeLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs,
  },
  rangeLabel: {
    fontWeight: '500',
    fontSize: FONTS.size.caption,
    color: COLORS.primary,
  },
  rangeHint: {
    fontWeight: '400',
    fontSize: FONTS.size.small,
    color: COLORS.text.secondary,
  },
  slider: {
    width: '100%',
    height: 40,
    marginVertical: SPACING.xs,
  },
});

export default PriceRangeFilter;
