import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

// Types
interface ContactFormData {
  name: string;
  email: string;
  number: string;
  message: string;
}

interface NewsletterFormData {
  email: string;
}

interface ContactState {
  loading: boolean;
  error: string | null;
  success: boolean;
  newsletterLoading: boolean;
  newsletterError: string | null;
  newsletterSuccess: boolean;
}

const initialState: ContactState = {
  loading: false,
  error: null,
  success: false,
  newsletterLoading: false,
  newsletterError: null,
  newsletterSuccess: false,
};

// Async thunks
export const submitContactForm = createAsyncThunk(
  'contact/submitContactForm',
  async (formData: ContactFormData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/contact/us`, formData);
      
      if (response.data.status) {
        return response.data;
      }
      
      return rejectWithValue(response.data.message || 'Failed to send message');
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to send message'
      );
    }
  }
);

export const subscribeNewsletter = createAsyncThunk(
  'contact/subscribeNewsletter',
  async (formData: NewsletterFormData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/stay/update`, formData);
      
      if (response.data.status) {
        return response.data;
      }
      
      return rejectWithValue(response.data.message || 'Failed to subscribe');
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to subscribe'
      );
    }
  }
);

// Slice
const contactSlice = createSlice({
  name: 'contact',
  initialState,
  reducers: {
    clearContactState: (state) => {
      state.loading = false;
      state.error = null;
      state.success = false;
    },
    clearNewsletterState: (state) => {
      state.newsletterLoading = false;
      state.newsletterError = null;
      state.newsletterSuccess = false;
    },
  },
  extraReducers: (builder) => {
    // Contact form
    builder
      .addCase(submitContactForm.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(submitContactForm.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
        state.error = null;
      })
      .addCase(submitContactForm.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.success = false;
      })
      // Newsletter subscription
      .addCase(subscribeNewsletter.pending, (state) => {
        state.newsletterLoading = true;
        state.newsletterError = null;
        state.newsletterSuccess = false;
      })
      .addCase(subscribeNewsletter.fulfilled, (state) => {
        state.newsletterLoading = false;
        state.newsletterSuccess = true;
        state.newsletterError = null;
      })
      .addCase(subscribeNewsletter.rejected, (state, action) => {
        state.newsletterLoading = false;
        state.newsletterError = action.payload as string;
        state.newsletterSuccess = false;
      });
  },
});

export const { clearContactState, clearNewsletterState } = contactSlice.actions;
export default contactSlice.reducer;
