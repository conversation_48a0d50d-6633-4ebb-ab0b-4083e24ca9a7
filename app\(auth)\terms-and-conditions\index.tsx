import React from 'react';
import {
    View,
    Text,
    ScrollView,
    StyleSheet,
    StatusBar,
    SafeAreaView,
    TouchableOpacity,
    TextStyle
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ThemedText } from '@/components/common/ThemedText';
import { router } from 'expo-router';
import { COLORS, FONTS, SPACING } from '@/constants/theme';
import { Ionicons } from '@expo/vector-icons';
import Constants from 'expo-constants';


// Type for navigation prop (adjust based on your navigation structure)
type RootStackParamList = {
    Home: undefined;
    TermsAndConditions: undefined;
};

type TermsAndConditionsScreenProps = NativeStackScreenProps<
    RootStackParamList,
    'TermsAndConditions'
>;

export default function TermsAndConditions({ navigation }: TermsAndConditionsScreenProps) {
    return (
        <SafeAreaView style={styles.container}>
            <StatusBar barStyle="dark-content" />

            {/* Header */}
            <View style={styles.header}>
                <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
                    <Ionicons name="arrow-back" size={24} color={COLORS.white} />
                </TouchableOpacity>
                <ThemedText style={styles.headerTitle}>Terms and Conditions</ThemedText>
                <View style={{ width: 24 }} />
            </View>

            <ScrollView
                contentContainerStyle={styles.scrollViewContent}
                showsVerticalScrollIndicator={false}
            >
                <View style={styles.content}>
                    <Text style={styles.heading}>Terms and Conditions</Text>
                    <Text style={styles.updatedDate}>Effective Date: April 25, 2025</Text>

                    <Text style={styles.paragraph}>
                        Welcome to <Text style={styles.bold}>2ndCar.in</Text> — India's trusted online platform for connecting vehicle buyers and sellers.
                        <Text style={styles.bold}> 2ndCar.in</Text> is owned and operated by <Text style={styles.bold}>2nd Car</Text>.
                        Please read these Terms and Conditions ("Terms") carefully before using our site.
                    </Text>

                    <Text style={styles.sectionHeading}>1. Services</Text>
                    <Text style={styles.paragraph}>
                        • 2ndCar.in is a platform that connects vehicle sellers with buyers.
                    </Text>
                    <Text style={styles.paragraph}>
                        • We do not involve ourselves in any part of the sale or purchase transaction.
                    </Text>

                    <Text style={styles.sectionHeading}>2. Seller Packages</Text>
                    <Text style={styles.paragraph}>
                        • Sellers must purchase a package to list their vehicles.
                    </Text>
                    <Text style={styles.paragraph}>
                        • Package benefits (such as higher ranking) are subject to the plan purchased.
                    </Text>

                    <Text style={styles.sectionHeading}>3. Buyer and Seller Responsibility</Text>
                    <Text style={styles.paragraph}>
                        • Buyers and sellers are solely responsible for their transactions.
                    </Text>
                    <Text style={styles.paragraph}>
                        • We are not liable for any payment, delivery, or dispute between buyers and sellers.
                    </Text>

                    <Text style={styles.sectionHeading}>4. No Refund or Return</Text>
                    <Text style={styles.paragraph}>
                        Once a package is purchased, it cannot be canceled or refunded.
                    </Text>

                    <Text style={styles.sectionHeading}>5. Liability Disclaimer</Text>
                    <Text style={styles.paragraph}>
                        We are not responsible for any loss or damages resulting from use of our site.
                    </Text>

                    <Text style={styles.sectionHeading}>6. Changes to Terms</Text>
                    <Text style={styles.paragraph}>
                        We reserve the right to modify these Terms at any time. Updates will be posted on the site.
                    </Text>

                    <Text style={styles.sectionHeading}>7. Acceptance</Text>
                    <Text style={styles.paragraph}>
                        By using 2ndCar.in, you agree to these Terms and Conditions.
                    </Text>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingTop: 16 + Constants.statusBarHeight,
        backgroundColor: '#1E2A47',
        paddingHorizontal: 16,
        paddingVertical: 14,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    backButton: {
        padding: SPACING.xs,
        borderRadius: 20,
    },
    headerTitle: {
        fontSize: 20,
        ...FONTS.medium,
        fontWeight: 'bold',
        color: COLORS.white,
    } as TextStyle,
    scrollViewContent: {
        flexGrow: 1,
    },
    content: {
        padding: 20,
    },
    heading: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 10,
    },
    sectionHeading: {
        fontSize: 18,
        fontWeight: 'bold',
        marginTop: 15,
        marginBottom: 10,
    },
    updatedDate: {
        fontSize: 14,
        color: '#666',
        marginBottom: 15,
    },
    paragraph: {
        fontSize: 16,
        marginBottom: 10,
        lineHeight: 22,
    },
    bold: {
        fontWeight: 'bold',
    },
});