# Contact Us & Stay Updated Implementation

## Overview
This implementation adds Contact Us and Stay Updated (Newsletter) functionality to the home page with proper API integration, form validation, and user feedback.

## API Endpoints

### Contact Us
- **Endpoint**: `/contact/us`
- **Method**: POST
- **Body**:
```json
{
  "name": "<PERSON><PERSON><PERSON>",
  "email": "<EMAIL>",
  "number": "7069",
  "message": "I am interested in the Yamaha FZ listed on your platform. Please share more details."
}
```
- **Success Response**:
```json
{
  "status": true,
  "message": "Your message has been sent successfully."
}
```

### Newsletter Subscription
- **Endpoint**: `/stay/update`
- **Method**: POST
- **Body**:
```json
{
  "email": "<EMAIL>"
}
```
- **Success Response**:
```json
{
  "status": true,
  "message": "Your message has been sent successfully."
}
```

## Implementation Details

### Files Created/Modified

1. **store/slices/contactSlice.ts** - Redux slice for API calls
2. **components/home/<USER>
3. **components/home/<USER>
4. **store/store.ts** - Added contact reducer
5. **app/(auth)/(tabs)/index.tsx** - Added sections to home page

### Components Used
- **ThemedTextInput** - Proper input component with borders and validation
- **ThemedButton** - Consistent button styling
- **Formik + Yup** - Form validation and management

### Features

#### Contact Us Section
- **Form Fields**: Name, Email, Phone Number, Message
- **Validation**:
  - Name: minimum 2 characters
  - Email: valid email format
  - Phone: 10-digit number
  - Message: minimum 10 characters
- **UI**: Clean white card with contact information and form
- **Feedback**: Success/error alerts with automatic form reset

#### Stay Updated Section
- **Form Fields**: Email only
- **Validation**: Valid email format required
- **UI**: Blue primary color background with white input
- **Feedback**: Success/error alerts with automatic form reset

#### Contact Information Display
- **Phone**: 7840975555
- **Email**: <EMAIL>
- **Address**: 211, PREM PRAKASH TOWER, Godhra, Gujarat, India

### Technical Implementation

#### Redux State Management
- Separate loading states for contact and newsletter
- Error handling with user-friendly messages
- Success state management with automatic cleanup

#### Form Validation
- Uses Formik + Yup for robust validation
- Real-time validation feedback
- Proper error display

#### UI/UX Best Practices
- **Consistent Design**: Uses ThemedTextInput with proper borders and styling
- **Proper Spacing**: Follows app's spacing system (SPACING constants)
- **Loading States**: Button text changes during API calls
- **Success/Error Feedback**: Alert dialogs with user-friendly messages
- **Form Validation**: Real-time validation with error display
- **Automatic Reset**: Forms clear after successful submission
- **Responsive Design**: Proper input heights and multiline support

#### API Integration
- Uses Axios for HTTP requests
- Proper error handling
- Environment-based API URL configuration
- Follows existing project patterns

## Usage

The Contact Us and Stay Updated sections are automatically displayed at the bottom of the home page. Users can:

1. **Contact Us**: Fill out the form to send inquiries
2. **Stay Updated**: Subscribe to newsletter with email

Both forms provide immediate feedback and handle all edge cases gracefully.
