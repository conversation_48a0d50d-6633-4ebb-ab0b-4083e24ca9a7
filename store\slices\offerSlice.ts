import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

// Types
export interface Offer {
  user_id: number;
  name: string;
  profile_picture: string;
  make_offer: number;
  offered_at: string;
}

export interface MakeOfferRequest {
  id: number;
  type: string;
  make_offer: number;
}

export interface MakeOfferResponse {
  status: boolean;
  message: string;
  data: Offer[];
}

interface OfferState {
  offers: Offer[];
  loading: boolean;
  error: string | null;
  submitting: boolean;
  submitSuccess: boolean;
}

const initialState: OfferState = {
  offers: [],
  loading: false,
  error: null,
  submitting: false,
  submitSuccess: false,
};

// Helper function to get auth token
const getAuthToken = async (): Promise<string | null> => {
  try {
    const AsyncStorage =
      require("@react-native-async-storage/async-storage").default;
    return await AsyncStorage.getItem("token");
  } catch (error) {
    console.error("Error getting auth token:", error);
    return null;
  }
};

// Async thunk for making an offer
export const makeOffer = createAsyncThunk<
  MakeOfferResponse,
  MakeOfferRequest,
  { rejectValue: string }
>("offer/makeOffer", async (offerData, { rejectWithValue }) => {
  try {
    const token = await getAuthToken();
    if (!token) {
      return rejectWithValue("Authentication required");
    }

    const response = await axios.post(`${API_BASE_URL}/make/offer`, offerData, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (response.data.status) {
      return response.data;
    }

    return rejectWithValue(response.data.message || "Failed to make offer");
  } catch (error: any) {
    return rejectWithValue(
      error.response?.data?.message || "Failed to make offer"
    );
  }
});

// Offer slice
const offerSlice = createSlice({
  name: "offer",
  initialState,
  reducers: {
    clearOfferState: (state) => {
      state.error = null;
      state.submitSuccess = false;
    },
    resetOffers: (state) => {
      state.offers = [];
      state.error = null;
      state.submitSuccess = false;
    },
    setOffers: (state, action) => {
      state.offers = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Make offer cases
      .addCase(makeOffer.pending, (state) => {
        state.submitting = true;
        state.error = null;
        state.submitSuccess = false;
      })
      .addCase(makeOffer.fulfilled, (state, action) => {
        state.submitting = false;
        state.submitSuccess = true;
        // Update offers with the response data
        state.offers = action.payload.data;
        state.error = null;
      })
      .addCase(makeOffer.rejected, (state, action) => {
        state.submitting = false;
        state.submitSuccess = false;
        state.error = action.payload || "Failed to make offer";
      });
  },
});

export const { clearOfferState, resetOffers, setOffers } = offerSlice.actions;
export default offerSlice.reducer;
