import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Platform,
  Animated,
  Dimensions,
  PanResponder,
  TouchableWithoutFeedback,
  Easing,
  KeyboardAvoidingView,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store/store';
import { FilterParams, setFilters, resetFilters, filterVehicles, defaultFilters } from '@/store/slices/filterSlice';
import { COLORS, SPACING, FONTS, BORDERS, SHADOWS } from '@/constants/theme';
import VehicleTypeFilter from './VehicleTypeFilter';
import BrandFilter from './BrandFilter';
import FuelTypeFilter from './FuelTypeFilter';
import TransmissionFilter from './TransmissionFilter';
import OwnershipFilter from './OwnershipFilter';
import LocationFilterWithAPI from './LocationFilterWithAPI';
import PriceRangeSlider from './PriceRangeSlider';
import YearRangeSlider from './YearRangeSlider';
import KilometersRangeSlider from './KilometersRangeSlider';

interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
}

const FilterModal: React.FC<FilterModalProps> = ({ visible, onClose }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { filters } = useSelector((state: RootState) => state.filter);
  const { height } = Dimensions.get('window');

  // Local state to track filter changes before applying
  const [localFilters, setLocalFilters] = useState<FilterParams>(filters);
  const [selectedStateIds, setSelectedStateIds] = useState<string[]>([]);
  const [modalVisible, setModalVisible] = useState(false);

  // Animation values
  const slideAnim = useRef(new Animated.Value(height)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;

  // Reset animation values when component mounts or when visibility changes
  useEffect(() => {
    if (!visible) {
      slideAnim.setValue(height);
      backdropOpacity.setValue(0);
    }
  }, [visible]);

  // Configure pan responder for swipe-down to close
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Only respond to downward gestures
        return gestureState.dy > 5;
      },
      onPanResponderMove: (_, gestureState) => {
        if (gestureState.dy > 0) {
          // Only allow downward movement
          slideAnim.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dy > 100 || gestureState.vy > 0.5) {
          // If dragged down more than 100 units or with high velocity, close the modal
          closeModal();
        } else {
          // Otherwise, snap back to open position
          Animated.spring(slideAnim, {
            toValue: 0,
            useNativeDriver: true,
            tension: 50,
            friction: 7,
          }).start();
        }
      },
    })
  ).current;

  // Update local filters when redux filters change
  useEffect(() => {
    setLocalFilters({
      ...defaultFilters,
      ...filters
    });

    // Reset location state if filters are reset
    if (!filters.state || filters.state.length === 0) {
      setSelectedStateIds([]);
    }
  }, [filters]);

  // Handle modal visibility changes
  useEffect(() => {
    if (visible) {
      // First set the modal to visible
      setModalVisible(true);
      // Then start the animations
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0.5,
          duration: 300,
          easing: Easing.out(Easing.cubic),
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // If the modal should be hidden and it's currently visible
      if (modalVisible) {
        // Start the closing animations
        Animated.parallel([
          Animated.timing(slideAnim, {
            toValue: height,
            duration: 300,
            easing: Easing.in(Easing.cubic),
            useNativeDriver: true,
          }),
          Animated.timing(backdropOpacity, {
            toValue: 0,
            duration: 250,
            easing: Easing.in(Easing.cubic),
            useNativeDriver: true,
          }),
        ]).start(() => {
          // After animations complete, set modal to invisible
          setModalVisible(false);
        });
      }
    }
  }, [visible, slideAnim, backdropOpacity, modalVisible, height]);

  // Function to close the modal with animation
  const closeModal = () => {
    // Just call the onClose prop which will trigger the useEffect
    onClose();
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof FilterParams, value: any) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Handle location changes
  const handleStateChange = (stateIds: string[], stateNames: string[]) => {
    setSelectedStateIds(stateIds);

    // Update filter state with state IDs as numbers
    if (stateIds.length > 0) {
      const stateIdsAsNumbers = stateIds.map(id => parseInt(id, 10));
      handleFilterChange('state', stateIdsAsNumbers);
    } else {
      handleFilterChange('state', []);
    }

    // Clear cities since we removed the cities dropdown
    handleFilterChange('city', []);
  };

  // We can keep handleCityChange for now to avoid breaking other code,
  // but it won't be used anymore

  // Apply filters
  const applyFilters = () => {
    // Use the current local filters which already include default values
    dispatch(setFilters(localFilters));
    dispatch(filterVehicles(localFilters));
    closeModal();
  };

  // Reset filters
  const handleResetFilters = () => {
    dispatch(resetFilters());
    setLocalFilters({
      ...defaultFilters,
      vehicle_type: ['car'],
      brand: [],
      fuel_type: [],
      transmission: [],
      owners: [],
      state: []
    });

    // Reset location state
    setSelectedStateIds([]);
  };

  return (
    <Modal
      visible={visible || modalVisible}
      transparent={true}
      onRequestClose={closeModal}
      animationType="none"
      statusBarTranslucent={true}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.modalContainer}
      >
        {/* Backdrop with animated opacity */}
        <TouchableWithoutFeedback onPress={closeModal}>
          <Animated.View
            style={[
              styles.backdrop,
              { opacity: backdropOpacity }
            ]}
          />
        </TouchableWithoutFeedback>

        {/* Animated modal content */}
        <Animated.View
          style={[
            styles.modalContent,
            {
              transform: [{ translateY: slideAnim }],
              opacity: visible || modalVisible ? 1 : 0,
            },
          ]}
        >
          {/* Drag handle for swipe down */}
          <View {...panResponder.panHandlers} style={styles.dragHandle}>
            <View style={styles.dragIndicator} />
          </View>

          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={closeModal} style={styles.closeButton}>
              <Ionicons name="close" size={20} color={COLORS.text.primary} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Filters</Text>
            <TouchableOpacity onPress={handleResetFilters} style={styles.resetButton}>
              <Text style={styles.resetText}>Reset</Text>
            </TouchableOpacity>
          </View>

          {/* Main Content Area with ScrollView */}
          <View style={styles.contentContainer}>
            <ScrollView
              style={styles.scrollView}
              contentContainerStyle={styles.scrollViewContent}
              showsVerticalScrollIndicator={true}
              bounces={true}
              alwaysBounceVertical={false}
              scrollEnabled={true}>

              {/* Vehicle Type Filter */}
              <VehicleTypeFilter
                selectedTypes={localFilters.vehicle_type || ['car']}
                onChange={(value) => handleFilterChange('vehicle_type', value)}
              />

              {/* Brand Filter */}
              <BrandFilter
                selectedBrands={localFilters.brand || []}
                vehicleType={localFilters.vehicle_type?.[0] || 'car'}
                onChange={(value) => handleFilterChange('brand', value)}
              />

              {/* Price Range Filter */}
              <PriceRangeSlider
                minPrice={localFilters.min_price}
                maxPrice={localFilters.max_price}
                onChange={(min, max) => {
                  handleFilterChange('min_price', min);
                  handleFilterChange('max_price', max);
                }}
                onValueChange={(min, max) => {
                  handleFilterChange('min_price', min);
                  handleFilterChange('max_price', max);
                }}
              />

              {/* Year Range Filter */}
              <YearRangeSlider
                minYear={localFilters.min_year}
                maxYear={localFilters.max_year}
                onChange={(min, max) => {
                  handleFilterChange('min_year', min);
                  handleFilterChange('max_year', max);
                }}
                onValueChange={(min, max) => {
                  handleFilterChange('min_year', min);
                  handleFilterChange('max_year', max);
                }}
              />

              {/* Kilometers Range Filter */}
              <KilometersRangeSlider
                minKm={localFilters.min_km}
                maxKm={localFilters.max_km}
                onChange={(min, max) => {
                  handleFilterChange('min_km', min);
                  handleFilterChange('max_km', max);
                }}
                onValueChange={(min, max) => {
                  handleFilterChange('min_km', min);
                  handleFilterChange('max_km', max);
                }}
              />

              {/* Fuel Type Filter */}
              <FuelTypeFilter
                selectedFuelTypes={localFilters.fuel_type || []}
                onChange={(value) => handleFilterChange('fuel_type', value)}
              />

              {/* Transmission Filter - only show for car and commercial vehicles
                  Bikes and scooters don't have transmission options, so we hide this filter
                  when those vehicle types are selected. Previously selected transmission
                  values are preserved in the state but not displayed. */}
              {(() => {
                const selectedVehicleType = localFilters.vehicle_type?.[0] || 'car';
                const showTransmissionFilter = selectedVehicleType === 'car' || selectedVehicleType === 'commercial';

                return showTransmissionFilter && (
                  <TransmissionFilter
                    selectedTransmissions={localFilters.transmission || []}
                    onChange={(value) => handleFilterChange('transmission', value)}
                  />
                );
              })()}

              {/* Ownership Filter */}
              <OwnershipFilter
                selectedOwners={localFilters.owners || []}
                onChange={(value) => handleFilterChange('owners', value)}
              />

              {/* Location Filter with API */}
              <LocationFilterWithAPI
                selectedStates={selectedStateIds}
                onStateChange={handleStateChange}
              />
            </ScrollView>
          </View>

          {/* Footer with Apply Button */}
          <SafeAreaView style={styles.footer}>
            <TouchableOpacity style={styles.applyButton} onPress={applyFilters}>
              <Text style={styles.applyButtonText}>Apply Filters</Text>
            </TouchableOpacity>
          </SafeAreaView>
        </Animated.View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'transparent',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: COLORS.black,
  },
  modalContent: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: BORDERS.radius.lg,
    borderTopRightRadius: BORDERS.radius.lg,
    height: '80%', // Fixed height instead of min/max
    overflow: 'hidden',
  },
  dragHandle: {
    width: '100%',
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: SPACING.xs,
  },
  dragIndicator: {
    width: 40,
    height: 5,
    borderRadius: 5,
    backgroundColor: COLORS.border.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border.primary,
    backgroundColor: COLORS.white,
    ...SHADOWS.sm,
  },
  headerTitle: {
    fontWeight: '600',
    fontSize: FONTS.size.h2,
    color: COLORS.text.primary,
    letterSpacing: FONTS.letterSpacing.wide,
  },
  closeButton: {
    padding: SPACING.sm,
    backgroundColor: COLORS.surface.secondary,
    borderRadius: BORDERS.radius.full,
    width: 42,
    height: 42,
    alignItems: 'center',
    justifyContent: 'center',
  },
  resetButton: {
    padding: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: BORDERS.radius.md,
    borderWidth: 1,
    borderColor: COLORS.secondary,
  },
  resetText: {
    color: COLORS.secondary,
    fontWeight: '600',
    fontSize: FONTS.size.caption,
  },
  contentContainer: {
    flex: 1,
    position: 'relative',
  },
  scrollView: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollViewContent: {
    padding: SPACING.md,
    paddingBottom: 20, // Reduced padding at the bottom
  },
  footer: {
    padding: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.border.primary,
    backgroundColor: COLORS.white,
    ...Platform.select({
      ios: {
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: -3 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  applyButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.md,
    borderRadius: BORDERS.radius.md,
    alignItems: 'center',
    ...SHADOWS.sm,
  },
  applyButtonText: {
    color: COLORS.white,
    fontWeight: '700',
    fontSize: FONTS.size.body,
    letterSpacing: 0.5,
  },
});

export default FilterModal;

