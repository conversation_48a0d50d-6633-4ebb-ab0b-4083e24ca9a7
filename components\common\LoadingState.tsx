import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING } from '@/constants/theme';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import LoadingIndicator from './LoadingIndicator';

interface LoadingStateProps {
    loading: boolean;
    error: string | null;
    children: React.ReactNode;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
    loading,
    error,
    children,
}) => {
    if (loading) {
        return <LoadingIndicator
            size={120}
            color1="#ff6b6b"
            color2="#4ecdc4"
        />;
    }

    if (error) {
        return (
            <View style={styles.container}>
                <ThemedText style={styles.errorText}>{error}</ThemedText>
            </View>
        );
    }

    return <>{children}</>;
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: SPACING.lg,
    },
    errorText: {
        color: COLORS.primary,
        textAlign: 'center',
    },
}); 