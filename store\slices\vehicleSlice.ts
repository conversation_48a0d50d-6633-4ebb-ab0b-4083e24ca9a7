import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

export interface Vehicle {
  id: number;
  title: string;
  brand: string;
  model: string;
  year: string;
  fuel_type: string;
  kilometers_driven: number;
  price: string;
  transmission: string | null;
  ownership: string;
  location: string;
  description: string;
  images: string[];
  primary_image: string;
  status: string;
  type: string;
  variant: string;
  insurance_type?: string;
}

export type VehicleType = "car" | "bike" | "scooter" | "commercial";

interface PaginationParams {
  type: VehicleType;
  page?: number;
  limit?: number;
  search?: string;
}

interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

interface VehicleState {
  vehicles: Vehicle[];
  loading: boolean;
  error: string | null;
  pagination: PaginationMeta;
  refreshing: boolean;
  selectedVehicleType: VehicleType;
}

const initialState: VehicleState = {
  vehicles: [],
  loading: false,
  error: null,
  refreshing: false,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
  },
  selectedVehicleType: "car",
};

export const fetchVehicles = createAsyncThunk<
  { data: Vehicle[]; pagination: PaginationMeta },
  PaginationParams,
  { rejectValue: string }
>(
  "vehicles/fetchVehicles",
  async ({ type, page = 1, limit = 10, search = "" }, { rejectWithValue }) => {
    try {
      const url = new URL(`${API_BASE_URL}/display/vehicals`);
      url.searchParams.append("type", type);
      url.searchParams.append("page", page.toString());
      url.searchParams.append("limit", limit.toString());

      if (search) {
        url.searchParams.append("search", search);
      }

      const response = await fetch(url.toString());

      // Check if response is ok
      if (!response.ok) {
        throw new Error(
          `Server responded with ${response.status}: ${response.statusText}`
        );
      }

      // Check content type to ensure it's JSON
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        throw new Error(
          `Expected JSON response but got ${
            contentType || "unknown content type"
          }`
        );
      }

      // Parse JSON with error handling
      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error("JSON Parse error:", parseError);
        throw new Error("Failed to parse server response as JSON");
      }

      if (!data.status) {
        throw new Error(data.message || "Failed to fetch vehicles");
      }

      return {
        data: data.data || [],
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(data.total / limit),
          totalItems: data.total,
          itemsPerPage: limit,
        },
      };
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Failed to fetch vehicles"
      );
    }
  }
);

const vehicleSlice = createSlice({
  name: "vehicles",
  initialState,
  reducers: {
    clearVehicles: (state) => {
      state.vehicles = [];
      state.pagination = initialState.pagination;
      state.loading = false;
      state.error = null;
      state.refreshing = false;
    },
    setRefreshing: (state, action) => {
      state.refreshing = action.payload;
    },
    setVehicleType: (state, action) => {
      state.selectedVehicleType = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchVehicles.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVehicles.fulfilled, (state, action) => {
        state.loading = false;
        state.refreshing = false;
        state.vehicles = action.payload.data;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchVehicles.rejected, (state, action) => {
        state.loading = false;
        state.refreshing = false;
        state.error = action.payload || "Failed to fetch vehicles";
      });
  },
});

export const { clearVehicles, setRefreshing, setVehicleType } =
  vehicleSlice.actions;
export default vehicleSlice.reducer;
