import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { User as FirebaseUser } from "firebase/auth";

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

interface RejectValue {
  message?: string;
  status: boolean;
}

interface User {
  name: string;
  email: string;
  phone: string;
  status?: string;
  id?: number;
  dob?: string;
  gender?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  profile_picture?: string;
}

interface AccountState {
  user: User | FirebaseUser | null;
  token: string | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  forgotPasswordLoading: boolean;
  verifyOtpLoading: boolean;
  resetPasswordLoading: boolean;
  currentUid: string | null;
  requiresPhone: boolean;
  userData: any;
  purchase: any[];
}

interface ErrorResponse {
  message: string;
  status: boolean;
}

const initialState: AccountState = {
  user: null,
  token: null,
  loading: true,
  error: null,
  isAuthenticated: false,
  forgotPasswordLoading: false,
  verifyOtpLoading: false,
  resetPasswordLoading: false,
  currentUid: null,
  requiresPhone: false,
  userData: null,
  purchase: [],
};

export const fetchUserData = createAsyncThunk(
  "account/fetchUserData",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: AccountState };
      const response = await axios.get(`${API_BASE_URL}/single-user`, {
        headers: {
          Authorization: `Bearer ${state.auth.token}`,
        },
      });
      if (response.data.status) {
        await AsyncStorage.setItem("user", JSON.stringify(response.data.user));
        return { user: response.data.user, purchase: response.data.purchase };
      }
      return rejectWithValue("Failed to fetch user data");
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch user data"
      );
    }
  }
);

// Update profile thunk
export const updateProfile = createAsyncThunk(
  "account/updateProfile",
  async (profileData: any, { rejectWithValue, getState }) => {
    try {
      // Get token from AsyncStorage
      const token = await AsyncStorage.getItem("token");

      // Create FormData for file upload and text fields
      const formData = new FormData();
      Object.keys(profileData).forEach((key) => {
        if (key !== "profile_picture" && profileData[key] !== undefined) {
          formData.append(key, profileData[key]);
        }
      });

      // Append image if updated
      if (
        profileData.profile_picture &&
        profileData.profile_picture.startsWith("file://")
      ) {
        const uriParts = profileData.profile_picture.split(".");
        const fileExtension = uriParts[uriParts.length - 1];
        const fileName = `profile_${Date.now()}.${fileExtension}`;
        formData.append("profile_picture", {
          uri: profileData.profile_picture,
          name: fileName,
          type: `image/${fileExtension === "jpg" ? "jpeg" : fileExtension}`,
        } as any);
      }

      // Make the API request
      const response = await axios.post(
        `${API_BASE_URL}/edit-profile`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "multipart/form-data",
          },
        }
      );

      if (response.data.status) {
        const { account } = getState() as { account: AccountState };
        const currentUserData = account.userData;
        // Merge updated fields with existing user data
        const updatedUserData = {
          ...currentUserData,
          ...profileData,
          profile_picture:
            response.data.data?.profile_picture ||
            profileData.profile_picture ||
            currentUserData?.profile_picture,
        };
        await AsyncStorage.setItem("user", JSON.stringify(updatedUserData));
        return {
          message: response.data.message,
          status: response.data.status,
          userData: updatedUserData,
        };
      }
      return rejectWithValue({
        message: response.data.message || "Profile update failed",
        status: false,
      });
    } catch (error: any) {
      return rejectWithValue({
        message: error.response?.data?.message || "Profile update failed",
        status: false,
      });
    }
  }
);

export const deleteAccount = createAsyncThunk(
  "account/deleteAccount",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: { token: string } };
      const response = await axios.post(
        `${API_BASE_URL}/delete-account`,
        null,
        {
          headers: {
            Authorization: `Bearer ${state.auth.token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.data.status) {
        // Clear local storage
        await AsyncStorage.removeItem("token");
        await AsyncStorage.removeItem("user");
        return response.data;
      }
      return rejectWithValue(
        response.data.message || "Failed to delete account"
      );
    } catch (error: any) {
      console.error("Delete account error:", error.response || error);
      return rejectWithValue(
        error.response?.data?.message ||
          error.message ||
          "Failed to delete account"
      );
    }
  }
);

const accountSlice = createSlice({
  name: "account",
  initialState,
  reducers: {
    resetAccountState: (state) => {
      state.error = null;
      state.loading = false;
      state.requiresPhone = false;
    },
    clearUserData: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(updateProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload.status) {
          // Update the stored userData with the merged profile information
          state.userData = action.payload.userData;
        }
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.loading = false;
        state.error =
          (action.payload as ErrorResponse)?.message || "Profile update failed";
      })
      .addCase(fetchUserData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserData.fulfilled, (state, action) => {
        state.loading = false;
        state.userData = action.payload.user;
        state.purchase = action.payload.purchase;
      })
      .addCase(fetchUserData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete account cases
      .addCase(deleteAccount.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteAccount.fulfilled, (state) => {
        state.loading = false;
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.userData = null;
      })
      .addCase(deleteAccount.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { resetAccountState, clearUserData } = accountSlice.actions;
export default accountSlice.reducer;
