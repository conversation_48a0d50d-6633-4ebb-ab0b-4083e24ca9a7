import { ManageVehicle, ManageVehiclesResponse } from "@/types/manageVehicle";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

interface ManageVehiclesState {
  vehicles: ManageVehicle[];
  loading: boolean;
  error: string | null;
  selectedType: string;
}

export const listManageVehicles = createAsyncThunk(
  "manageVehicles/list",
  async (
    { type, search = "" }: { type: string; search?: string },
    { getState, rejectWithValue }
  ) => {
    const state = getState() as { auth: { token: string } };
    try {
      const response = await axios.get(
        `${API_BASE_URL}/list-vehicles?type=${type}&search=${search}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${state.auth.token}`,
          },
        }
      );

      if (response?.data?.status) {
        return response?.data?.data;
      }
      return rejectWithValue(response?.data?.message);
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to fetch vehicles"
      );
    }
  }
);

export const updateVehicleStatus = createAsyncThunk(
  "manageVehicles/updateStatus",
  async (
    {
      vehicleId,
      status,
      vehicleType,
    }: {
      vehicleId: number;
      status: boolean;
      vehicleType: string;
    },
    { getState, rejectWithValue }
  ) => {
    const state = getState() as { auth: { token: string } };
    try {
      const response = await axios.post(
        `${API_BASE_URL}/update-status-vehicles`,
        {
          status,
          vehicle_id: vehicleId.toString(),
          vehicle_type: vehicleType,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${state.auth.token}`,
          },
        }
      );

      if (response.data.status) {
        return response.data;
      }
      return rejectWithValue(response.data.message);
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to update vehicle status"
      );
    }
  }
);

const manageVehiclesSlice = createSlice({
  name: "manageVehicles",
  initialState: {
    vehicles: [],
    loading: false,
    error: null,
    selectedType: "car",
  } as ManageVehiclesState,
  reducers: {
    setSelectedType: (state, action) => {
      state.selectedType = action.payload;
    },
    clearManageVehicles: (state) => {
      state.vehicles = [];
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(listManageVehicles.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(listManageVehicles.fulfilled, (state, action) => {
        state.loading = false;
        state.vehicles = action.payload;
      })
      .addCase(listManageVehicles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(updateVehicleStatus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateVehicleStatus.fulfilled, (state, action) => {
        state.loading = false;
        const updatedVehicle = action.payload.vehicle;
        state.vehicles = state.vehicles.map((vehicle) =>
          vehicle.id === updatedVehicle.id ? updatedVehicle : vehicle
        );
      })
      .addCase(updateVehicleStatus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setSelectedType, clearManageVehicles } =
  manageVehiclesSlice.actions;
export default manageVehiclesSlice.reducer;
