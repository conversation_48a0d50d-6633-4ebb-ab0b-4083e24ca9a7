import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/common/ThemedText';
import ThemedInput from '@/components/common/ThemedInput';
import ThemedButton from '@/components/common/ThemedButton';
import { COLORS } from '@/constants/theme';

export default function ForgotPasswordForm({ email, onSendOtpSuccess, loading }: { email: string, onSendOtpSuccess: () => void, loading: boolean }) {
    return (
        <>
            <ThemedText style={styles.title}>Forgot Password</ThemedText>
            <ThemedInput
                value={email}
                editable={false}
                placeholder="Email"
                onChangeText={() => { }}
            />
            <ThemedButton
                style={styles.button}
                onPress={onSendOtpSuccess}
                disabled={loading}
            >
                {loading ? 'Sending OTP...' : 'Send OTP'}
            </ThemedButton>
        </>
    );
}

const styles = StyleSheet.create({
    title: {
        fontSize: 24,
        marginBottom: 16,
        textAlign: 'center',
        color: COLORS.text.primary,
    },
    button: {
        width: '100%',
        marginTop: 8,
    },
});