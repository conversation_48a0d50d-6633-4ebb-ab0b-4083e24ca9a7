import React, { useEffect, useState } from 'react';
import { View, TouchableOpacity, StyleSheet, SafeAreaView, StatusBar, ScrollView, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import Constants from 'expo-constants';
import { COLORS } from '@/constants/theme';
import { ThemedText } from '@/components/common/ThemedText';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { fetchPackages, applyReferralCode, clearDiscount, purchasePackage } from '@/store/slices/packagesSlice';
import LoadingIndicator from '@/components/common/LoadingIndicator';
import PackageCard from '@/components/account/PackageCard'; // Import the updated component

interface ProcessingPackages {
    [key: number]: boolean;
}

interface ReferralCodes {
    [key: string]: string;
}

interface ReferralErrors {
    [key: string]: string;
}

export default function BuyPackageScreen() {
    const dispatch = useAppDispatch();
    const {
        packages,
        loading,
        appliedDiscounts,
    } = useAppSelector((state) => state.packages);

    // Track applying code state per package instead of globally
    const [processingPackages, setProcessingPackages] = useState<ProcessingPackages>({});
    const [referralCodes, setReferralCodes] = useState<ReferralCodes>({});
    const [referralErrors, setReferralErrors] = useState<ReferralErrors>({});



    useEffect(() => {
        dispatch(fetchPackages());
    }, [dispatch]);

    const handleApplyReferral = async (packageId: number) => {
        const code: string = referralCodes[packageId]?.trim();
        if (!code) {
            setReferralErrors((prev: ReferralErrors) => ({ ...prev, [packageId]: 'Please enter a referral code' }));
            return;
        }

        try {
            // Set processing state for this specific package
            setProcessingPackages((prev: ProcessingPackages) => ({ ...prev, [packageId]: true }));

            await dispatch(applyReferralCode({ code, package_id: Number(packageId) })).unwrap();

            // Clear any previous errors on success
            setReferralErrors((prev: ReferralErrors) => ({ ...prev, [packageId]: '' }));
        } catch (error) {
            // Set the error message
            setReferralErrors((prev: ReferralErrors) => ({ ...prev, [packageId]: (error as any).toString() }));
        } finally {
            // Clear processing state for this package when done
            setProcessingPackages((prev: ProcessingPackages) => ({ ...prev, [packageId]: false }));
        }
    };

    const handleClearReferral = (packageId: number) => {
        dispatch(clearDiscount(packageId));
        setReferralCodes((prev) => ({ ...prev, [packageId]: '' }));
        setReferralErrors((prev) => ({ ...prev, [packageId]: '' }));
    };

    const handleReferralCodeChange = (packageId: number, text: any) => {
        setReferralCodes((prev) => ({ ...prev, [packageId]: text }));

        // Clear error when user starts typing again
        if (referralErrors[packageId]) {
            setReferralErrors((prev) => ({ ...prev, [packageId]: '' }));
        }

        // Clear discount if one was already applied
        if (appliedDiscounts[packageId]) {
            dispatch(clearDiscount(packageId));
        }
    };

    // Handle package purchase
    const handlePurchasePackage = async (purchaseDetails: {
        package: { id: number; name: string };
        finalAmount: number;
        paymentMethod: string;
        paymentId?: string;
    }) => {
        try {
            const packageId = purchaseDetails.package.id;
            const appliedDiscount = appliedDiscounts[packageId];

            const result = await dispatch(purchasePackage({
                package_id: packageId,
                payment_method: purchaseDetails.paymentMethod as 'online',
                code: appliedDiscount ? referralCodes[packageId] : undefined,
                payment_id: purchaseDetails.paymentId
            })).unwrap();

            if (result.status) {
                // Show success message
                Alert.alert(
                    "Success",
                    result.message,
                    [
                        {
                            text: "View Packages",
                            onPress: () => {
                                router.replace('/(auth)/(tabs)/account');
                            }
                        }
                    ]
                );
            }
        } catch (error: any) {
            console.error("Purchase error:", error);

            // Show a more detailed error message
            Alert.alert(
                "Purchase Failed",
                error?.message || "Failed to complete the purchase. Please try again.",
                [
                    {
                        text: "OK",
                        style: "cancel"
                    }
                ]
            );
        }
    };

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <LoadingIndicator size={120} color1="#ff6b6b" color2="#4ecdc4" />
            </View>
        );
    }

    // if (error) {
    //     return (
    //         <View style={[styles.container, styles.centerContent]}>
    //             <Text style={styles.errorText}>Failed to load packages. Please try again.</Text>
    //             <TouchableOpacity
    //                 style={styles.getPackageButton}
    //                 onPress={() => dispatch(fetchPackages())}
    //             >
    //                 <Text style={styles.getPackageButtonText}>Retry</Text>
    //             </TouchableOpacity>
    //         </View>
    //     );
    // }

    return (
        <SafeAreaView style={styles.container}>
            <StatusBar barStyle="light-content" backgroundColor="#1E2A47" />

            <View style={styles.header}>
                <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
                    <Ionicons name="arrow-back" size={24} color={COLORS.white} />
                </TouchableOpacity>
                <ThemedText style={styles.headerTitle}>Buy Package</ThemedText>
                <View style={{ width: 24 }} />
            </View>

            <ScrollView style={styles.content}>
                {packages.map((pkg) => (
                    <PackageCard
                        key={pkg.id}
                        pkg={pkg}
                        appliedDiscount={appliedDiscounts[pkg.id]}
                        referralCode={referralCodes[pkg.id] || ''}
                        referralError={referralErrors[pkg.id] || ''}
                        isProcessing={processingPackages[pkg.id] || false}
                        onReferralCodeChange={(text: any) => handleReferralCodeChange(pkg.id, text)}
                        onApplyReferral={() => handleApplyReferral(pkg.id)}
                        onClearReferral={() => handleClearReferral(pkg.id)}
                        onPurchasePackage={handlePurchasePackage}
                    />
                ))}
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingTop: 16 + (Constants.statusBarHeight || 0),
        backgroundColor: '#1E2A47',
        paddingHorizontal: 16,
        paddingVertical: 14,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    backButton: {
        marginRight: 16,
    },
    headerTitle: {
        color: 'white',
        fontSize: 18,
        fontWeight: '500',
    },
    content: {
        flex: 1,
        padding: 16,
    },
    centerContent: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    errorText: {
        color: COLORS.error,
        fontSize: 16,
        textAlign: 'center',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: COLORS.background,
    },
    getPackageButton: {
        backgroundColor: '#1E2A47',
        padding: 12,
        borderRadius: 4,
        alignItems: 'center',
        marginTop: 16,
        width: 150,
    },
    getPackageButtonText: {
        color: 'white',
        fontWeight: '500',
        fontSize: 16,
    },
});