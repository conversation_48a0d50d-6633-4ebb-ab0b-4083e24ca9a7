import React, { ReactNode } from 'react';
import { View, StyleSheet, Animated, Easing, ViewStyle } from 'react-native';
import { SPACING } from '@/constants/theme';

interface SkeletonLoaderProps {
    children: ReactNode;
    style?: ViewStyle;
}

export const SkeletonLoader = ({ children, style }: SkeletonLoaderProps) => {
    const opacityValue = React.useRef(new Animated.Value(0.3)).current;

    React.useEffect(() => {
        const animation = Animated.loop(
            Animated.sequence([
                Animated.timing(opacityValue, {
                    toValue: 0.8,
                    duration: 800,
                    easing: Easing.ease,
                    useNativeDriver: true,
                }),
                Animated.timing(opacityValue, {
                    toValue: 0.3,
                    duration: 800,
                    easing: Easing.ease,
                    useNativeDriver: true,
                }),
            ])
        );

        animation.start();
        return () => animation.stop();
    }, [opacityValue]);

    return (
        <Animated.View
            style={[
                style,
                { opacity: opacityValue }
            ]}
        >
            {children}
        </Animated.View>
    );
};

// Vehicle card skeleton
export const VehicleCardSkeleton = () => {
    return (
        <View style={styles.vehicleCard}>
            {/* Image Skeleton */}
            <SkeletonLoader style={styles.imageSkeleton}>
                <View style={styles.skeletonBackground} />
            </SkeletonLoader>

            <View style={styles.detailsContainer}>
                {/* Title Skeleton */}
                <SkeletonLoader style={styles.titleSkeleton}>
                    <View style={styles.skeletonBackground} />
                </SkeletonLoader>

                {/* Model Skeleton */}
                <SkeletonLoader style={styles.modelSkeleton}>
                    <View style={styles.skeletonBackground} />
                </SkeletonLoader>

                {/* Price Skeleton */}
                <SkeletonLoader style={styles.priceSkeleton}>
                    <View style={styles.skeletonBackground} />
                </SkeletonLoader>

                {/* Actions Skeleton */}
                <View style={styles.actionsContainer}>
                    <View style={styles.contactButtonsContainer}>
                        <SkeletonLoader style={styles.contactButtonSkeleton}>
                            <View style={styles.skeletonBackground} />
                        </SkeletonLoader>
                        <SkeletonLoader style={styles.contactButtonSkeleton}>
                            <View style={styles.skeletonBackground} />
                        </SkeletonLoader>
                    </View>

                    <View style={styles.toggleButtonsContainer}>
                        <SkeletonLoader style={styles.toggleButtonSkeleton}>
                            <View style={styles.skeletonBackground} />
                        </SkeletonLoader>
                        <SkeletonLoader style={styles.toggleButtonSkeleton}>
                            <View style={styles.skeletonBackground} />
                        </SkeletonLoader>
                    </View>
                </View>
            </View>
        </View>
    );
};

// Generate multiple skeletons for the list
export const VehicleSkeletonList = ({ count = 3 }) => {
    return Array.from({ length: count }).map((_, index) => (
        <VehicleCardSkeleton key={`skeleton-${index}`} />
    ));
};

const styles = StyleSheet.create({
    vehicleCard: {
        backgroundColor: '#fff',
        borderRadius: 12,
        marginBottom: SPACING.lg,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.12,
        shadowRadius: 6,
        elevation: 3,
        overflow: 'hidden',
    },
    skeletonBackground: {
        flex: 1,
        backgroundColor: '#E0E0E0',
    },
    imageSkeleton: {
        width: '100%',
        height: 180,
    },
    detailsContainer: {
        padding: SPACING.md,
    },
    titleSkeleton: {
        height: 24,
        width: '70%',
        borderRadius: 4,
        marginBottom: 8,
    },
    modelSkeleton: {
        height: 16,
        width: '50%',
        borderRadius: 4,
        marginBottom: 12,
    },
    priceSkeleton: {
        height: 20,
        width: '30%',
        borderRadius: 4,
        marginBottom: 16,
    },
    actionsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    contactButtonsContainer: {
        flexDirection: 'row',
    },
    contactButtonSkeleton: {
        height: 36,
        width: 36,
        borderRadius: 6,
        marginRight: 10,
    },
    toggleButtonsContainer: {
        flexDirection: 'row',
    },
    toggleButtonSkeleton: {
        height: 36,
        width: 80,
        borderRadius: 8,
        marginLeft: 10,
    },
});